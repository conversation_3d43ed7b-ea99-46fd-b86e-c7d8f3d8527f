/**
 * 统一的角色物品背包数据访问层
 * 合并ItemRepository和InventoryRepository的功能
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 */

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import {
  Inventory,
  InventoryDocument,
  Item,
  InventoryTab,
  InventoryTabType,
  InventorySummary,
  ItemUseResult
} from '../schemas/inventory.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的角色物品背包数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 物品实例CRUD操作
 * - 背包页签管理
 * - 物品堆叠和映射
 * - 背包扩展和排序
 * - 物品使用和转换
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 * - 事务支持确保数据一致性
 */
@Injectable()
export class InventoryRepository extends BaseRepository<InventoryDocument> {
  constructor(
    @InjectModel(Inventory.name) inventoryModel: Model<InventoryDocument>
  ) {
    super(inventoryModel, 'InventoryRepository');
  }

  // ========== 基础CRUD方法 ==========

  /**
   * 根据角色ID查找角色物品数据
   * 使用BaseRepository的findOne方法优化性能
   */
  async findById(characterId: string): Promise<XResult<InventoryDocument | null>> {
    return this.findOne({ characterId: characterId });
  }

  /**
   * 根据角色ID查找角色物品数据（Lean查询优化版本）
   */
  async findByIdLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ characterId: characterId });
  }

  /**
   * 创建角色物品数据
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(characterData: Partial<Inventory>): Promise<XResult<InventoryDocument>> {
    const result = await this.createOne(characterData);
    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`角色物品数据创建成功: ${result.data.characterId}`);
    }
    return result;
  }

  /**
   * 更新角色物品数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateInventory(
    characterId: string,
    updateData: Partial<Inventory>,
    session?: ClientSession
  ): Promise<XResult<InventoryDocument | null>> {
    const result = await this.updateOne(
      { characterId: characterId },
      { ...updateData, updateTime: Date.now() },
      session
    );

    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`角色物品数据更新成功: ${characterId}`);
    }

    return result;
  }

  // ========== 物品管理方法 ==========

  /**
   * 添加物品到角色背包
   * 使用事务确保数据一致性
   */
  async addItem(
    characterId: string,
    configId: number,
    quantity: number,
    itemConfig?: any,
    externalSession?: ClientSession
  ): Promise<XResult<{ addedIds: string[]; slotsUsed: number }>> {
    const executeAddItem = async (session: ClientSession) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;

      // 检查背包空间
      if (itemConfig) {
        const slotsNeeded = inventory.addItemUseCount(configId, quantity, itemConfig);
        const availableSlots = this.getAvailableSlots(inventory);
        
        if (slotsNeeded > availableSlots) {
          return XResultUtils.error('背包空间不足', 'INSUFFICIENT_SLOTS');
        }
      }

      // 添加物品（支持堆叠）
      const addedIds = await inventory.addItemWithStacking(configId, quantity);

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`物品添加成功: 角色${characterId}, 物品${configId}, 数量${quantity}, 新增${addedIds.length}个实例`);

      return XResultUtils.ok({
        addedIds: addedIds,
        slotsUsed: addedIds.length
      });
    };

    // 如果有外部session，使用外部session；否则创建新的事务
    if (externalSession) {
      return executeAddItem(externalSession);
    } else {
      return this.withTransaction(executeAddItem);
    }
  }

  /**
   * 从角色背包移除物品
   * 使用事务确保数据一致性
   */
  async removeItem(
    characterId: string,
    itemId: string,
    quantity?: number,
    externalSession?: ClientSession
  ): Promise<XResult<{ removedQuantity: number }>> {
    const executeRemoveItem = async (session: ClientSession) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      const item = inventory.getItem(itemId);
      
      if (!item) {
        return XResultUtils.error('物品不存在', 'ITEM_NOT_FOUND');
      }

      let removedQuantity = 0;

      if (!quantity || quantity >= item.quantity) {
        // 完全移除物品
        removedQuantity = item.quantity;
        inventory.removeItem(itemId);
        inventory.removeFromConfigMapping(item.configId, itemId);
        inventory.removeItemFromTabMapping(itemId);
      } else {
        // 部分移除
        removedQuantity = quantity;
        item.quantity -= quantity;
      }

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`物品移除成功: 角色${characterId}, 物品${itemId}, 移除数量${removedQuantity}`);

      return XResultUtils.ok({
        removedQuantity: removedQuantity
      });
    };

    // 如果有外部session，使用外部session；否则创建新的事务
    if (externalSession) {
      return executeRemoveItem(externalSession);
    } else {
      return this.withTransaction(executeRemoveItem);
    }
  }

  /**
   * 根据配置ID扣除道具
   * 供内部调用方法使用，支持按配置ID批量扣除
   * 使用事务确保数据一致性
   *
   * @param characterId 角色ID
   * @param configId 道具配置ID
   * @param quantity 扣除数量
   * @returns 扣除结果
   */
  async deductItemByConfigId(
    characterId: string,
    configId: number,
    quantity: number,
    externalSession?: ClientSession
  ): Promise<XResult<{ deductedQuantity: number; affectedItems: string[] }>> {
    const executeDeductItem = async (session: ClientSession) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色背包不存在', 'INVENTORY_NOT_FOUND');
      }

      const inventory = inventoryResult.data;

      // 获取该配置ID的所有物品实例
      const itemIds = inventory.getItemsByConfigId(configId);
      if (!itemIds || itemIds.length === 0) {
        return XResultUtils.error(`没有找到配置ID为${configId}的道具`, 'ITEM_NOT_FOUND');
      }

      let remainingToDeduct = quantity;
      let totalDeducted = 0;
      const affectedItems: string[] = [];

      // 按物品实例逐个扣除
      for (const itemId of itemIds) {
        if (remainingToDeduct <= 0) break;

        const item = inventory.getItem(itemId);
        if (!item) continue;

        const deductFromThis = Math.min(item.quantity, remainingToDeduct);

        if (deductFromThis >= item.quantity) {
          // 完全移除这个物品实例
          totalDeducted += item.quantity;
          remainingToDeduct -= item.quantity;
          affectedItems.push(itemId);

          inventory.removeItem(itemId);
          inventory.removeFromConfigMapping(configId, itemId);
          inventory.removeItemFromTabMapping(itemId);
        } else {
          // 部分扣除
          item.quantity -= deductFromThis;
          totalDeducted += deductFromThis;
          remainingToDeduct -= deductFromThis;
          affectedItems.push(itemId);
        }
      }

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`按配置ID扣除道具成功: 角色${characterId}, 配置ID${configId}, 扣除数量${totalDeducted}, 影响物品${affectedItems.length}个`);

      return XResultUtils.ok({
        deductedQuantity: totalDeducted,
        affectedItems
      });
    };

    // 如果有外部session，使用外部session；否则创建新的事务
    if (externalSession) {
      return executeDeductItem(externalSession);
    } else {
      return this.withTransaction(executeDeductItem);
    }
  }

  /**
   * 移动物品到指定页签和位置
   * 使用事务确保数据一致性
   */
  async moveItem(
    characterId: string,
    itemId: string,
    tabId: number,
    slot?: number
  ): Promise<XResult<{ newSlot: number }>> {
    return this.withTransaction(async (session) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      const success = inventory.addToTab(tabId, itemId, slot);

      if (!success) {
        return XResultUtils.error('移动物品失败，可能是背包已满或位置被占用', 'MOVE_ITEM_FAILED');
      }

      const item = inventory.getItem(itemId);
      const newSlot = item ? item.slot : -1;

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`物品移动成功: 角色${characterId}, 物品${itemId}, 页签${tabId}, 位置${newSlot}`);

      return XResultUtils.ok({
        newSlot: newSlot
      });
    });
  }

  // ========== 背包管理方法 ==========

  /**
   * 获取角色背包列表（客户端数据）
   * 使用BaseRepository的缓存优化
   */
  async getInventorySummaries(characterId: string): Promise<XResult<InventorySummary[]>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    const bagList = inventoryResult.data.makeInventorySummaries();
    return XResultUtils.ok(bagList);
  }

  /**
   * 计算背包扩展费用
   * 不实际执行扩展，只计算费用
   */
  async calculateExpandCost(
    characterId: string,
    tabId: number,
    expandCount: number = 1
  ): Promise<XResult<number>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    const inventory = inventoryResult.data;
    const cost = inventory.calculateExpandCost(tabId, expandCount);
    return XResultUtils.ok(cost);
  }

  /**
   * 扩展背包页签
   * 使用事务确保数据一致性
   */
  async expandTab(
    characterId: string,
    tabId: number,
    expandCount: number = 1
  ): Promise<XResult<{ cost: number; newCapacity: number }>> {
    return this.withTransaction(async (session) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      const expandResult = inventory.expandTab(tabId, expandCount);

      if (!expandResult.success) {
        return XResultUtils.error('扩展背包失败，可能已达到最大容量', 'EXPAND_BAG_FAILED');
      }

      const tab = inventory.getOneTab(tabId);
      const newCapacity = tab ? tab.capacity : 0;

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`背包扩展成功: 角色${characterId}, 页签${tabId}, 扩展${expandCount}次, 费用${expandResult.cost}`);

      return XResultUtils.ok({
        cost: expandResult.cost,
        newCapacity: newCapacity
      });
    });
  }

  /**
   * 背包排序
   * 使用事务确保数据一致性
   */
  async sortTab(
    characterId: string,
    tabId: number,
    sortType: string = 'slot'
  ): Promise<XResult<void>> {
    return this.withTransaction(async (session) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      inventory.sortTab(tabId, sortType);

      // 保存更改
      await inventory.save({ session });

      this.logger.log(`背包排序成功: 角色${characterId}, 页签${tabId}, 排序类型${sortType}`);

      return XResultUtils.empty();
    });
  }

  // ========== 辅助方法 ==========

  /**
   * 计算角色可用的背包槽位数
   */
  private getAvailableSlots(inventory: InventoryDocument): number {
    let totalCapacity = 0;
    let usedSlots = 0;

    for (const tab of inventory.tabs) {
      totalCapacity += tab.capacity;
      usedSlots += inventory.items.filter(item => item.tabId === tab.id).length;
    }

    return totalCapacity - usedSlots;
  }

  // ========== 查询和统计方法 ==========

  /**
   * 根据配置ID查询角色物品数量
   * 使用BaseRepository的缓存优化
   */
  async getItemQuantity(characterId: string, configId: number): Promise<XResult<number>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    const quantity = inventoryResult.data.getItemQuantityByConfigId(configId);
    return XResultUtils.ok(quantity);
  }

  /**
   * 检查角色是否有足够的物品
   * 使用BaseRepository的缓存优化
   */
  async checkItemSufficient(characterId: string, configId: number, needQuantity: number): Promise<XResult<boolean>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    const sufficient = inventoryResult.data.checkItemIsEnough(configId, needQuantity);
    return XResultUtils.ok(sufficient);
  }

  /**
   * 获取角色的所有物品实例
   * 使用BaseRepository的findOne方法优化性能
   */
  async getItems(characterId: string): Promise<XResult<Item[]>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    return XResultUtils.ok(inventoryResult.data.items);
  }

  /**
   * 根据页签ID获取物品列表
   * 使用BaseRepository的缓存优化
   */
  async getItemsByTab(characterId: string, tabId: number): Promise<XResult<Item[]>> {
    const inventoryResult = await this.findById(characterId);
    if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
      return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    const items = inventoryResult.data.items.filter(item => item.tabId === tabId);
    return XResultUtils.ok(items);
  }

  /**
   * 清理过期物品
   * 使用事务确保数据一致性
   */
  async cleanExpiredItems(characterId: string): Promise<XResult<{ cleanedCount: number }>> {
    return this.withTransaction(async (session) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      const cleanedCount = inventory.cleanExpiredItems();

      if (cleanedCount > 0) {
        // 保存更改
        await inventory.save({ session });
        this.logger.log(`清理过期物品成功: 角色${characterId}, 清理${cleanedCount}个物品`);
      }

      return XResultUtils.ok({ cleanedCount });
    });
  }

  /**
   * 批量添加物品
   * 使用事务确保数据一致性，支持高效的批量操作
   */
  async batchAddItems(
    characterId: string,
    itemsToAdd: Array<{configId: number, quantity: number}>
  ): Promise<XResult<{ addedIds: string[] }>> {
    return this.withTransaction(async (session) => {
      const inventoryResult = await this.findById(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const inventory = inventoryResult.data;
      const allAddedIds: string[] = [];

      // 批量处理物品添加
      for (const {configId, quantity} of itemsToAdd) {
        try {
          // 使用Schema方法进行堆叠添加
          const addedIds = await inventory.addItemWithStacking(configId, quantity);
          allAddedIds.push(...addedIds);

          this.logger.debug(`批量添加单项成功: 角色${characterId}, 物品${configId}, 数量${quantity}, 实例数${addedIds.length}`);
        } catch (error) {
          this.logger.error(`批量添加单项失败: 角色${characterId}, 物品${configId}, 数量${quantity}`, error);
          // 继续处理其他物品，不中断整个批量操作
        }
      }

      // 保存所有更改
      await inventory.save({ session });

      this.logger.log(`批量添加物品成功: 角色${characterId}, 添加${itemsToAdd.length}种物品, 共${allAddedIds.length}个实例`);

      return XResultUtils.ok({
        addedIds: allAddedIds
      });
    });
  }

  /**
   * 初始化角色背包数据
   * 创建默认的页签配置
   */
  async initializeInventory(characterId: string): Promise<XResult<InventoryDocument>> {
    // 默认页签配置
    const defaultTabs: InventoryTab[] = [
      {
        id: 1,
        name: '装备',
        type: InventoryTabType.EQUIPMENT,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 2,
        name: '消耗品',
        type: InventoryTabType.CONSUMABLE,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 3,
        name: '材料',
        type: InventoryTabType.MATERIAL,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 4,
        name: '卡片',
        type: InventoryTabType.CARD,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      }
    ];

    const data: Partial<Inventory> = {
      characterId: characterId,
      items: [],
      configToItems: {},
      tabs: defaultTabs,
      itemToTab: [],
      lastActiveTime: Date.now(),
      createTime: Date.now(),
      updateTime: Date.now()
    };

    return this.create(data);
  }

  /**
   * 初始化角色背包数据（支持事务）
   * 创建默认的页签配置
   *
   * @param characterId 角色ID
   * @param session 数据库事务session
   */
  async initializeInventoryWithSession(characterId: string, session: any): Promise<XResult<InventoryDocument>> {
    // 默认页签配置
    const defaultTabs: InventoryTab[] = [
      {
        id: 1,
        name: '装备',
        type: InventoryTabType.EQUIPMENT,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 2,
        name: '消耗品',
        type: InventoryTabType.CONSUMABLE,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 3,
        name: '材料',
        type: InventoryTabType.MATERIAL,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      },
      {
        id: 4,
        name: '卡片',
        type: InventoryTabType.CARD,
        capacity: 50,
        expandCount: 0,
        nextExpandCost: 1000,
        createTime: Date.now(),
        updateTime: Date.now()
      }
    ];

    const data: Partial<Inventory> = {
      characterId: characterId,
      items: [],
      configToItems: {},
      tabs: defaultTabs,
      itemToTab: [],
      lastActiveTime: Date.now(),
      createTime: Date.now(),
      updateTime: Date.now()
    };

    // 使用session创建背包数据
    const result = await this.createOne(data, session);
    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`角色背包数据创建成功(事务): ${result.data.characterId}`);
    }
    return result;
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加角色物品特定的验证规则
   */
  protected validateData(data: Partial<Inventory>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'UID_REQUIRED');
      }
    }

    // 验证物品实例数组
    if (data.items !== undefined) {
      if (!Array.isArray(data.items)) {
        return XResultUtils.error('物品列表必须为数组', 'INVALID_ITEMS_ARRAY');
      }

      for (const item of data.items) {
        if (!item.itemId || !item.configId || item.quantity <= 0) {
          return XResultUtils.error('物品实例数据不完整', 'INVALID_ITEM_INSTANCE');
        }
      }
    }

    // 验证页签数组
    if (data.tabs !== undefined) {
      if (!Array.isArray(data.tabs)) {
        return XResultUtils.error('页签列表必须为数组', 'INVALID_TABS_ARRAY');
      }

      for (const tab of data.tabs) {
        if (!tab.id || !tab.name || tab.capacity <= 0) {
          return XResultUtils.error('页签数据不完整', 'INVALID_TAB_DATA');
        }
      }
    }

    return XResultUtils.empty();
  }
}
