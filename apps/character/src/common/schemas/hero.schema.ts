/**
 * 球员实体Schema
 * 基于旧项目footballer.js迁移而来，适配新的微服务架构
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { HeroQuality } from '../types';
import { HeroPosition } from '@libs/game-constants';


// 属性加成详细结构 - 基于old项目的复杂属性系统
@Schema({ _id: false })
export class AttributeDetail {
  @Prop({ default: 0 })
  cur: number;              // 当前值 (最终计算值)

  @Prop({ default: 0 })
  base: number;             // 基础值 (培养值)

  @Prop({ default: 0 })
  skill: number;            // 技能附加值

  @Prop({ default: 0 })
  qualificate: number;      // 特训加成

  @Prop({ default: 0 })
  upgradeStar: number;      // 升星加成

  @Prop({ default: 0 })
  handbook: number;         // 图鉴加成

  @Prop({ default: 0 })
  skillAct: number;         // 技能加成系数

  @Prop({ default: 0 })
  trainer: number;          // 教练属性加成

  @Prop({ default: 0 })
  groundTrain: number;      // 球场训练加成

  @Prop({ default: 0 })
  attackTactics: number;    // 进攻战术加成

  @Prop({ default: 0 })
  defTactics: number;       // 防守战术加成

  @Prop({ default: 0 })
  trainerSkillAtt: number;  // 教练技能加成

  @Prop({ default: 0 })
  beliefSkillAtt: number;   // 信仰技能加成
}

// 球员完整属性系统 - 基于old项目ONE_LEVEL_ATTR_NAMES
@Schema({ _id: false })
export class HeroAttributes {
  // 基础身体属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  speed: AttributeDetail;              // 速度

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  jumping: AttributeDetail;            // 弹跳

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  strength: AttributeDetail;           // 力量

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  stamina: AttributeDetail;            // 耐力

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  explosiveForce: AttributeDetail;     // 爆发力

  // 技术属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  finishing: AttributeDetail;          // 射门

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  dribbling: AttributeDetail;          // 盘带

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  passing: AttributeDetail;            // 传球

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  longPassing: AttributeDetail;        // 长传

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  longShots: AttributeDetail;          // 远射

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  heading: AttributeDetail;            // 头球

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  volleys: AttributeDetail;            // 制空

  // 防守属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  standingTackle: AttributeDetail;     // 抢断

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  slidingTackle: AttributeDetail;      // 铲球

  // 定位球属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  penalties: AttributeDetail;          // 点球

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  cornerKick: AttributeDetail;         // 角球

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  freeKick: AttributeDetail;           // 任意球

  // 门将属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  attack: AttributeDetail;             // 出击

  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  save: AttributeDetail;               // 扑救

  // 特殊属性
  @Prop({ type: AttributeDetail, default: () => new AttributeDetail() })
  resistanceDamage: AttributeDetail;   // 抗伤性
}



// 球员技能子文档
// 基于old项目架构：球员技能只有激活功能，无升级系统
// old项目第849-863行：只有getHeroSkillInfo和activateHeroSkill功能
@Schema({ _id: false })
export class HeroSkill {
  @Prop({ required: true })
  skillId: number;      // 技能配置ID（对应old项目SkillList[k].skillId）

  @Prop({ default: 0, min: 0, max: 1 })
  isActivate: number;   // 是否激活：1=激活，0=未激活（完全对齐old项目第2446行）

  @Prop({ default: 1 })
  level: number;        // 技能等级
}

// 球员训练阶段信息
@Schema({ _id: false })
export class TrainingStageInfo {
  @Prop({ default: 1, min: 1, max: 9 })
  stage: number;        // 当前阶段 (1-9)

  @Prop({ default: 0 })
  stageProgress: number; // 阶段进度

  @Prop({ default: 0 })
  totalTrainingCount: number; // 该阶段总训练次数
}

// 球员训练信息子文档
@Schema({ _id: false })
export class HeroTraining {
  @Prop({ default: 0 })
  exp: number;          // 经验值

  @Prop({ default: 0 })
  trainingCount: number; // 总训练次数

  @Prop({ default: 0 })
  lastTrainingTime: number; // 最后训练时间

  @Prop({ default: 0 })
  trainingCooldown: number; // 训练冷却时间

  // 六大属性的阶段信息
  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  speedStage: TrainingStageInfo;

  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  shootingStage: TrainingStageInfo;

  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  passingStage: TrainingStageInfo;

  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  defendingStage: TrainingStageInfo;

  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  dribblingStage: TrainingStageInfo;

  @Prop({ type: TrainingStageInfo, default: () => ({ stage: 1, stageProgress: 0, totalTrainingCount: 0 }) })
  physicalityStage: TrainingStageInfo;

  // 特训数据
  @Prop({ type: Object, default: () => ({}) })
  additive: any;        // 特训加成值

  @Prop({ type: Object, default: () => ({}) })
  type1: any;           // 初级特训数据

  @Prop({ type: Object, default: () => ({}) })
  type2: any;           // 中级特训数据

  @Prop({ type: Object, default: () => ({}) })
  type3: any;           // 高级特训数据

  @Prop({ type: Object, default: () => ({}) })
  type4: any;           // 定向特训数据
}

// 球员升星信息子文档
@Schema({ _id: false })
export class HeroEvolution {
  @Prop({ default: 0, min: 0, max: 9 })
  star: number;         // 星级 (0-9)

  @Prop({ default: 0 })
  evolutionExp: number; // 升星经验

  @Prop({ default: 0 })
  evolutionCount: number; // 升星次数

  @Prop({ type: [String], default: [] })
  consumedHeroes: string[]; // 消耗的球员ID列表

  @Prop({ default: 0 })
  lastEvolutionTime: number; // 最后升星时间

  @Prop({ default: false })
  isMaxStar: boolean;   // 是否已达到最高星级
}

// 主球员Schema
@Schema({ 
  collection: 'heroes', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Hero {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  heroId: string;

  @Prop({ required: true, index: true })
  characterId: string;

  @Prop({ required: true })
  resId: number;        // 配置表ID (old: ResID)
  // 基础信息
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  position: HeroPosition;

  @Prop({ required: true })
  quality: HeroQuality;

  @Prop({ default: 1, min: 1, max: 100 })
  level: number;

  @Prop({ default: 0 })
  exp: number;

  @Prop({ default: 0 })
  experience: number;   // 经验值（训练系统使用）

  // 外观信息
  @Prop({ default: '' })
  avatar: string;

  @Prop({ default: 0 })
  faceIcon: number;

  @Prop({ default: '' })
  nationality: string;   // 国籍

  @Prop({ default: '' })
  club: string;         // 俱乐部

  // 属性信息 - 包含完整的加成结构
  @Prop({ type: HeroAttributes, default: () => new HeroAttributes() })
  attributes: HeroAttributes;

  // 技能信息
  @Prop({ type: [HeroSkill], default: [] })
  skills: HeroSkill[];

  @Prop({ type: [Number], default: [] })
  activeSkills: number[]; // 激活的技能ID列表

  // 训练信息
  @Prop({ type: HeroTraining, default: () => ({}) })
  training: HeroTraining;

  // 升星信息
  @Prop({ type: HeroEvolution, default: () => ({}) })
  evolution: HeroEvolution;

  // 突破系统
  @Prop({ type: [Number], default: [] })
  breakthrough: number[];   // 突破记录

  @Prop({ default: 30, min: 0, max: 100 })
  oldBreakOut: number;      // 突破最大值

  // 状态信息
  @Prop({ default: false })
  isLocked: boolean;    // 是否锁定

  @Prop({ default: false })
  isInLineup: boolean; // 是否在阵容中

  @Prop({ default: 0 })
  lineupPosition: number; // 阵容位置

  @Prop({ default: 0 })
  energy: number;       // 体力值

  @Prop({ default: 0 })
  morale: number;       // 士气值

  // 治疗状态
  @Prop({ default: false })
  isTreat: boolean;     // 是否在治疗中

  // 疲劳系统 (基于old项目)
  @Prop({ default: 0, min: 0, max: 100 })
  fatigue: number;      // 疲劳值 (old: Fatigue)

  @Prop({ default: 1.0, min: 0.1, max: 1.0 })
  fatigueRatio: number; // 疲劳衰减系数 (old: FatigueRatio)

  @Prop({ default: 0 })
  reTimeFatigue: number; // 恢复疲劳值时间 (old: ReTimeFatigue)

  // 健康状态系统 (基于old项目)
  @Prop({ default: 1 })
  health: number;       // 健康状态 1健康 2轻微伤 3轻伤 4重伤 (old: Health)

  @Prop({ default: 1 })
  status: number;       // 球员状态 (old: Status)

  @Prop({ default: 0 })
  threeDropNum: number; // 状态3连降级次数 (old: ThreeDropNum)

  @Prop({ default: 0 })
  tenDropNum: number;   // 状态10连未巅峰次数 (old: TenDropNum)

  // 装备信息
  @Prop({ type: [String], default: [] })
  equipments: string[]; // 装备ID列表

  // 合同信息
  @Prop({ default: 0 })
  contractDays: number; // 合同剩余天数

  @Prop({ default: 0 })
  treatyReTime: number; // 合约刷新时间

  @Prop({ type: Date })
  contractExpireTime: Date; // 合约到期时间

  @Prop({ default: 0 })
  contractRenewals: number; // 续约次数

  @Prop({ type: Date })
  lastRenewTime: Date; // 最后续约时间

  @Prop({ default: 0 })
  salary: number;       // 薪水

  @Prop({ default: 0 })
  lifeNum: number;      // 续约生涯次数

  // 药剂使用记录（基于old项目）
  @Prop({ default: false })
  isUseCareerMedicine: boolean; // 是否使用过青春永驻药剂

  // 战斗统计（基于old项目）
  @Prop({ default: 0 })
  battleNum: number;    // 战斗场数

  // 市场信息
  @Prop({ default: 0 })
  marketValue: number;  // 市场价值

  @Prop({ default: false })
  isOnMarket: boolean;  // 是否在转会市场

  @Prop({ default: 0 })
  marketPrice: number;  // 挂牌价格

  // 退役信息
  @Prop({ default: false })
  isRetired: boolean;   // 是否已退役

  @Prop({ default: 0 })
  retirementTime: number; // 退役时间

  @Prop({ default: '' })
  retirementStatus: string; // 退役状态 (pending, retired)

  @Prop({ type: Date })
  retirementProcessedTime: Date; // 退役处理时间

  // 养成相关字段（基于old项目）
  @Prop({ default: 0 })
  breakLevel: number; // 突破等级

  @Prop({ default: 0 })
  starLevel: number; // 星级

  @Prop({ default: 0 })
  evolutionStage: number; // 进化阶段

  // 培养系统
  @Prop({ type: [Number], default: [1, 1, 1, 1, 1, 1] })
  cultivateStages: number[]; // 培养阶段

  // 获得时间
  @Prop({ type: Date, default: Date.now })
  acquiredTime: Date; // 获得球员时间

  // 状态相关字段 - 移除重复定义，使用上面的status: number

  @Prop({ type: Date })
  lastStatusUpdateTime: Date; // 最后状态更新时间

  // 生涯统计
  @Prop({ default: 0 })
  careerDays: number; // 生涯天数

  @Prop({ default: 0 })
  statusPromotions: number; // 状态提升次数

  @Prop({ default: 0 })
  levelUps: number; // 升级次数

  @Prop({ default: 0 })
  skillUpgrades: number; // 技能升级次数

  @Prop({ default: 0 })
  breakthroughs: number; // 突破次数

  @Prop({ default: 1 })
  Health: number; // 健康状态（1健康,2轻微伤,3轻伤,4重伤）

  // 统计信息
  @Prop({ default: 0 })
  matchesPlayed: number; // 比赛场次

  @Prop({ default: 0 })
  goals: number;        // 进球数

  @Prop({ default: 0 })
  assists: number;      // 助攻数

  @Prop({ default: 0 })
  yellowCards: number;  // 黄牌数

  @Prop({ default: 0 })
  redCards: number;     // 红牌数

  @Prop({ default: 0 })
  averageRating: number; // 平均评分

  @Prop({ default: 30 })
  potential: number;    // 潜力值 (old: Potential, 技能解锁条件)

  @Prop({ default: 0 })
  totalPower: number;    // 总实力值

  @Prop({ type: Date })
  lastCalculateTime: Date; // 最后计算时间

  // 获得时间
  @Prop({ default: 0 })
  obtainTime: number;   // 获得时间

  @Prop({ default: 0 })
  obtainType: number;   // 获得方式（1:抽卡 2:购买 3:奖励等）

  // 虚拟字段：总评分 - 基于完整属性计算
  get overallRating(): number {
    const attrs = this.attributes;
    if (!attrs) return 0;

    // 根据位置计算权重
    const weights = this.getPositionWeights();
    return Math.round(
      attrs.speed.cur * weights.speed +
      attrs.finishing.cur * weights.finishing +
      attrs.passing.cur * weights.passing +
      attrs.standingTackle.cur * weights.defending +
      attrs.dribbling.cur * weights.dribbling +
      attrs.strength.cur * weights.physicality +
      attrs.save.cur * weights.goalkeeping
    );
  }

  // 虚拟字段：球员综合实力评分 - 基于old项目BallerRating逻辑
  get ballerRating(): number {
    const attrs = this.attributes;
    if (!attrs) return 0;

    // 计算所有一级属性的平均值 (基于old项目calcRating逻辑)
    const attributeNames = [
      'speed', 'jumping', 'strength', 'stamina', 'explosiveForce',
      'finishing', 'dribbling', 'passing', 'longPassing', 'longShots',
      'heading', 'volleys', 'standingTackle', 'slidingTackle',
      'penalties', 'cornerKick', 'freeKick', 'attack', 'save'
    ];

    let currTotal = 0;
    let count = 0;

    attributeNames.forEach(attrName => {
      const attr = attrs[attrName];
      if (attr) {
        currTotal += attr.cur;
        count++;
      }
    });

    return count > 0 ? Math.floor(currTotal / count) : 0;
  }

  // 虚拟字段：是否可以训练
  get canTrain(): boolean {
    const now = Date.now();
    return now >= (this.training?.lastTrainingTime || 0) + (this.training?.trainingCooldown || 0);
  }

  // 虚拟字段：是否可以升星
  get canEvolve(): boolean {
    return this.evolution.star < 9 && !this.evolution.isMaxStar && this.level >= (this.evolution.star + 1) * 10;
  }

  // 虚拟字段：升星成功率
  get evolutionSuccessRate(): number {
    // 基础成功率根据星级递减
    const baseRate = Math.max(0.8 - (this.evolution.star * 0.1), 0.2);
    return Math.round(baseRate * 100);
  }

  // 虚拟字段：升星所需材料数量
  get evolutionMaterialCount(): number {
    return Math.min(this.evolution.star + 1, 5);
  }

  getPositionWeights(): any {
    // 根据位置返回属性权重 - 基于新的20个完整属性
    const baseWeights = {
      speed: 0.05, jumping: 0.05, strength: 0.05, stamina: 0.05, explosiveForce: 0.05,
      finishing: 0.05, dribbling: 0.05, passing: 0.05, longPassing: 0.05, longShots: 0.05,
      heading: 0.05, volleys: 0.05, standingTackle: 0.05, slidingTackle: 0.05,
      penalties: 0.05, cornerKick: 0.05, freeKick: 0.05, attack: 0.05, save: 0.05, resistanceDamage: 0.05
    };

    switch (this.position) {
      case HeroPosition.GK:
        return { ...baseWeights, save: 0.3, attack: 0.2, strength: 0.15, jumping: 0.1 };
      case HeroPosition.DC:
        return { ...baseWeights, standingTackle: 0.2, heading: 0.15, strength: 0.15, passing: 0.1 };
      case HeroPosition.ST:
        return { ...baseWeights, finishing: 0.25, speed: 0.15, heading: 0.1, volleys: 0.1 };
      case HeroPosition.MC:
        return { ...baseWeights, passing: 0.2, longPassing: 0.15, stamina: 0.1, dribbling: 0.1 };
      default:
        return baseWeights;
    }
  }
}

export const HeroSchema = SchemaFactory.createForClass(Hero);

// 定义方法接口 - 基于HeroService的真实业务逻辑
export interface HeroMethods {
  // 基础信息方法
  getBattleData(): any;
  canLevelUp(): boolean;

  // 属性管理 - 基于HeroService
  calculateOverallRating(): number;
  getPositionWeights(): any;
  updateAttributes(newAttributes: Partial<HeroAttributes>): void;
  addAttributeBonus(attribute: string, bonus: number): void;

  // 等级和经验管理 - 基于HeroService
  addExperience(exp: number): boolean;
  levelUp(): boolean;
  getRequiredExpForNextLevel(): number;
  canUpgrade(): boolean;

  // 训练系统 - 基于HeroService
  canTrain(): boolean;
  startTraining(trainingType: number): boolean;
  completeTraining(): any;
  updateTrainingStage(attribute: string, progress: number): void;
  getTrainingCooldownRemaining(): number;

  // 升星系统 - 基于HeroService
  canEvolve(): boolean;
  evolve(consumedHeroes: string[]): boolean;
  addEvolutionExp(exp: number): boolean;
  getEvolutionRequirement(): any;

  // 技能管理 - 基于HeroService
  addSkill(skill: HeroSkill): boolean;
  removeSkill(skillId: number): boolean;
  upgradeSkill(skillId: number): boolean;
  activateSkill(skillId: number): boolean;
  deactivateSkill(skillId: number): boolean;
  getActiveSkills(): HeroSkill[];

  // 状态管理 - 基于HeroService
  updateEnergy(energy: number): void;
  updateMorale(morale: number): void;
  addFatigue(fatigue: number): void;
  recoverFatigue(): void;
  updateHealth(health: number): void;

  // 阵容管理 - 基于HeroService
  setLineupStatus(inLineup: boolean, position?: number): void;
  getLineupPosition(): number;

  // 装备管理 - 基于HeroService
  equipItem(itemId: string): boolean;
  unequipItem(itemId: string): boolean;
  getEquippedItems(): string[];
  calculateEquipmentBonus(): HeroAttributes;

  // 合同管理 - 基于HeroService
  renewContract(days: number): boolean;
  updateContractDays(days: number): void;
  isContractExpired(): boolean;
  shouldRetire(): boolean;

  // 市场管理 - 基于HeroService
  putOnMarket(price: number): boolean;
  removeFromMarket(): boolean;
  updateMarketValue(): void;

  // 统计管理 - 基于HeroService
  updateMatchStats(goals: number, assists: number, rating: number): void;
  addMatchPlayed(): void;
  addCard(cardType: 'yellow' | 'red'): void;
  getCareerStats(): any;

  // 数据转换 - 基于HeroService
  toClientHeroInfo(): any;
  toFormationData(): any;
  toBattleData(): any;
}

// 定义Document类型
export type HeroDocument = Hero & Document & HeroMethods;

// 创建索引
HeroSchema.index({ heroId: 1 }, { unique: true });
HeroSchema.index({ characterId: 1 });
HeroSchema.index({ resId: 1 });
HeroSchema.index({ position: 1 });
HeroSchema.index({ quality: 1 });
HeroSchema.index({ level: 1 });
HeroSchema.index({ isInLineup: 1 });
HeroSchema.index({ isOnMarket: 1 });
HeroSchema.index({ obtainTime: 1 });

// 注意：overallRating 已在类中定义为 getter，无需重复添加虚拟字段

// 添加实例方法
HeroSchema.methods.getBattleData = function() {
  return {
    heroId: this.heroId,
    name: this.name,
    position: this.position,
    level: this.level,
    attributes: this.attributes,
    skills: this.activeSkills,
    overallRating: this.overallRating,
  };
};

HeroSchema.methods.canLevelUp = function() {
  const requiredExp = this.level * 100; // 简单的升级经验公式
  return this.exp >= requiredExp && this.level < 100;
};

// ==================== 新增实用业务方法实现 ====================

/**
 * 计算总评分 (基于位置权重)
 * 基于HeroService: calculateOverallRating方法逻辑
 */
HeroSchema.methods.calculateOverallRating = function(): number {
  const attrs = this.attributes;
  if (!attrs) return 0;

  const weights = this.getPositionWeights();
  return Math.round(
    attrs.speed.cur * weights.speed +
    attrs.finishing.cur * weights.finishing +
    attrs.passing.cur * weights.passing +
    attrs.standingTackle.cur * weights.defending +
    attrs.dribbling.cur * weights.dribbling +
    attrs.strength.cur * weights.physicality +
    attrs.save.cur * weights.goalkeeping
  );
};



/**
 * 获取位置权重
 * 基于HeroService: 位置权重计算逻辑和现有的getPositionWeights方法
 */
HeroSchema.methods.getPositionWeights = function(): any {
  const baseWeights = {
    speed: 0.1,
    finishing: 0.1,
    passing: 0.1,
    defending: 0.1,
    dribbling: 0.1,
    physicality: 0.1,
    goalkeeping: 0.0
  };

  // 根据真实的HeroPosition枚举调整权重
  switch (this.position) {
    case HeroPosition.GK:
      return { ...baseWeights, goalkeeping: 0.4, defending: 0.2, physicality: 0.2 };
    case HeroPosition.DC:
    case HeroPosition.DL:
    case HeroPosition.DR:
      return { ...baseWeights, defending: 0.3, physicality: 0.2, speed: 0.15 };
    case HeroPosition.MC:
    case HeroPosition.DM:
    case HeroPosition.AM:
      return { ...baseWeights, passing: 0.25, dribbling: 0.2, physicality: 0.15 };
    case HeroPosition.WL:
    case HeroPosition.WR:
    case HeroPosition.ML:
    case HeroPosition.MR:
      return { ...baseWeights, speed: 0.25, dribbling: 0.2, finishing: 0.15 };
    case HeroPosition.ST:
      return { ...baseWeights, finishing: 0.3, speed: 0.2, physicality: 0.15 };
    default:
      return baseWeights;
  }
};

/**
 * 更新属性
 * 基于HeroService: 属性更新逻辑
 */
HeroSchema.methods.updateAttributes = function(newAttributes: Partial<HeroAttributes>): void {
  Object.assign(this.attributes, newAttributes);

  // 重新计算总评分
  this.totalPower = this.calculateOverallRating();
  this.lastCalculateTime = new Date();
};

/**
 * 添加属性加成
 * 基于HeroService: 属性加成逻辑（装备、训练等）
 */
HeroSchema.methods.addAttributeBonus = function(attribute: string, bonus: number): void {
  if (this.attributes[attribute] !== undefined) {
    // 使用AttributeDetail结构，加成到cur值
    this.attributes[attribute].cur = Math.max(0, this.attributes[attribute].cur + bonus);
    this.totalPower = this.calculateOverallRating();
    this.lastCalculateTime = new Date();
  }
};

/**
 * 添加经验值
 * 基于HeroService: addExperience方法逻辑
 */
HeroSchema.methods.addExperience = function(exp: number): boolean {
  if (exp <= 0) return false;

  this.exp += exp;
  this.experience += exp; // 训练系统使用的经验值

  // 检查是否可以升级
  while (this.canLevelUp()) {
    this.levelUp();
  }

  return true;
};

/**
 * 升级
 * 基于HeroService: levelUp方法逻辑
 */
HeroSchema.methods.levelUp = function(): boolean {
  if (!this.canLevelUp()) return false;

  const requiredExp = this.getRequiredExpForNextLevel();
  this.exp -= requiredExp;
  this.level += 1;

  // 升级时属性提升
  const attributeGain = Math.floor(this.level / 10) + 1;
  this.addAttributeBonus('speed', attributeGain);
  this.addAttributeBonus('shooting', attributeGain);
  this.addAttributeBonus('passing', attributeGain);
  this.addAttributeBonus('defending', attributeGain);
  this.addAttributeBonus('dribbling', attributeGain);
  this.addAttributeBonus('physicality', attributeGain);

  return true;
};

/**
 * 获取下一级所需经验
 * 基于HeroService: 经验需求计算逻辑
 */
HeroSchema.methods.getRequiredExpForNextLevel = function(): number {
  return this.level * 100;
};

/**
 * 检查是否可以升级
 * 基于HeroService: 升级条件检查逻辑
 */
HeroSchema.methods.canUpgrade = function(): boolean {
  return this.level < 100 && !this.isRetired;
};

/**
 * 检查是否可以训练
 * 基于HeroService: trainHero方法中的训练条件检查逻辑
 */
HeroSchema.methods.canTrain = function(): boolean {
  // 检查球员状态 - 基于service中的真实检查逻辑
  if (this.isTreat) return false;

  // 检查是否退役
  if (this.isRetired) return false;

  // 检查训练冷却时间
  const now = Date.now();
  const canTrainTime = (this.training?.lastTrainingTime || 0) + (this.training?.trainingCooldown || 0);
  if (now < canTrainTime) return false;

  return true;
};

/**
 * 开始训练
 * 基于HeroService: setHeroTrainStatus方法逻辑
 */
HeroSchema.methods.startTraining = function(trainingType: number): boolean {
  if (!this.canTrain()) return false;

  this.training.lastTrainingTime = Date.now();
  this.training.trainingCooldown = 3600000; // 1小时冷却时间
  return true;
};

/**
 * 完成训练
 * 基于HeroService: executeGeneralTraining方法逻辑
 */
HeroSchema.methods.completeTraining = function(): any {
  // 检查训练冷却时间
  if (this.getTrainingCooldownRemaining() > 0) return null;

  this.training.trainingCount += 1;

  // 随机属性提升 - 使用新的20个属性名称
  const attributeNames = ['speed', 'finishing', 'passing', 'standingTackle', 'dribbling', 'strength'];
  const randomAttribute = attributeNames[Math.floor(Math.random() * attributeNames.length)];
  const improvement = Math.floor(Math.random() * 3) + 1; // 1-3点提升

  this.addAttributeBonus(randomAttribute, improvement);

  // 更新训练阶段
  this.updateTrainingStage(randomAttribute, 1);

  return {
    attribute: randomAttribute,
    improvement,
    newValue: this.attributes[randomAttribute]
  };
};

/**
 * 更新训练阶段
 * 基于HeroService: 训练阶段管理逻辑
 */
HeroSchema.methods.updateTrainingStage = function(attribute: string, progress: number): void {
  const stageField = `${attribute}Stage`;
  if (this.training[stageField]) {
    this.training[stageField].stageProgress += progress;
    this.training[stageField].totalTrainingCount += 1;

    // 检查是否可以升级阶段
    const requiredProgress = this.training[stageField].stage * 10;
    if (this.training[stageField].stageProgress >= requiredProgress) {
      this.training[stageField].stage += 1;
      this.training[stageField].stageProgress = 0;
    }
  }
};

/**
 * 获取训练冷却剩余时间
 * 基于HeroService: 训练冷却时间计算逻辑
 */
HeroSchema.methods.getTrainingCooldownRemaining = function(): number {
  const now = Date.now();
  const cooldownEnd = (this.training?.lastTrainingTime || 0) + (this.training?.trainingCooldown || 0);
  return Math.max(0, cooldownEnd - now);
};

/**
 * 检查是否可以升星
 * 基于HeroService: evolveHero方法中的升星条件检查逻辑
 */
HeroSchema.methods.canEvolve = function(): boolean {
  // 检查星级限制 - 基于service中的真实限制（最高9星）
  if (this.evolution.star >= 9) return false;

  // 检查球员状态
  if (this.isRetired || this.isTreat) return false;

  return true;
};

/**
 * 更新升星数据
 * 基于HeroService: evolveHero方法逻辑
 */
HeroSchema.methods.updateEvolutionData = function(newStar: number, consumedHeroes: string[]): void {
  this.evolution.star = newStar;
  this.evolution.evolutionCount += 1;
  this.evolution.consumedHeroes.push(...consumedHeroes);
  this.evolution.lastEvolutionTime = Date.now();
  this.evolution.isMaxStar = newStar >= 9;
};

/**
 * 添加升星经验
 * 基于HeroService: 升星经验管理逻辑
 */
HeroSchema.methods.addEvolutionExp = function(exp: number): boolean {
  if (exp <= 0 || this.evolution.isMaxStar) return false;

  this.evolution.evolutionExp += exp;
  return true;
};

/**
 * 获取升星需求
 * 基于HeroService: 升星需求计算逻辑
 */
HeroSchema.methods.getEvolutionRequirement = function(): any {
  if (this.evolution.isMaxStar) {
    return { canEvolve: false, reason: '已达到最高星级' };
  }

  const requiredLevel = (this.evolution.star + 1) * 10;
  const requiredHeroes = this.evolution.star + 1; // 升星需要的球员数量

  return {
    canEvolve: this.canEvolve(),
    currentStar: this.evolution.star,
    nextStar: this.evolution.star + 1,
    requiredLevel,
    currentLevel: this.level,
    requiredHeroes,
    evolutionExp: this.evolution.evolutionExp
  };
};

/**
 * 升级技能
 * 基于HeroService: upgradeSkill方法逻辑
 */
HeroSchema.methods.upgradeSkill = function(skillId: number): boolean {
  const skill = this.skills.find(s => s.skillId === skillId);
  if (!skill || skill.level >= skill.maxLevel) return false;

  skill.level += 1;
  skill.upgradeCount += 1;
  skill.lastUpgradeTime = Date.now();

  return true;
};

/**
 * 添加技能
 * 基于HeroService: 技能管理逻辑
 */
HeroSchema.methods.addSkill = function(skill: HeroSkill): boolean {
  // 检查技能是否已存在
  const existingSkill = this.skills.find(s => s.skillId === skill.skillId);
  if (existingSkill) return false;

  this.skills.push(skill);
  return true;
};

/**
 * 移除技能
 * 基于HeroService: 技能管理逻辑
 */
HeroSchema.methods.removeSkill = function(skillId: number): boolean {
  const index = this.skills.findIndex(s => s.skillId === skillId);
  if (index === -1) return false;

  this.skills.splice(index, 1);

  // 同时从激活技能列表中移除
  this.deactivateSkill(skillId);

  return true;
};

/**
 * 切换技能激活状态
 * 基于old项目第857-863行：activateHeroSkill功能
 */
HeroSchema.methods.toggleSkillActivate = function(skillId: number): boolean {
  const skill = this.skills.find(s => s.skillId === skillId);
  if (!skill) return false;

  // 切换激活状态
  skill.isActivate = skill.isActivate === 1 ? 0 : 1;

  // 更新activeSkills数组
  if (skill.isActivate === 1) {
    if (!this.activeSkills.includes(skillId)) {
      this.activeSkills.push(skillId);
    }
  } else {
    const index = this.activeSkills.indexOf(skillId);
    if (index > -1) {
      this.activeSkills.splice(index, 1);
    }
  }

  return true;
};

/**
 * 激活技能
 * 基于old项目第857-863行：activateHeroSkill功能
 */
HeroSchema.methods.activateSkill = function(skillId: number): boolean {
  const skill = this.skills.find(s => s.skillId === skillId);
  if (!skill || skill.isActivate === 1) return false;

  // 检查激活技能数量限制
  if (this.activeSkills.length >= 4) return false; // 最多4个激活技能

  // 激活技能
  skill.isActivate = 1;
  this.activeSkills.push(skillId);
  return true;
};

/**
 * 取消激活技能
 * 基于HeroService: 技能管理逻辑
 */
HeroSchema.methods.deactivateSkill = function(skillId: number): boolean {
  const index = this.activeSkills.indexOf(skillId);
  if (index === -1) return false;

  this.activeSkills.splice(index, 1);
  return true;
};

/**
 * 获取激活的技能列表
 * 基于HeroService: 技能查询逻辑
 * 基于old项目第2446行：isActivate === 1的技能为激活状态
 */
HeroSchema.methods.getActiveSkills = function(): HeroSkill[] {
  return this.skills.filter(skill => skill.isActivate === 1);
};

/**
 * 更新合同天数
 * 基于HeroService: renewCareer方法逻辑
 */
HeroSchema.methods.updateContractDays = function(days: number): void {
  this.contractDays += days;
  this.lifeNum += 1;
  this.treatyReTime = Date.now();
};

/**
 * 检查合同是否过期
 * 基于HeroService: shouldRetire方法逻辑
 */
HeroSchema.methods.isContractExpired = function(): boolean {
  return this.contractDays <= 0;
};

/**
 * 检查是否应该退役
 * 基于HeroService: shouldRetire方法逻辑
 */
HeroSchema.methods.shouldRetire = function(): boolean {
  // 基于service中的真实退役条件
  const maxLifeNum = this.quality === HeroQuality.RED ? 10 :
                     this.quality === HeroQuality.ORANGE ? 8 :
                     this.quality === HeroQuality.PURPLE ? 6 :
                     this.quality === HeroQuality.BLUE ? 4 :
                     this.quality === HeroQuality.GREEN ? 3 : 2;

  return this.contractDays <= 0 && this.lifeNum >= maxLifeNum && !this.isRetired;
};

/**
 * 设置阵容状态
 * 基于HeroService: 阵容管理逻辑
 */
HeroSchema.methods.setLineupStatus = function(inLineup: boolean, position?: number): void {
  this.isInLineup = inLineup;
  if (position !== undefined) {
    this.formationPosition = position;
  }
};

/**
 * 获取阵容位置
 * 基于HeroService: 阵容位置查询逻辑
 */
HeroSchema.methods.getLineupPosition = function(): number {
  return this.formationPosition || 0;
};

/**
 * 更新状态数据
 * 基于HeroService: 状态管理逻辑
 */
HeroSchema.methods.updateStatus = function(updates: { energy?: number; morale?: number; fatigue?: number; health?: number }): void {
  if (updates.energy !== undefined) this.energy = Math.max(0, Math.min(100, updates.energy));
  if (updates.morale !== undefined) this.morale = Math.max(0, Math.min(100, updates.morale));
  if (updates.fatigue !== undefined) this.fatigue = Math.max(0, Math.min(100, updates.fatigue));
  if (updates.health !== undefined) this.Health = Math.max(0, Math.min(2, updates.health));
};

/**
 * 设置市场状态
 * 基于HeroService: 市场管理逻辑
 */
HeroSchema.methods.setMarketStatus = function(onMarket: boolean, price?: number): void {
  this.isOnMarket = onMarket;
  if (price !== undefined) {
    this.marketValue = price;
  }
  if (onMarket) {
    this.marketTime = Date.now();
  }
};
