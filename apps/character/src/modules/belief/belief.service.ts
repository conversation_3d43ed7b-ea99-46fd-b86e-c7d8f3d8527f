import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { BeliefRepository } from '../../common/repositories/belief.repository';
import { CharacterService } from '../character/character.service';
import { BeliefSkillCalculator, BeliefDonationCalculator, BeliefManagementCalculator } from './calculators';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { PushClientService } from '@libs/common/push/push-client.service';
import {
  CreateBeliefPayloadDto,
  ApplyJoinBeliefPayloadDto,
  JoinBeliefPayloadDto,
  LeaveBeliefPayloadDto,
  DonateBeliefPayloadDto,
  UpdateBeliefNoticePayloadDto,
  GetBeliefInfoPayloadDto,
  GetBeliefListPayloadDto,
  SetBeliefLeaderPayloadDto,
  GetBeliefMembersPayloadDto,
  GetBeliefRankPayloadDto,
  GetBeliefNotificationsPayloadDto,
  TransferChairmanPayloadDto,
  DisbandBeliefPayloadDto,
  SearchBeliefPayloadDto,
  GetMyBeliefPayloadDto,
  KickBeliefMemberPayloadDto,
  GetBeliefStatsPayloadDto,
  ReviewApplicationPayloadDto,
  GetApplicationsPayloadDto
} from './dto/belief-payload.dto';

/**
 * 信仰系统业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的信仰系统业务逻辑
 *
 * 🎯 核心功能：
 * - 信仰创建和管理
 * - 信仰成员管理（加入、退出）
 * - 信仰捐献系统（欧元、球币）
 * - 信仰等级和经验管理
 * - 信仰领导者管理
 * - 信仰排行和统计
 *
 * 🚀 架构优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 与CharacterService紧密集成，保证数据一致性
 * - 事件驱动的跨服务通信
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、资源检查（直接注入）
 * - BeliefRepository：信仰数据持久化（直接注入）
 * - GameConfigFacade：配置数据获取（直接注入）
 *
 * old项目核心逻辑：
 * - 信仰捐献：donateBeliefGold(type, num)
 * - 信仰升级：addBeliefLiveness(value)
 * - 成员管理：playerList数组操作
 * - 领导者管理：leader数组操作
 */
@Injectable()
export class BeliefService extends BaseService {
  constructor(
    private readonly beliefRepository: BeliefRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    private readonly pushClientService: PushClientService,
  ) {
    super('BeliefService');
  }

  /**
   * 创建信仰
   * 基于old项目: 创建新的信仰组织
   */
  async createBelief(dto: CreateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建信仰: ${dto.characterId}, 名称: ${dto.name}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入信仰，无法创建新信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 检查信仰名称是否可用
      const nameAvailableResult = await this.beliefRepository.isNameAvailable(dto.name);
      if (XResultUtils.isFailure(nameAvailableResult)) {
        return XResultUtils.error(`检查名称可用性失败: ${nameAvailableResult.message}`, nameAvailableResult.code);
      }

      if (!nameAvailableResult.data) {
        return XResultUtils.error('信仰名称已被使用', 'BELIEF_NAME_TAKEN');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 检查创建费用（基于old项目逻辑）
      const createCost = BeliefManagementCalculator.getBeliefCreateCost();
      if (character.cash < createCost) {
        return XResultUtils.error('现金不足，无法创建信仰', 'INSUFFICIENT_CASH');
      }

      // 5. 扣除创建费用
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        currencyType: 'cash',
        amount: createCost,
        reason: 'create_belief'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除费用失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 获取下一个信仰ID
      const nextIdResult = await this.beliefRepository.getNextBeliefId();
      if (XResultUtils.isFailure(nextIdResult)) {
        return XResultUtils.error(`获取信仰ID失败: ${nextIdResult.message}`, nextIdResult.code);
      }

      const beliefId = nextIdResult.data;

      // 7. 创建信仰实体
      const beliefData = {
        beliefId,
        name: dto.name,
        notice: dto.notice || '',
        creatorId: dto.characterId,
        leader: [{
          uid: dto.characterId,
          gid: '', // 预留字段
          name: character.name,
          faceUrl: character.avatar || '',
          pos: 1, // 董事长
          appointTime: Date.now()
        }],
        playerList: [{
          playerId: dto.characterId,
          playerName: character.name,
          faceUrl: character.avatar || '',
          contribution: 0,
          weekContribution: 0,
          joinTime: Date.now(),
          lastActiveTime: Date.now(),
          isActive: true
        }],
        level: 1,
        beliefExp: 0,
        beliefGold: 0,
        status: 'active',
        createTime: Date.now()
      };

      const createResult = await this.beliefRepository.createOne(beliefData);
      if (XResultUtils.isFailure(createResult)) {
        // 创建失败，退还费用
        await this.characterService.addCurrency({
          characterId: dto.characterId,
          currencyType: 'cash',
          amount: createCost,
          reason: 'refund_belief_creation'
        });
        return XResultUtils.error(`创建信仰失败: ${createResult.message}`, createResult.code);
      }

      const belief = createResult.data;

      // 8. 更新角色的信仰信息
      const updateCharacterResult = await this.updateCharacterBeliefInfo(dto.characterId, beliefId, 1); // 1表示董事长
      if (XResultUtils.isFailure(updateCharacterResult)) {
        this.logger.warn(`更新角色信仰信息失败: ${updateCharacterResult.message}`);
      }

      // 9. 添加创建动态
      belief.addNotify(`信仰"${dto.name}"成立，${character.name}成为首任董事长`, 'system');
      await belief.save();

      // 10. 触发信仰创建事件（通知其他服务）
      await this.notifyBeliefCreated(beliefId, dto.characterId);

      return XResultUtils.ok({
        beliefId,
        name: dto.name,
        level: 1,
        memberCount: 1,
        leaderPosition: 1,
        createCost
      });
    }, { reason: 'create_belief', metadata: { characterId: dto.characterId, name: dto.name } });
  }

  /**
   * 申请加入信仰
   * 基于old项目: 玩家申请加入信仰（需要审批）
   */
  async applyJoinBelief(dto: ApplyJoinBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`申请加入信仰: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入其他信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 获取目标信仰
      const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief || belief.status !== 'active') {
        return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
      }

      // 3. 检查信仰是否已满
      if (belief.playerList.length >= belief.maxMembers) {
        return XResultUtils.error('信仰成员已满', 'BELIEF_FULL');
      }

      // 4. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 5. 添加申请到信仰
      try {
        belief.addApplication({
          playerId: dto.characterId,
          playerName: character.name,
          faceUrl: character.avatar || '',
          message: dto.message || ''
        });

        // 6. 添加申请动态
        belief.addNotify(`${character.name}申请加入信仰`, 'application', dto.characterId);

        // 7. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 8. 推送申请通知给管理人员
        await this.pushNewApplicationToManagers(belief, character, dto.message || '');

        return XResultUtils.ok({
          beliefId: dto.beliefId,
          beliefName: belief.name,
          applyTime: Date.now(),
          message: '申请已提交，等待管理员审批'
        });

      } catch (error) {
        return XResultUtils.error(`申请加入信仰失败: ${error.message}`, 'APPLY_JOIN_BELIEF_ERROR');
      }
    }, { reason: 'apply_join_belief', metadata: { characterId: dto.characterId, beliefId: dto.beliefId } });
  }

  // ==================== 私有辅助方法 ====================

  // 已迁移到BeliefManagementCalculator.getBeliefCreateCost

  /**
   * 通知信仰创建事件
   */
  private async notifyBeliefCreated(beliefId: number, creatorId: string): Promise<void> {
    try {
      // 发送事件到其他服务（通过微服务调用或事件系统）
      // 1. 通知统计服务更新信仰数量
      // 2. 通知排行榜服务
      // 3. 通知活动服务（如果有相关活动）

      this.logger.log(`信仰创建事件: ${beliefId}, 创建者: ${creatorId}`);

      // 可以通过消息队列或事件总线发送事件
      // await this.eventBus.publish('belief.created', { beliefId, creatorId, timestamp: Date.now() });

    } catch (error) {
      this.logger.error(`通知信仰创建事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 通知加入信仰事件
   */
  private async notifyBeliefJoined(beliefId: number, playerId: string): Promise<void> {
    try {
      // 发送加入信仰事件到其他服务
      // 1. 通知统计服务更新成员数量
      // 2. 通知排行榜服务更新活跃度
      // 3. 通知任务系统（如果有加入信仰相关任务）

      this.logger.log(`加入信仰事件: ${beliefId}, 玩家: ${playerId}`);

      // 可以通过消息队列发送事件
      // await this.eventBus.publish('belief.joined', { beliefId, playerId, timestamp: Date.now() });

    } catch (error) {
      this.logger.error(`通知加入信仰事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 审批申请
   */
  async reviewApplication(dto: ReviewApplicationPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`审批申请: ${dto.characterId} -> ${dto.targetPlayerId}, 操作: ${dto.action}`);

      // 1. 获取操作者的信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查操作权限（董事长、副董事长、理事可以审批）
      const operator = belief.leader.find(leader => leader.uid === dto.characterId);
      if (!operator || operator.pos > 3) {
        return XResultUtils.error('权限不足，只有管理人员可以审批申请', 'PERMISSION_DENIED');
      }

      // 3. 查找申请
      const application = belief.applications.find(app =>
        app.playerId === dto.targetPlayerId && app.status === 'pending'
      );
      if (!application) {
        return XResultUtils.error('未找到待审批的申请', 'APPLICATION_NOT_FOUND');
      }

      try {
        if (dto.action === 'approve') {
          // 4a. 同意申请
          const approvedApp = belief.approveApplication(dto.targetPlayerId, dto.characterId, dto.reason);
          if (!approvedApp) {
            return XResultUtils.error('审批失败', 'REVIEW_FAILED');
          }

          // 检查信仰是否已满
          if (belief.playerList.length >= belief.maxMembers) {
            return XResultUtils.error('信仰成员已满，无法通过申请', 'BELIEF_FULL');
          }

          // 添加成员到信仰
          belief.addMember({
            playerId: dto.targetPlayerId,
            playerName: approvedApp.playerName,
            faceUrl: approvedApp.faceUrl,
            contribution: 0,
            weekContribution: 0
          });

          // 添加加入动态
          belief.addNotify(`${approvedApp.playerName}通过申请加入了信仰`, 'player', dto.targetPlayerId);

          // 更新角色的信仰信息
          const updateCharacterResult = await this.updateCharacterBeliefInfo(dto.targetPlayerId, belief.beliefId, 4);
          if (XResultUtils.isFailure(updateCharacterResult)) {
            this.logger.warn(`更新角色信仰信息失败: ${updateCharacterResult.message}`);
          }

          // 推送通过通知给申请者
          await this.pushApplicationApprovedToApplicant(dto.targetPlayerId, belief, dto.reason || '');

        } else {
          // 4b. 拒绝申请
          const rejectedApp = belief.rejectApplication(dto.targetPlayerId, dto.characterId, dto.reason);
          if (!rejectedApp) {
            return XResultUtils.error('审批失败', 'REVIEW_FAILED');
          }

          // 添加拒绝动态
          belief.addNotify(`${rejectedApp.playerName}的申请被拒绝`, 'application', dto.targetPlayerId);

          // 推送拒绝通知给申请者
          await this.pushApplicationRejectedToApplicant(dto.targetPlayerId, belief, dto.reason || '');
        }

        // 5. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        return XResultUtils.ok({
          targetPlayerId: dto.targetPlayerId,
          targetPlayerName: application.playerName,
          action: dto.action,
          reason: dto.reason || '',
          reviewTime: Date.now(),
          message: dto.action === 'approve' ? '申请已通过' : '申请已拒绝'
        });

      } catch (error) {
        return XResultUtils.error(`审批申请失败: ${error.message}`, 'REVIEW_APPLICATION_ERROR');
      }
    }, { reason: 'review_application', metadata: { characterId: dto.characterId, targetPlayerId: dto.targetPlayerId, action: dto.action } });
  }

  /**
   * 获取申请列表
   */
  async getApplications(dto: GetApplicationsPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取申请列表: ${dto.characterId}`);

      // 1. 获取操作者的信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查查看权限（董事长、副董事长、理事可以查看）
      const operator = belief.leader.find(leader => leader.uid === dto.characterId);
      if (!operator || operator.pos > 3) {
        return XResultUtils.error('权限不足，只有管理人员可以查看申请', 'PERMISSION_DENIED');
      }

      // 3. 筛选申请
      const status = dto.status || 'pending';
      let applications = belief.applications;

      if (status !== 'all') {
        applications = applications.filter(app => app.status === status);
      }

      // 4. 分页
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedApplications = applications.slice(startIndex, endIndex);

      return XResultUtils.ok({
        applications: paginatedApplications,
        total: applications.length,
        page,
        limit,
        hasMore: endIndex < applications.length
      });

    }, { reason: 'get_applications', metadata: { characterId: dto.characterId } });
  }

  /**
   * 退出信仰
   * 基于old项目: 玩家主动退出信仰
   */
  async leaveBelief(dto: LeaveBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`退出信仰: ${dto.characterId}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查是否为董事长
      const isChairman = belief.leader.some(leader => leader.uid === dto.characterId && leader.pos === 1);
      if (isChairman && belief.playerList.length > 1) {
        return XResultUtils.error('董事长不能直接退出，请先转让职位或解散信仰', 'CHAIRMAN_CANNOT_LEAVE');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 从信仰中移除成员
      try {
        belief.removeMember(dto.characterId);

        // 5. 移除领导职位（如果有）
        belief.leader = belief.leader.filter(leader => leader.uid !== dto.characterId);

        // 6. 添加退出动态
        belief.addNotify(`${character.name}退出了信仰`, 'player', dto.characterId);

        // 7. 如果信仰没有成员了，解散信仰
        if (belief.playerList.length === 0) {
          belief.status = 'disbanded';
          belief.addNotify('信仰因无成员而自动解散', 'system');
        }

        // 8. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 9. 清除角色的信仰信息
        const clearCharacterResult = await this.updateCharacterBeliefInfo(dto.characterId, 0, 0); // 清除信仰信息
        if (XResultUtils.isFailure(clearCharacterResult)) {
          this.logger.warn(`清除角色信仰信息失败: ${clearCharacterResult.message}`);
        }

        // 10. 推送成员退出通知给管理人员
        await this.pushMemberLeftToManagers(belief, character);

        // 11. 触发退出信仰事件
        await this.notifyBeliefLeft(belief.beliefId, dto.characterId);

        return XResultUtils.ok({
          beliefId: belief.beliefId,
          beliefName: belief.name,
          leftTime: Date.now(),
          isDisbanded: belief.status === 'disbanded'
        });

      } catch (error) {
        return XResultUtils.error(`退出信仰失败: ${error.message}`, 'LEAVE_BELIEF_ERROR');
      }
    }, { reason: 'leave_belief', metadata: { characterId: dto.characterId } });
  }

  /**
   * 信仰捐献
   * 基于old项目: donateBeliefGold(type, num)
   */
  async donateBelief(dto: DonateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`信仰捐献: ${dto.characterId}, 类型: ${dto.type}, 数量: ${dto.amount}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 3. 计算捐献获得的信仰资金和经验
      const donationResult = BeliefDonationCalculator.calculateDonation(dto.type, dto.amount);
      const { beliefGoldGain, expGain, costAmount, currencyType } = donationResult;

      // 4. 检查角色资源是否足够
      const currentAmount = character[currencyType] || 0;
      if (currentAmount < costAmount) {
        const errorMsg = currencyType === 'cash' ? '现金不足' : '金币不足';
        return XResultUtils.error(errorMsg, 'INSUFFICIENT_CURRENCY');
      }

      // 5. 扣除角色资源
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        currencyType,
        amount: costAmount,
        reason: 'belief_donation'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除资源失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 更新信仰资金和经验
      belief.beliefGold += beliefGoldGain;
      belief.totalDonation += beliefGoldGain;

      // 7. 更新信仰经验和等级
      const levelUpResult = belief.addExperience(expGain);

      // 8. 更新成员贡献
      const member = belief.playerList.find(m => m.playerId === dto.characterId);
      if (member) {
        member.contribution += beliefGoldGain;
        member.weekContribution += beliefGoldGain;
        member.lastActiveTime = Date.now();
        member.isActive = true;
      }

      // 9. 更新当日捐献记录（球币捐献）
      if (dto.type === 2) {
        belief.todayGoldNum += dto.amount;
      }

      // 10. 添加捐献动态
      const currencyName = currencyType === 'cash' ? '现金' : '金币';
      belief.addNotify(
        `${character.name}捐献了${costAmount}${currencyName}，信仰获得${beliefGoldGain}资金`,
        'player',
        dto.characterId
      );

      // 11. 如果升级了，添加升级动态
      if (levelUpResult.levelUp) {
        belief.addNotify(
          `信仰升级到${levelUpResult.newLevel}级！`,
          'system'
        );
      }

      // 12. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 13. 触发捐献事件
      await this.notifyBeliefDonation(belief.beliefId, dto.characterId, beliefGoldGain);

      return XResultUtils.ok({
        beliefGoldGain,
        expGain,
        costAmount,
        currencyType,
        newBeliefGold: belief.beliefGold,
        newLevel: belief.level,
        levelUp: levelUpResult.levelUp,
        memberContribution: member?.contribution || 0
      });
    }, { reason: 'donate_belief', metadata: { characterId: dto.characterId, type: dto.type, amount: dto.amount } });
  }

  // ==================== 私有辅助方法（续） ====================

  // 已迁移到BeliefDonationCalculator.calculateDonation（需要配置支持）

  /**
   * 通知退出信仰事件
   */
  private async notifyBeliefLeft(beliefId: number, playerId: string): Promise<void> {
    try {
      this.logger.log(`退出信仰事件: ${beliefId}, 玩家: ${playerId}`);
    } catch (error) {
      this.logger.error('通知退出信仰事件失败', error);
    }
  }

  /**
   * 通知信仰捐献事件
   */
  private async notifyBeliefDonation(beliefId: number, playerId: string, amount: number): Promise<void> {
    try {
      this.logger.log(`信仰捐献事件: ${beliefId}, 玩家: ${playerId}, 金额: ${amount}`);
    } catch (error) {
      this.logger.error('通知信仰捐献事件失败', error);
    }
  }

  // ==================== 控制器调用的其他方法 ====================

  /**
   * 获取信仰信息
   */
  async getBeliefInfo(dto: GetBeliefInfoPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰信息: ${dto.beliefId}`);

    const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief || belief.status !== 'active') {
      return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
    }

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      name: belief.name,
      notice: belief.notice,
      level: belief.level,
      beliefExp: belief.beliefExp,
      beliefGold: belief.beliefGold,
      memberCount: belief.playerList.length,
      maxMembers: belief.maxMembers,
      leader: belief.leader,
      createTime: belief.createTime,
      beliefRank: belief.beliefRank
    });
  }

  /**
   * 获取信仰列表
   */
  async getBeliefList(dto: GetBeliefListPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰列表: 页码${dto.page || 1}`);

    const page = dto.page || 1;
    const limit = dto.limit || 20;

    const result = await this.beliefRepository.getActiveBeliefs(page, limit);
    return result;
  }

  /**
   * 更新信仰公告
   */
  async updateBeliefNotice(dto: UpdateBeliefNoticePayloadDto): Promise<XResult<any>> {
    this.logger.log(`更新信仰公告: ${dto.characterId}`);

    // 获取角色当前信仰
    const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief) {
      return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
    }

    // 检查是否为董事长
    const isChairman = belief.leader.some(leader => leader.uid === dto.characterId && leader.pos === 1);
    if (!isChairman) {
      return XResultUtils.error('只有董事长可以更新信仰公告', 'PERMISSION_DENIED');
    }

    // 更新公告
    belief.notice = dto.notice;
    belief.addNotify('信仰公告已更新', 'system');

    const saveResult = await this.beliefRepository.updateById(belief._id, belief);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
    }

    // 推送公告更新通知给全体成员
    await this.pushNoticeUpdateToAllMembers(belief, dto.notice);

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      notice: dto.notice,
      updateTime: Date.now()
    });
  }

  /**
   * 设置信仰领导者
   * 基于old项目: setBeliefLeader方法
   */
  async setBeliefLeader(dto: SetBeliefLeaderPayloadDto): Promise<XResult<any>> {
    this.logger.log(`设置信仰领导者: ${dto.characterId} -> ${dto.targetPlayerId}, 职位: ${dto.position}`);

    try {
      // 1. 获取操作者角色信息
      const operatorResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(operatorResult)) {
        return XResultUtils.error(`获取操作者信息失败: ${operatorResult.message}`, operatorResult.code);
      }

      // 2. 获取目标角色信息
      const targetResult = await this.characterService.getCharacterInfo(dto.targetPlayerId);
      if (XResultUtils.isFailure(targetResult)) {
        return XResultUtils.error(`获取目标角色信息失败: ${targetResult.message}`, targetResult.code);
      }

      // 3. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 4. 检查操作权限（只有董事长可以设置领导者）
      const operator = belief.leader.find(l => l.uid === dto.characterId);
      if (!operator || operator.pos !== 1) {
        return XResultUtils.error('只有董事长可以设置领导者', 'PERMISSION_DENIED');
      }

      // 5. 检查目标是否在同一信仰
      const targetMember = belief.playerList.find(p => p.playerId === dto.targetPlayerId);
      if (!targetMember) {
        return XResultUtils.error('目标角色不在同一信仰中', 'TARGET_NOT_IN_BELIEF');
      }

      // 6. 检查职位有效性（1-董事长，2-副董事长，3-理事，4-成员）
      if (dto.position < 1 || dto.position > 4) {
        return XResultUtils.error('无效的职位', 'INVALID_POSITION');
      }

      // 7. 查找目标在leader数组中的记录
      let targetLeader = belief.leader.find(l => l.uid === dto.targetPlayerId);
      const oldPosition = targetLeader ? targetLeader.pos : 4; // 默认为普通成员

      // 8. 如果是任命董事长，需要特殊处理
      if (dto.position === 1) {
        // 原董事长降为副董事长
        operator.pos = 2;
      }

      // 9. 更新或创建目标角色的领导职位
      if (targetLeader) {
        targetLeader.pos = dto.position;
        targetLeader.appointTime = Date.now();
      } else {
        // 创建新的领导记录
        belief.leader.push({
          uid: dto.targetPlayerId,
          gid: '',
          name: targetMember.playerName,
          faceUrl: targetMember.faceUrl,
          pos: dto.position,
          appointTime: Date.now()
        });
      }

      // 9. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 10. 添加动态通知
      const actionText = dto.isAppoint ? '任命' : '调整';
      const positionName = this.getPositionName(dto.position);
      belief.notify.push({
        msg: `${operatorResult.data.name}${actionText}${targetResult.data.name}为${positionName}`,
        type: 'leadership',
        time: Date.now(),
        playerId: dto.characterId
      });

      // 11. 推送职位变更通知给目标成员
      await this.pushPositionChangeNotification(dto.targetPlayerId, belief, dto.position, oldPosition);

      return XResultUtils.ok({
        targetPlayerId: dto.targetPlayerId,
        targetPlayerName: targetResult.data.name,
        oldPosition,
        newPosition: dto.position,
        newPositionName: positionName,
        appointTime: Date.now()
      });

    } catch (error) {
      this.logger.error(`设置信仰领导者失败: ${error.message}`, error.stack);
      return XResultUtils.error(`设置信仰领导者失败: ${error.message}`, 'SET_BELIEF_LEADER_ERROR');
    }
  }

  /**
   * 获取信仰成员列表
   * 基于old项目: getBeliefMembers方法
   */
  async getBeliefMembers(dto: GetBeliefMembersPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰成员列表: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

    try {
      // 1. 获取信仰信息
      let belief;
      if (dto.beliefId) {
        // 通过信仰ID获取
        const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
        if (XResultUtils.isFailure(beliefResult)) {
          return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
        }
        belief = beliefResult.data;
      } else {
        // 通过角色ID获取其所在信仰
        const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
        if (XResultUtils.isFailure(beliefResult)) {
          return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
        }
        belief = beliefResult.data;
      }

      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 分页处理
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const skip = (page - 1) * limit;

      // 3. 排序处理
      let sortedMembers = [...belief.playerList];
      const sortBy = dto.sortBy || 'joinTime';
      const sortOrder = dto.sortOrder || 'asc';

      sortedMembers.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
          case 'contribution':
            aValue = a.contribution || 0;
            bValue = b.contribution || 0;
            break;
          case 'joinTime':
            aValue = a.joinTime;
            bValue = b.joinTime;
            break;
          case 'lastActiveTime':
            aValue = a.lastActiveTime || 0;
            bValue = b.lastActiveTime || 0;
            break;
          default:
            aValue = a.joinTime;
            bValue = b.joinTime;
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        } else {
          return aValue - bValue;
        }
      });

      // 4. 分页截取
      const totalCount = sortedMembers.length;
      const members = sortedMembers.slice(skip, skip + limit);

      // 5. 获取成员详细信息
      const memberDetails = await Promise.all(
        members.map(async (member) => {
          try {
            const characterResult = await this.characterService.getCharacterInfo(member.playerId);
            const characterInfo = XResultUtils.isSuccess(characterResult) ? characterResult.data : null;

            // 查找该成员在leader数组中的职位
            const leaderInfo = belief.leader.find(l => l.uid === member.playerId);
            const position = leaderInfo ? leaderInfo.pos : 4; // 默认为普通成员

            return {
              playerId: member.playerId,
              playerName: member.playerName || characterInfo?.name || '未知',
              faceUrl: member.faceUrl || characterInfo?.faceUrl || '',
              level: characterInfo?.level || 1,
              position: position,
              positionName: this.getPositionName(position),
              contribution: member.contribution || 0,
              weekContribution: member.weekContribution || 0,
              joinTime: member.joinTime,
              lastActiveTime: member.lastActiveTime || 0,
              isOnline: this.isPlayerOnline(member.playerId),
              teamValue: 0 // CharacterInfoDto中没有teamValue字段，暂时设为0
            };
          } catch (error) {
            this.logger.warn(`获取成员信息失败: ${member.playerId}, ${error.message}`);
            // 查找该成员在leader数组中的职位
            const leaderInfo = belief.leader.find(l => l.uid === member.playerId);
            const position = leaderInfo ? leaderInfo.pos : 4; // 默认为普通成员

            return {
              playerId: member.playerId,
              playerName: member.playerName || '未知',
              faceUrl: member.faceUrl || '',
              level: 1,
              position: position,
              positionName: this.getPositionName(position),
              contribution: member.contribution || 0,
              weekContribution: member.weekContribution || 0,
              joinTime: member.joinTime,
              lastActiveTime: member.lastActiveTime || 0,
              isOnline: false,
              teamValue: 0
            };
          }
        })
      );

      return XResultUtils.ok({
        beliefId: belief.beliefId,
        beliefName: belief.name,
        members: memberDetails,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        },
        statistics: {
          totalMembers: totalCount,
          onlineMembers: memberDetails.filter(m => m.isOnline).length,
          positionCounts: this.calculatePositionCounts(sortedMembers, belief.leader)
        }
      });

    } catch (error) {
      this.logger.error(`获取信仰成员列表失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰成员列表失败: ${error.message}`, 'GET_BELIEF_MEMBERS_ERROR');
    }
  }

  /**
   * 获取信仰排行
   */
  async getBeliefRank(dto: GetBeliefRankPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰排行: ${dto.characterId}, 类型: ${dto.rankType}`);

    try {
      const page = dto.page || 1;
      const limit = dto.limit || 50;
      const skip = (page - 1) * limit;

      let rankings: any[] = [];
      let totalCount = 0;

      switch (dto.rankType) {
        case 'level':
          // 信仰等级排行
          // 使用现有的getBeliefRanking方法
          const levelRankResult = await this.beliefRepository.getBeliefRanking(1000); // 获取足够多的数据用于排序
          if (XResultUtils.isSuccess(levelRankResult)) {
            // 按等级和经验排序
            const sortedBeliefs = (levelRankResult.data as any[]).sort((a: any, b: any) => {
              if (a.level !== b.level) return b.level - a.level;
              return (b.beliefExp || 0) - (a.beliefExp || 0);
            });

            rankings = sortedBeliefs.slice(skip, skip + limit).map((belief: any, index: number) => ({
              rank: skip + index + 1,
              beliefId: belief.beliefId,
              beliefName: belief.name,
              level: belief.level,
              experience: belief.beliefExp || 0,
              memberCount: belief.playerList?.length || 0,
              chairmanName: this.getChairmanName(belief.leader),
              createdTime: belief.createdTime
            }));
            totalCount = sortedBeliefs.length;
          }
          break;

        case 'activity':
          // 信仰活跃度排行
          // 使用现有的getBeliefRanking方法，按活跃度排序
          const activityRankResult = await this.beliefRepository.getBeliefRanking(1000); // 获取足够多的数据用于排序
          if (XResultUtils.isSuccess(activityRankResult)) {
            // 按周贡献排序
            const sortedBeliefs = (activityRankResult.data as any[]).sort((a: any, b: any) => {
              return (b.weekExp || 0) - (a.weekExp || 0);
            });

            rankings = sortedBeliefs.slice(skip, skip + limit).map((belief: any, index: number) => ({
              rank: skip + index + 1,
              beliefId: belief.beliefId,
              beliefName: belief.name,
              level: belief.level,
              todayActivity: belief.todayGoldNum || 0,
              weekActivity: belief.weekExp || 0,
              totalActivity: belief.totalDonation || 0,
              memberCount: belief.playerList?.length || 0,
              chairmanName: this.getChairmanName(belief.leader)
            }));
            totalCount = sortedBeliefs.length;
          }
          break;

        case 'contribution':
          // 个人贡献排行（需要指定信仰）
          const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
          if (XResultUtils.isFailure(beliefResult) || !beliefResult.data) {
            return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
          }

          const belief = beliefResult.data;
          const sortedMembers = [...belief.playerList].sort((a, b) => (b.contribution || 0) - (a.contribution || 0));

          const startIndex = skip;
          const endIndex = Math.min(skip + limit, sortedMembers.length);
          const memberRankings = sortedMembers.slice(startIndex, endIndex);

          // 获取成员详细信息
          rankings = await Promise.all(
            memberRankings.map(async (member: any, index: number) => {
              try {
                const characterResult = await this.characterService.getCharacterInfo(member.playerId);
                const characterInfo = XResultUtils.isSuccess(characterResult) ? characterResult.data : null;

                // 查找该成员在leader数组中的职位
                const leaderInfo = belief.leader.find(l => l.uid === member.playerId);
                const position = leaderInfo ? leaderInfo.pos : 4; // 默认为普通成员

                return {
                  rank: startIndex + index + 1,
                  playerId: member.playerId,
                  playerName: member.playerName || characterInfo?.name || '未知',
                  faceUrl: member.faceUrl || characterInfo?.faceUrl || '',
                  level: characterInfo?.level || 1,
                  contribution: member.contribution || 0,
                  weekContribution: member.weekContribution || 0,
                  position: position,
                  positionName: this.getPositionName(position),
                  joinTime: member.joinTime,
                  lastActiveTime: member.lastActiveTime || 0
                };
              } catch (error) {
                // 查找该成员在leader数组中的职位
                const leaderInfo = belief.leader.find(l => l.uid === member.playerId);
                const position = leaderInfo ? leaderInfo.pos : 4; // 默认为普通成员

                return {
                  rank: startIndex + index + 1,
                  playerId: member.playerId,
                  playerName: member.playerName || '未知',
                  faceUrl: member.faceUrl || '',
                  level: 1,
                  contribution: member.contribution || 0,
                  weekContribution: member.weekContribution || 0,
                  position: position,
                  positionName: this.getPositionName(position),
                  joinTime: member.joinTime,
                  lastActiveTime: member.lastActiveTime || 0
                };
              }
            })
          );

          totalCount = sortedMembers.length;
          break;

        default:
          // 默认使用等级排行
          const defaultResult = await this.beliefRepository.getBeliefRanking(limit);
          if (XResultUtils.isSuccess(defaultResult)) {
            rankings = defaultResult.data;
            totalCount = defaultResult.data?.length || 0;
          }
          break;
      }

      return XResultUtils.ok({
        rankType: dto.rankType || 'level',
        rankings,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        },
        updateTime: Date.now()
      });

    } catch (error) {
      this.logger.error(`获取信仰排行失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰排行失败: ${error.message}`, 'GET_BELIEF_RANK_ERROR');
    }
  }

  /**
   * 获取我的信仰信息
   */
  async getBelief(dto: GetMyBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取我的信仰信息: ${dto.characterId}`);

    const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief) {
      return XResultUtils.ok(null); // 未加入信仰
    }

    // 获取我在信仰中的信息
    const myMember = belief.playerList.find(m => m.playerId === dto.characterId);
    const myLeader = belief.leader.find(l => l.uid === dto.characterId);

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      name: belief.name,
      notice: belief.notice,
      level: belief.level,
      memberCount: belief.playerList.length,
      myContribution: myMember?.contribution || 0,
      myPosition: myLeader?.pos || 0,
      joinTime: myMember?.joinTime || 0
    });
  }

  /**
   * 搜索信仰
   */
  async searchBelief(dto: SearchBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`搜索信仰: ${dto.keyword}`);

    const result = await this.beliefRepository.searchByName(dto.keyword, 20);
    return result;
  }

  /**
   * 增加信仰活跃度
   * 基于old项目: addBeliefLiveness方法
   *
   * @param playerId 玩家ID
   * @param beliefId 信仰ID
   * @param playerName 玩家名称
   * @param faceUrl 头像URL
   * @param value 活跃度数值
   * @param type 类型：1-任务活跃度，2-捐球币活跃度
   * @param goldNum 球币数量（type=2时使用）
   */
  async addBeliefLiveness(
    playerId: string,
    beliefId: number,
    playerName: string,
    faceUrl: string,
    value: number,
    type: 1 | 2,
    goldNum?: number
  ): Promise<XResult<{ level: number; beliefExp: number; levelUp: boolean }>> {
    this.logger.log(`增加信仰活跃度: 玩家${playerId}, 信仰${beliefId}, 活跃度${value}, 类型${type}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByBeliefId(beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 增加信仰活跃度和经验
      const originalLevel = belief.level;
      const addExpResult = belief.addExperience(value);

      // 3. 球币捐献时增加今日捐献统计
      if (type === 2 && goldNum) {
        belief.todayGoldNum = (belief.todayGoldNum || 0) + goldNum;
      }

      // 4. 更新活跃度排行
      await this.updateBeliefActivityRanking(beliefId, playerId, playerName, faceUrl, value);

      // 5. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 6. 如果信仰升级，发送广播通知
      if (addExpResult.levelUp) {
        await this.broadcastBeliefLevelUp(beliefId, belief.name, addExpResult.newLevel);
        // 推送升级通知给全体成员
        await this.pushBeliefLevelUpToAllMembers(belief, addExpResult.newLevel);
      }

      return XResultUtils.ok({
        level: addExpResult.newLevel,
        beliefExp: belief.beliefExp,
        levelUp: addExpResult.levelUp
      });

    } catch (error) {
      this.logger.error(`增加信仰活跃度失败: ${error.message}`, error.stack);
      return XResultUtils.error(`增加信仰活跃度失败: ${error.message}`, 'ADD_BELIEF_LIVENESS_ERROR');
    }
  }

  /**
   * 邮件增加信仰活跃度
   * 基于old项目: mailAddBeliefLiveness方法
   */
  async mailAddBeliefLiveness(beliefId: number, value: number): Promise<XResult<{ level: number; beliefExp: number; levelUp: boolean }>> {
    this.logger.log(`邮件增加信仰活跃度: 信仰${beliefId}, 活跃度${value}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByBeliefId(beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 增加信仰经验
      const addExpResult = belief.addExperience(value);

      // 3. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 4. 如果信仰升级，发送广播通知
      if (addExpResult.levelUp) {
        await this.broadcastBeliefLevelUp(beliefId, belief.name, addExpResult.newLevel);
        // 推送升级通知给全体成员
        await this.pushBeliefLevelUpToAllMembers(belief, addExpResult.newLevel);
      }

      return XResultUtils.ok({
        level: addExpResult.newLevel,
        beliefExp: belief.beliefExp,
        levelUp: addExpResult.levelUp
      });

    } catch (error) {
      this.logger.error(`邮件增加信仰活跃度失败: ${error.message}`, error.stack);
      return XResultUtils.error(`邮件增加信仰活跃度失败: ${error.message}`, 'MAIL_ADD_BELIEF_LIVENESS_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 广播信仰升级消息
   * 基于old项目: 信仰升级广播机制
   */
  private async broadcastBeliefLevelUp(beliefId: number, beliefName: string, newLevel: number): Promise<void> {
    try {
      const message = `恭喜${beliefName}信仰将信仰等级提升到${newLevel}级`;

      // 实现广播消息发送
      // 这里需要调用聊天服务或消息服务来发送全服广播
      this.logger.log(`信仰升级广播: ${message}`);

      // 可以通过微服务调用聊天服务发送全服广播
      try {
        // await this.microserviceClient.call('chat', 'broadcast.sendSystemMessage', {
        //   message,
        //   type: 'belief_levelup',
        //   data: { beliefId, beliefName, newLevel }
        // });
      } catch (error) {
        this.logger.warn(`发送广播消息失败: ${error.message}`);
      }

    } catch (error) {
      this.logger.error(`发送信仰升级广播失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取信仰统计信息
   */
  async getBeliefStats(dto: GetBeliefStatsPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰统计信息: ${dto.characterId}`);

    const result = await this.beliefRepository.getBeliefStats();
    return result;
  }

  /**
   * 踢出信仰成员
   */
  async kickBeliefMember(dto: KickBeliefMemberPayloadDto): Promise<XResult<any>> {
    this.logger.log(`踢出信仰成员: ${dto.characterId} -> ${dto.targetPlayerId}`);

    try {
      // 1. 获取操作者信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查操作权限
      const operator = belief.leader.find(l => l.uid === dto.characterId);
      if (!operator || operator.pos > 3) { // 只有董事长、副董事长、理事可以踢人
        return XResultUtils.error('权限不足，无法踢出成员', 'PERMISSION_DENIED');
      }

      // 3. 检查目标成员
      const targetMember = belief.playerList.find(p => p.playerId === dto.targetPlayerId);
      if (!targetMember) {
        return XResultUtils.error('目标成员不在信仰中', 'TARGET_NOT_IN_BELIEF');
      }

      // 4. 检查目标成员的职位
      const targetLeader = belief.leader.find(l => l.uid === dto.targetPlayerId);
      const targetPosition = targetLeader ? targetLeader.pos : 4; // 默认为普通成员

      // 5. 检查是否可以踢出（不能踢出比自己职位高的成员）
      if (targetPosition <= operator.pos && dto.targetPlayerId !== dto.characterId) {
        return XResultUtils.error('无法踢出职位相同或更高的成员', 'CANNOT_KICK_HIGHER_POSITION');
      }

      // 6. 不能踢出董事长
      if (targetPosition === 1) {
        return XResultUtils.error('无法踢出董事长', 'CANNOT_KICK_CHAIRMAN');
      }

      // 7. 移除成员
      const memberIndex = belief.playerList.findIndex(p => p.playerId === dto.targetPlayerId);
      belief.playerList.splice(memberIndex, 1);

      // 8. 如果目标成员是领导者，也要从leader数组中移除
      if (targetLeader) {
        const leaderIndex = belief.leader.findIndex(l => l.uid === dto.targetPlayerId);
        if (leaderIndex !== -1) {
          belief.leader.splice(leaderIndex, 1);
        }
      }

      // 9. 添加动态通知
      const targetName = targetMember.playerName || '未知';
      belief.notify.push({
        msg: `${targetName}被踢出信仰`,
        type: 'kick',
        time: Date.now(),
        playerId: dto.characterId
      });

      // 8. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 9. 清除被踢出成员的信仰信息
      await this.updateCharacterBeliefInfo(dto.targetPlayerId, 0, 0);

      // 10. 推送被踢出通知给目标成员
      await this.pushKickedNotificationToMember(dto.targetPlayerId, belief.name, targetName);

      return XResultUtils.ok({
        targetPlayerId: dto.targetPlayerId,
        targetPlayerName: targetName,
        beliefName: belief.name,
        remainingMembers: belief.playerList.length,
        message: `成功踢出成员${targetName}`
      });

    } catch (error) {
      this.logger.error(`踢出信仰成员失败: ${error.message}`, error.stack);
      return XResultUtils.error(`踢出信仰成员失败: ${error.message}`, 'KICK_MEMBER_ERROR');
    }
  }

  /**
   * 解散信仰
   */
  async disbandBelief(dto: DisbandBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`解散信仰: ${dto.characterId}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查权限（只有董事长可以解散信仰）
      const operator = belief.leader.find(l => l.uid === dto.characterId);
      if (!operator || operator.pos !== 1) {
        return XResultUtils.error('只有董事长可以解散信仰', 'PERMISSION_DENIED');
      }

      // 3. 检查成员数量（如果只有董事长一人，可以直接解散）
      if (belief.playerList.length > 1) {
        return XResultUtils.error('信仰中还有其他成员，请先移除所有成员后再解散', 'BELIEF_HAS_MEMBERS');
      }

      // 4. 标记信仰为已解散
      belief.status = 'disbanded';

      // 5. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 6. 推送解散通知给全体成员（在清除信息前推送）
      await this.pushBeliefDisbandedToAllMembers(belief);

      // 7. 清除董事长的信仰信息
      await this.updateCharacterBeliefInfo(dto.characterId, 0, 0);

      // 8. 通知解散事件
      await this.notifyBeliefDisbanded(belief.beliefId, dto.characterId);

      return XResultUtils.ok({
        beliefId: belief.beliefId,
        beliefName: belief.name,
        disbandTime: Date.now(),
        message: `信仰"${belief.name}"已成功解散`
      });

    } catch (error) {
      this.logger.error(`解散信仰失败: ${error.message}`, error.stack);
      return XResultUtils.error(`解散信仰失败: ${error.message}`, 'DISBAND_BELIEF_ERROR');
    }
  }

  /**
   * 转让董事长
   */
  async transferChairman(dto: TransferChairmanPayloadDto): Promise<XResult<any>> {
    this.logger.log(`转让董事长: ${dto.characterId} -> ${dto.targetPlayerId}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查权限（只有董事长可以转让）
      const currentChairman = belief.leader.find(l => l.uid === dto.characterId);
      if (!currentChairman || currentChairman.pos !== 1) {
        return XResultUtils.error('只有董事长可以转让职位', 'PERMISSION_DENIED');
      }

      // 3. 检查目标成员
      const targetMember = belief.playerList.find(p => p.playerId === dto.targetPlayerId);
      if (!targetMember) {
        return XResultUtils.error('目标成员不在信仰中', 'TARGET_NOT_IN_BELIEF');
      }

      // 4. 不能转让给自己
      if (dto.characterId === dto.targetPlayerId) {
        return XResultUtils.error('不能转让给自己', 'CANNOT_TRANSFER_TO_SELF');
      }

      // 5. 获取目标成员名称
      const targetName = targetMember.playerName || '未知';

      // 6. 执行转让
      // 原董事长变为副董事长
      currentChairman.pos = 2;

      // 查找或创建目标成员的领导记录
      let targetLeader = belief.leader.find(l => l.uid === dto.targetPlayerId);
      if (targetLeader) {
        targetLeader.pos = 1;
        targetLeader.appointTime = Date.now();
      } else {
        // 创建新的董事长记录
        belief.leader.push({
          uid: dto.targetPlayerId,
          gid: '',
          name: targetName,
          faceUrl: targetMember.faceUrl,
          pos: 1,
          appointTime: Date.now()
        });
      }

      // 7. 添加动态通知
      belief.notify.push({
        msg: `董事长职位已转让给${targetName}`,
        type: 'transfer',
        time: Date.now(),
        playerId: dto.characterId
      });

      // 8. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 9. 更新角色信仰信息
      await this.updateCharacterBeliefInfo(dto.characterId, belief.beliefId, 2); // 原董事长变为副董事长
      await this.updateCharacterBeliefInfo(dto.targetPlayerId, belief.beliefId, 1); // 新董事长

      return XResultUtils.ok({
        beliefName: belief.name,
        oldChairman: currentChairman.name,
        newChairman: targetName,
        transferTime: Date.now(),
        message: `董事长职位已成功转让给${targetName}`
      });

    } catch (error) {
      this.logger.error(`转让董事长失败: ${error.message}`, error.stack);
      return XResultUtils.error(`转让董事长失败: ${error.message}`, 'TRANSFER_CHAIRMAN_ERROR');
    }
  }

  /**
   * 获取信仰动态
   */
  async getBeliefNotifications(dto: GetBeliefNotificationsPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰动态: ${dto.characterId}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 获取动态通知
      const notifications = belief.notify || [];

      // 3. 分页处理
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const skip = (page - 1) * limit;

      // 4. 按时间排序（最新的在前）
      const sortedNotifications = notifications.sort((a: any, b: any) => b.time - a.time);

      // 5. 分页截取
      const totalCount = sortedNotifications.length;
      const paginatedNotifications = sortedNotifications.slice(skip, skip + limit);

      // 6. 格式化通知数据
      const formattedNotifications = paginatedNotifications.map((notification: any) => ({
        id: notification.id || notification._id,
        message: notification.message,
        type: notification.type || 'system',
        time: notification.time,
        playerId: notification.playerId,
        isRead: notification.isRead || false,
        timeAgo: this.formatTimeAgo(notification.time)
      }));

      return XResultUtils.ok({
        notifications: formattedNotifications,
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: skip + limit < totalCount,
        beliefName: belief.name,
        unreadCount: notifications.filter((n: any) => !n.isRead).length
      });

    } catch (error) {
      this.logger.error(`获取信仰动态失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰动态失败: ${error.message}`, 'GET_NOTIFICATIONS_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取董事长名称
   */
  private getChairmanName(leaders: any[]): string {
    const chairman = leaders?.find(l => l.pos === 1);
    return chairman?.name || '未知';
  }

  /**
   * 获取职位名称
   */
  private getPositionName(position: number): string {
    const positionNames = {
      1: '董事长',
      2: '副董事长',
      3: '理事',
      4: '成员'
    };
    return positionNames[position] || '成员';
  }

  /**
   * 计算职位统计
   */
  private calculatePositionCounts(members: any[], leaders: any[]): any {
    const counts = { 1: 0, 2: 0, 3: 0, 4: 0 };

    // 统计领导者职位
    leaders.forEach(leader => {
      counts[leader.pos] = (counts[leader.pos] || 0) + 1;
    });

    // 其余成员都是普通成员
    const leaderIds = new Set(leaders.map(l => l.uid));
    const regularMembers = members.filter(m => !leaderIds.has(m.playerId));
    counts[4] += regularMembers.length;

    return {
      chairman: counts[1],
      viceChairman: counts[2],
      director: counts[3],
      member: counts[4]
    };
  }

  /**
   * 检查玩家是否在线
   */
  private isPlayerOnline(playerId: string): boolean {
    // 实现在线状态检查
    // 这里可以通过Redis缓存或在线状态服务来检查
    // 暂时返回false，实际项目中需要实现真实的在线检查逻辑
    return false;
  }

  /**
   * 更新角色信仰信息
   * 基于old项目的角色信仰信息更新逻辑
   */
  private async updateCharacterBeliefInfo(characterId: string, beliefId: number, position: number): Promise<XResult<void>> {
    try {
      // 1. 获取角色信息（通过characterService）
      const characterResult = await this.characterService.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 更新信仰信息（基于old项目的真实字段结构）
      // 通过characterService更新角色信仰信息
      const updateData = {
        characterId: characterId,
        beliefId: beliefId
      };

      const saveResult = await this.characterService.updateCharacter(updateData);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存角色数据失败: ${saveResult.message}`, saveResult.code);
      }

      return XResultUtils.ok(undefined);

    } catch (error) {
      return XResultUtils.error(`更新角色信仰信息失败: ${error.message}`, 'UPDATE_CHARACTER_BELIEF_ERROR');
    }
  }

  /**
   * 通知信仰解散事件
   */
  private async notifyBeliefDisbanded(beliefId: number, operatorId: string): Promise<void> {
    try {
      this.logger.log(`信仰解散事件: ${beliefId}, 操作者: ${operatorId}`);
      // 可以通过消息队列发送事件
      // await this.eventBus.publish('belief.disbanded', { beliefId, operatorId, timestamp: Date.now() });
    } catch (error) {
      this.logger.error(`通知信仰解散事件失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 更新信仰活跃度排行
   */
  private async updateBeliefActivityRanking(
    beliefId: number,
    playerId: string,
    playerName: string,
    faceUrl: string,
    value: number
  ): Promise<void> {
    try {
      // 这里可以实现活跃度排行榜的更新逻辑
      // 1. 更新Redis中的排行榜数据
      // 2. 更新数据库中的活跃度统计
      // 3. 触发排行榜刷新事件

      this.logger.log(`更新活跃度排行: 信仰${beliefId}, 玩家${playerId}, 活跃度+${value}`);

      // 实际实现中可以使用Redis的ZADD命令来维护排行榜
      // await this.redis.zadd(`belief:activity:${beliefId}`, value, playerId);
      // await this.redis.zadd('belief:activity:global', value, `${beliefId}:${playerId}`);

    } catch (error) {
      this.logger.error(`更新活跃度排行失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 格式化时间差
   */
  private formatTimeAgo(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  }

  // ==================== 推送通知方法 ====================

  /**
   * 推送新成员加入通知给管理人员
   */
  private async pushNewMemberJoinedToManagers(belief: any, newMember: any): Promise<void> {
    try {
      // 获取管理人员ID列表（董事长、副董事长、理事）
      const managerIds = belief.leader
        .filter((leader: any) => leader.pos <= 3) // 1-董事长，2-副董事长，3-理事
        .map((leader: any) => leader.uid);

      if (managerIds.length === 0) return;

      const notificationData = {
        type: 'belief_new_member',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        newMember: {
          playerId: newMember.characterId,
          playerName: newMember.name,
          avatar: newMember.avatar || '',
          joinTime: Date.now()
        },
        memberCount: belief.playerList.length,
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有管理人员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        managerIds,
        {
          eventName: 'belief_new_member_joined',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed new member notification to ${managerIds.length} managers: ${belief.name}`);
    } catch (error) {
      this.logger.error(`Failed to push new member notification: ${error.message}`);
    }
  }

  /**
   * 推送公告更新通知给全体成员
   */
  private async pushNoticeUpdateToAllMembers(belief: any, newNotice: string): Promise<void> {
    try {
      // 获取所有成员ID列表
      const memberIds = belief.playerList.map((member: any) => member.playerId);

      if (memberIds.length === 0) return;

      const notificationData = {
        type: 'belief_notice_updated',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        notice: newNotice,
        updateTime: Date.now(),
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有成员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        memberIds,
        {
          eventName: 'belief_notice_updated',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed notice update to ${memberIds.length} members: ${belief.name}`);
    } catch (error) {
      this.logger.error(`Failed to push notice update notification: ${error.message}`);
    }
  }

  /**
   * 推送被踢出通知给目标成员
   */
  private async pushKickedNotificationToMember(targetPlayerId: string, beliefName: string, playerName: string): Promise<void> {
    try {
      const notificationData = {
        type: 'belief_member_kicked',
        beliefName,
        playerName,
        kickTime: Date.now(),
        message: `您已被踢出信仰"${beliefName}"`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给被踢出的成员（使用角色ID）
      await this.pushClientService.pushToCharacter(
        targetPlayerId,
        {
          eventName: 'belief_member_kicked',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed kicked notification to member: ${playerName} from ${beliefName}`);
    } catch (error) {
      this.logger.error(`Failed to push kicked notification: ${error.message}`);
    }
  }

  /**
   * 推送职位变更通知给目标成员
   */
  private async pushPositionChangeNotification(targetPlayerId: string, belief: any, newPosition: number, oldPosition: number): Promise<void> {
    try {
      const positionNames = {
        1: '董事长',
        2: '副董事长',
        3: '理事',
        4: '成员'
      };

      const notificationData = {
        type: 'belief_position_changed',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        oldPosition: positionNames[oldPosition] || '成员',
        newPosition: positionNames[newPosition] || '成员',
        changeTime: Date.now(),
        message: `您在信仰"${belief.name}"中的职位已变更为${positionNames[newPosition]}`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给目标成员（使用角色ID）
      await this.pushClientService.pushToCharacter(
        targetPlayerId,
        {
          eventName: 'belief_position_changed',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed position change notification: ${targetPlayerId} -> ${positionNames[newPosition]}`);
    } catch (error) {
      this.logger.error(`Failed to push position change notification: ${error.message}`);
    }
  }

  /**
   * 推送成员退出通知给管理人员
   */
  private async pushMemberLeftToManagers(belief: any, leftMember: any): Promise<void> {
    try {
      // 获取管理人员ID列表（董事长、副董事长、理事）
      const managerIds = belief.leader
        .filter((leader: any) => leader.pos <= 3) // 1-董事长，2-副董事长，3-理事
        .map((leader: any) => leader.uid)
        .filter((id: string) => id !== leftMember.characterId); // 排除退出的成员自己

      if (managerIds.length === 0) return;

      const notificationData = {
        type: 'belief_member_left',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        leftMember: {
          playerId: leftMember.characterId,
          playerName: leftMember.name,
          avatar: leftMember.avatar || '',
          leaveTime: Date.now()
        },
        memberCount: belief.playerList.length,
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有管理人员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        managerIds,
        {
          eventName: 'belief_member_left',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed member left notification to ${managerIds.length} managers: ${belief.name}`);
    } catch (error) {
      this.logger.error(`Failed to push member left notification: ${error.message}`);
    }
  }

  /**
   * 推送信仰升级通知给全体成员
   */
  private async pushBeliefLevelUpToAllMembers(belief: any, newLevel: number): Promise<void> {
    try {
      // 获取所有成员ID列表
      const memberIds = belief.playerList.map((member: any) => member.playerId);

      if (memberIds.length === 0) return;

      const notificationData = {
        type: 'belief_level_up',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        newLevel,
        levelUpTime: Date.now(),
        message: `恭喜！信仰"${belief.name}"升级到${newLevel}级`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有成员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        memberIds,
        {
          eventName: 'belief_level_up',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed level up notification to ${memberIds.length} members: ${belief.name} -> Level ${newLevel}`);
    } catch (error) {
      this.logger.error(`Failed to push level up notification: ${error.message}`);
    }
  }

  /**
   * 推送信仰解散通知给全体成员
   */
  private async pushBeliefDisbandedToAllMembers(belief: any): Promise<void> {
    try {
      // 获取所有成员ID列表
      const memberIds = belief.playerList.map((member: any) => member.playerId);

      if (memberIds.length === 0) return;

      const notificationData = {
        type: 'belief_disbanded',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        disbandTime: Date.now(),
        message: `信仰"${belief.name}"已被解散`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有成员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        memberIds,
        {
          eventName: 'belief_disbanded',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed disband notification to ${memberIds.length} members: ${belief.name}`);
    } catch (error) {
      this.logger.error(`Failed to push disband notification: ${error.message}`);
    }
  }

  /**
   * 推送新申请通知给管理人员
   */
  private async pushNewApplicationToManagers(belief: any, applicant: any, message: string): Promise<void> {
    try {
      // 获取管理人员ID列表（董事长、副董事长、理事）
      const managerIds = belief.leader
        .filter((leader: any) => leader.pos <= 3) // 1-董事长，2-副董事长，3-理事
        .map((leader: any) => leader.uid);

      if (managerIds.length === 0) return;

      const notificationData = {
        type: 'belief_new_application',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        applicant: {
          playerId: applicant.characterId,
          playerName: applicant.name,
          avatar: applicant.avatar || '',
          applyTime: Date.now(),
          message
        },
        pendingCount: belief.getPendingApplications().length,
        timestamp: new Date()
      };

      // 🎯 优化：推送给所有管理人员（使用角色ID）
      await this.pushClientService.pushToCharacters(
        managerIds,
        {
          eventName: 'belief_new_application',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed new application notification to ${managerIds.length} managers: ${belief.name}`);
    } catch (error) {
      this.logger.error(`Failed to push new application notification: ${error.message}`);
    }
  }

  /**
   * 推送申请通过通知给申请者
   */
  private async pushApplicationApprovedToApplicant(applicantId: string, belief: any, reason: string): Promise<void> {
    try {
      const notificationData = {
        type: 'belief_application_approved',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        approveTime: Date.now(),
        reason,
        message: `恭喜！您的信仰"${belief.name}"申请已通过`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给申请者（使用角色ID）
      await this.pushClientService.pushToCharacter(
        applicantId,
        {
          eventName: 'belief_application_approved',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed application approved notification to: ${applicantId}`);
    } catch (error) {
      this.logger.error(`Failed to push application approved notification: ${error.message}`);
    }
  }

  /**
   * 推送申请拒绝通知给申请者
   */
  private async pushApplicationRejectedToApplicant(applicantId: string, belief: any, reason: string): Promise<void> {
    try {
      const notificationData = {
        type: 'belief_application_rejected',
        beliefId: belief.beliefId,
        beliefName: belief.name,
        rejectTime: Date.now(),
        reason,
        message: `很遗憾，您的信仰"${belief.name}"申请被拒绝`,
        timestamp: new Date()
      };

      // 🎯 优化：推送给申请者（使用角色ID）
      await this.pushClientService.pushToCharacter(
        applicantId,
        {
          eventName: 'belief_application_rejected',
          payload: notificationData,
          businessType: 'belief'
        }
      );

      this.logger.debug(`📤 Pushed application rejected notification to: ${applicantId}`);
    } catch (error) {
      this.logger.error(`Failed to push application rejected notification: ${error.message}`);
    }
  }
}
