import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';

// Character相关组件
import { CharacterController } from './character.controller';
import { CharacterService } from './character.service';
import { EnergyRecoveryService } from './services/energy-recovery.service';
import { DailyEnergyService } from './services/daily-energy.service';
import { Character, CharacterSchema } from '@character/common/schemas/character.schema';
import { CharacterRepository } from '@character/common/repositories/character.repository';

// 导入Formation模块（使用forwardRef解决循环依赖）
import { LineupModule } from '../lineup/lineup.module';
import { HeroModule } from '../hero/hero.module';

// 导入通用模块
import { PushEventsHelper } from '@libs/common/push';

/**
 * 角色模块
 */
@Module({
  imports: [
    // 注册Character Schema
    MongooseModule.forFeature([
      { name: Character.name, schema: CharacterSchema },
    ]),
    // 定时任务模块
    ScheduleModule.forRoot(),
    // 使用forwardRef解决循环依赖问题
    forwardRef(() => LineupModule),
    forwardRef(() => HeroModule),
  ],

  controllers: [CharacterController],

  providers: [
    CharacterService,
    CharacterRepository,
    EnergyRecoveryService,
    DailyEnergyService,
    PushEventsHelper,
  ],

  exports: [
    CharacterService,
    CharacterRepository,
    EnergyRecoveryService,
    DailyEnergyService,
  ],
})
export class CharacterModule {}
