import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse, XResultUtils } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { CharacterOrchestratorService } from '../services/character-orchestrator.service';
import {
  CompleteCharacterInfoDto,
  InitializeCharacterDto,
  InitializeCharacterResultDto
} from '../dto/orchestrator.dto';
import {
  GetCompleteCharacterInfoPayloadDto,
  InitializeCharacterPayloadDto,
  OrchestrateCharacterLoginPayloadDto,
  CheckCharacterDataIntegrityPayloadDto,
  BatchGetCompleteCharacterInfoPayloadDto,
  SyncCharacterDataPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 角色聚合控制器
 * 提供跨模块的角色相关业务接口
 * 
 * 🎯 接口职责：
 * - 角色完整信息聚合接口
 * - 角色初始化业务编排接口
 * - 角色登录业务编排接口
 * - 角色数据同步接口
 * 
 * 🔄 业务场景：
 * - 客户端登录时获取完整角色信息
 * - 新角色创建时的完整初始化流程
 * - 角色数据不一致时的修复流程
 * - 跨模块业务操作的统一入口
 */
@Controller()
export class CharacterOrchestratorController extends BaseController {
  constructor(
    private readonly characterOrchestratorService: CharacterOrchestratorService,
  ) {
    super('CharacterOrchestratorController');
  }

  /**
   * 获取角色完整信息
   * 聚合所有子模块的角色相关数据
   *
   * @param payload 包含characterId和可选的数据范围配置
   * @returns 完整的角色信息，包括基础数据、阵容、英雄、背包等
   */
  @MessagePattern('orchestrator.character.getCompleteInfo')
  async getCompleteInfo(@Payload() payload: GetCompleteCharacterInfoPayloadDto): Promise<XResponse<CompleteCharacterInfoDto>> {
    const { characterId, ...options } = payload;
    const result = await this.characterOrchestratorService.getCompleteInfo(
      characterId,
      options
    );

    return this.fromResult(result);
  }

  /**
   * 角色初始化
   * 确保角色在所有子系统中都有完整的数据结构
   *
   * @param payload 角色初始化参数
   * @returns 初始化结果摘要
   */
  @MessagePattern('orchestrator.character.initialize')
  async initialize(@Payload() payload: InitializeCharacterPayloadDto): Promise<XResponse<InitializeCharacterResultDto>> {
    const initDto: InitializeCharacterDto = {
      characterId: payload.characterId,
      userId: payload.userId,
      serverId: payload.serverId,
      forceReinit: payload.forceReinit
    };

    const result = await this.characterOrchestratorService.initialize(
      initDto.characterId
    );

    return this.fromResult(result);
  }

  /**
   * 角色登录业务编排
   * 在角色登录时进行数据完整性检查和补偿初始化
   *
   * @param payload 登录参数
   * @returns 增强的登录结果，包含完整角色信息
   */
  @MessagePattern('orchestrator.character.login')
  async login(@Payload() payload: OrchestrateCharacterLoginPayloadDto): Promise<XResponse<any>> {
    const { characterId, userId, sessionId } = payload;
    const serverId = payload.serverId;
    const result = await this.characterOrchestratorService.login(
      characterId,
      userId,
      serverId,
      sessionId
    );

    return this.fromResult(result);
  }

  /**
   * 角色数据完整性检查
   * 检查角色在各子系统中的数据完整性，并执行必要的修复
   *
   * @param payload 包含characterId
   * @returns 检查和修复结果
   */
  @MessagePattern('orchestrator.character.checkIntegrity')
  async checkDataIntegrity(@Payload() payload: CheckCharacterDataIntegrityPayloadDto): Promise<XResponse<{
    characterId: string;
    integrityStatus: {
      character: boolean;
      lineup: boolean;
      heroes: boolean;
      inventory: boolean;
      belief: boolean;
      tactics: boolean;
    };
    repairedSystems: string[];
    issues: string[];
  }>> {
    try {
      this.logger.log(`角色数据完整性检查: ${payload.characterId}`);
      
      const { characterId, autoRepair = true } = payload;
      
      // 检查各子系统数据完整性
      const integrityStatus = {
        character: false,
        lineup: false,
        heroes: false,
        inventory: false,
        belief: false,
        tactics: false
      };
      
      const issues: string[] = [];
      let repairedSystems: string[] = [];

      try {
        // 检查各子系统数据完整性
        try {
          // 检查角色基础数据
          const characterResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: false,
            includeHeroes: false,
            includeInventory: false,
            includeBelief: false,
            includeTactics: false,
            includeBattlePower: false
          });
          integrityStatus.character = XResultUtils.isSuccess(characterResult);
          if (!integrityStatus.character) {
            issues.push('角色基础数据缺失或损坏');
          }

          // 检查阵容数据
          const lineupResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: true,
            includeHeroes: false,
            includeInventory: false,
            includeBelief: false,
            includeTactics: false,
            includeBattlePower: false
          });
          integrityStatus.lineup = XResultUtils.isSuccess(lineupResult) && lineupResult.data?.lineups;
          if (!integrityStatus.lineup) {
            issues.push('阵容数据缺失或损坏');
          }

          // 检查英雄数据
          const heroResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: false,
            includeHeroes: true,
            includeInventory: false,
            includeBelief: false,
            includeTactics: false,
            includeBattlePower: false
          });
          integrityStatus.heroes = XResultUtils.isSuccess(heroResult) && (heroResult.data?.heroCount || 0) > 0;
          if (!integrityStatus.heroes) {
            issues.push('英雄数据缺失');
          }

          // 检查背包数据
          const inventoryResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: false,
            includeHeroes: false,
            includeInventory: true,
            includeBelief: false,
            includeTactics: false,
            includeBattlePower: false
          });
          integrityStatus.inventory = XResultUtils.isSuccess(inventoryResult) && inventoryResult.data?.inventorySummary;
          if (!integrityStatus.inventory) {
            issues.push('背包数据缺失或损坏');
          }

          // 检查信念数据
          const beliefResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: false,
            includeHeroes: false,
            includeInventory: false,
            includeBelief: true,
            includeTactics: false,
            includeBattlePower: false
          });
          integrityStatus.belief = XResultUtils.isSuccess(beliefResult) && beliefResult.data?.beliefInfo;
          if (!integrityStatus.belief) {
            issues.push('信念数据缺失');
          }

          // 检查战术数据
          const tacticResult = await this.characterOrchestratorService.getCompleteInfo(characterId, {
            includeLineup: false,
            includeHeroes: false,
            includeInventory: false,
            includeBelief: false,
            includeTactics: true,
            includeBattlePower: false
          });
          integrityStatus.tactics = XResultUtils.isSuccess(tacticResult) && tacticResult.data?.tacticInfo;
          if (!integrityStatus.tactics) {
            issues.push('战术数据缺失');
          }

          // 执行自动修复
          if (autoRepair && issues.length > 0) {
            const initResult = await this.characterOrchestratorService.initialize(
              characterId
            );

            if (XResultUtils.isSuccess(initResult)) {
              repairedSystems = initResult.data.initSteps;
            }
          }
        } catch (error) {
          this.logger.error('数据完整性检查过程中发生错误', error);
          issues.push('检查过程中发生系统错误');
        }

        const result = {
          characterId,
          integrityStatus,
          repairedSystems,
          issues
        };

        return this.toSuccessResponse(result, '数据完整性检查完成');
      } catch (error) {
        this.logger.error('数据完整性检查失败', error);
        return this.toErrorResponse('数据完整性检查失败', 'DATA_INTEGRITY_CHECK_FAILED');
      }
    } catch (error) {
      this.logger.error('数据完整性检查失败', error);
      return this.toErrorResponse('数据完整性检查失败', 'DATA_INTEGRITY_CHECK_FAILED');
    }
  }
}
