import { IsString, IsNumber, IsOptional, IsArray, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

// ==================== 角色聚合DTO ====================

/**
 * 角色完整信息DTO
 * 聚合character、formation、hero、inventory等模块数据
 */
export class CompleteCharacterInfoDto {
  // 基础角色信息
  characterId: string;
  name: string;
  avatar: string;
  faceIcon: number;
  level: number;
  
  // 资源信息
  cash: number;
  gold: number;
  energy: number;
  fame: number;
  trophy: number;
  worldCoin: number;
  chip: number;
  integral: number;
  
  // VIP信息
  vip: number;
  vipExp: number;
  
  // 游戏进度
  isNewer: boolean;
  createRoleStep: number;
  activeDay: number;
  
  // 聚合数据
  lineups?: any;      // 当前阵容信息
  heroCount?: number;          // 英雄数量
  totalBattlePower?: number;   // 总战力
  inventorySummary?: any;      // 背包摘要
  beliefInfo?: any;            // 信念信息
  tacticInfo?: any;            // 战术信息
}

/**
 * 角色初始化请求DTO
 */
export class InitializeCharacterDto {
  @IsString()
  characterId: string;

  @IsString()
  userId: string;

  @IsString()
  serverId: string;

  @IsOptional()
  @IsBoolean()
  forceReinit?: boolean;
}

/**
 * 角色初始化结果DTO
 */
export class InitializeCharacterResultDto {
  character: any;              // 角色基础数据
  lineups: any;                // 阵容数据
  inventory: any;              // 背包数据
  tactics: any;                // 战术数据
  initialHeroes?: any[];       // 初始球员数据
  initSteps: string[];         // 初始化步骤
}

// ==================== 奖励聚合DTO ====================

/**
 * 奖励项目DTO
 */
export class RewardItemDto {
  @IsString()
  type: string;                // 奖励类型：currency, item, hero

  @IsString()
  subType: string;             // 子类型：cash, gold, energy 或 具体物品ID

  @IsNumber()
  amount: number;              // 数量

  @IsOptional()
  @IsString()
  reason?: string;             // 奖励原因
}

/**
 * 批量奖励发放请求DTO
 */
export class BatchRewardDto {
  @IsString()
  characterId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RewardItemDto)
  rewards: RewardItemDto[];

  @IsOptional()
  @IsString()
  reason?: string;             // 整体原因
}

/**
 * 奖励发放结果DTO
 */
export class RewardResultDto {
  characterId: string;
  totalRewards: number;        // 总奖励数量
  currencyRewards: any[];      // 货币奖励结果
  itemRewards: any[];          // 物品奖励结果
  heroRewards: any[];          // 英雄奖励结果
  failedRewards: any[];        // 失败的奖励
}

// ==================== 战力聚合DTO ====================

/**
 * 战力计算请求DTO
 */
export class BattlePowerCalculationDto {
  @IsString()
  characterId: string;

  @IsOptional()
  @IsString()
  formationId?: string;        // 指定阵容ID，不指定则使用当前阵容

  @IsOptional()
  @IsBoolean()
  includeEquipment?: boolean;  // 是否包含装备加成

  @IsOptional()
  @IsBoolean()
  includeTactics?: boolean;    // 是否包含战术加成

  @IsOptional()
  @IsBoolean()
  includeBelief?: boolean;     // 是否包含信念加成
}

/**
 * 战力计算结果DTO
 */
export class BattlePowerResultDto {
  characterId: string;
  formationId: string;
  totalBattlePower: number;    // 总战力
  
  // 战力构成
  heroBattlePower: number;     // 英雄基础战力
  equipmentBonus: number;      // 装备加成
  tacticBonus: number;         // 战术加成
  beliefBonus: number;         // 信念加成
  formationBonus: number;      // 阵型加成
  
  // 详细信息
  heroDetails: any[];          // 英雄战力详情
  equipmentDetails: any[];     // 装备详情
  tacticDetails: any[];        // 战术详情
  beliefDetails: any;          // 信念详情
}

// ==================== 阵容聚合DTO ====================

/**
 * 智能布阵请求DTO
 */
export class SmartFormationDto {
  @IsString()
  characterId: string;

  @IsString()
  formationId: string;

  @IsOptional()
  @IsArray()
  preferredHeroes?: string[];  // 优先使用的英雄ID列表

  @IsOptional()
  @IsString()
  strategy?: string;           // 布阵策略：attack, defense, balanced

  @IsOptional()
  @IsBoolean()
  autoEquip?: boolean;         // 是否自动装备
}

/**
 * 智能布阵结果DTO
 */
export class SmartFormationResultDto {
  characterId: string;
  formationId: string;
  formation: any;              // 更新后的阵容
  positionMapping: any;        // 位置映射
  unassignedHeroes: any[];     // 未分配的英雄
  equipmentChanges: any[];     // 装备变更
  battlePowerChange: number;   // 战力变化
  recommendations: string[];   // 优化建议
}
