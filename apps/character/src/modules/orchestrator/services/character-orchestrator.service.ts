import {Injectable, Inject, forwardRef} from "@nestjs/common";
import {BaseService} from "@libs/common/service";
import {MicroserviceClientService} from "@libs/service-mesh";
import {XResult, XResultUtils} from "@libs/common/types/result.type";
import {GameConfigFacade} from "@libs/game-config";

// 导入各个子模块服务
import {CharacterService} from "../../character/character.service";
import {LineupService} from "../../lineup/lineup.service";
import {HeroService} from "../../hero/services/hero.service";
import {InventoryService} from "../../inventory/inventory.service";
import {BeliefService} from "../../belief/belief.service";
import {TacticService} from "../../tactic/tactic.service";

// 导入DTO
import {
  CompleteCharacterInfoDto,
  InitializeCharacterDto,
  InitializeCharacterResultDto,
} from "../dto/orchestrator.dto";

/**
 * 角色聚合服务 - 角色相关跨模块业务编排
 *
 * 🎯 核心职责：
 * - 角色完整信息聚合（基础数据 + 阵容 + 英雄 + 背包 + 信念 + 战术）
 * - 角色初始化业务编排（并行初始化各子系统）
 * - 角色登录业务编排（数据完整性检查 + 补偿初始化）
 *
 * 🚀 性能优化：
 * - 并行调用子模块服务，减少总响应时间
 * - 智能缓存聚合结果，避免重复计算
 * - 按需加载数据，支持部分数据获取
 * - 事务支持，确保初始化过程的原子性
 *
 * 🔄 业务编排模式：
 * - 基础数据优先：先确保character基础数据存在
 * - 并行初始化：lineup、inventory、tactic可并行初始化
 * - 依赖检查：hero依赖character，belief依赖character
 * - 补偿机制：失败时支持部分回滚和重试
 */
@Injectable()
export class CharacterOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    @Inject(forwardRef(() => LineupService))
    private readonly lineupService: LineupService,
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    @Inject(forwardRef(() => BeliefService))
    private readonly beliefService: BeliefService,
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient?: MicroserviceClientService
  ) {
    super("CharacterOrchestratorService", microserviceClient);
  }

  /**
   * 获取角色完整信息
   * 聚合所有子模块的角色相关数据，提供统一的角色信息视图
   *
   * 🔄 业务编排流程：
   * 1. 获取角色基础信息（必需）
   * 2. 并行获取各子系统数据（可选）
   * 3. 聚合数据并计算衍生字段
   * 4. 返回完整的角色信息
   */
  async getCompleteInfo(
    characterId: string,
    options: {
      includeLineup?: boolean;
      includeHeroes?: boolean;
      includeInventory?: boolean;
      includeBelief?: boolean;
      includeTactics?: boolean;
      includeBattlePower?: boolean;
    } = {}
  ): Promise<XResult<CompleteCharacterInfoDto>> {
    return this.executeBusinessOperation(
      async () => {
        this.logger.log(`获取角色完整信息: ${characterId}`, {options});

        // 1. 获取角色
        let characterResult = await this.characterService.getCharacter(characterId);
        if (XResultUtils.isFailure(characterResult)) {
          return XResultUtils.error(
            `获取角色基础信息失败: ${characterResult.message}`,
            characterResult.code
          );
        }
        
        // 2. 检查角色是否需要初始化
        const character = characterResult.data;
        let characterBaseInfo: any;
        let isNewCharacter = false;

        if (!character) {
          this.logger.log(`角色不存在: ${characterId}, 需要初始化`);
          isNewCharacter = true;

          const initResult = await this.initialize(characterId);

          if (XResultUtils.isFailure(initResult)) {
            return XResultUtils.error(
              `角色初始化失败: ${initResult.message}`,
              initResult.code
            );
          }

          // 🎯 性能优化：新角色直接使用初始化返回的数据，避免重复查询
          const initData = initResult.data;
          characterBaseInfo = this.convertCharacterToDto(initData.character);

          // 新角色直接返回完整的初始化数据，忽略可选参数
          const completeInfo: CompleteCharacterInfoDto = {
            ...characterBaseInfo,
            lineups: initData.lineups,
            heroCount: (initData.initialHeroes || []).length,
            inventorySummary: this.buildInventorySummary(initData.inventory),
            tacticInfo: initData.tactics,
          };

          this.logger.log(`✅ 新角色初始化完成，直接返回完整数据: ${characterId}`, {
            heroCount: completeInfo.heroCount,
            hasLineup: !!completeInfo.lineups,
            hasInventory: !!completeInfo.inventorySummary,
            hasTactics: !!completeInfo.tacticInfo,
          });

          return XResultUtils.ok(completeInfo);
        }

        // 3. 现有角色，获取角色信息
        const characterInfoResult = await this.characterService.getCharacterInfo(characterId);
        if (XResultUtils.isFailure(characterInfoResult)) {
          return XResultUtils.error(
            `获取角色基础信息失败: ${characterInfoResult.message}`,
            characterInfoResult.code
          );
        }

        characterBaseInfo = characterInfoResult.data;
        const completeInfo: CompleteCharacterInfoDto = {
          // 复制基础角色信息
          ...characterBaseInfo,
        };

        // 4. 并行获取各子系统数据（按需加载）
        const promises: Promise<any>[] = [];
        const dataKeys: string[] = [];

        if (options.includeLineup !== false) {
          promises.push(
            this.lineupService.getLineups(characterId)
          );
          dataKeys.push("lineup");
        }

        if (options.includeHeroes !== false) {
          promises.push(this.heroService.getHeroes(characterId));
          dataKeys.push("heroes");
        }

        if (options.includeInventory !== false) {
          promises.push(this.inventoryService.getInventory(characterId));
          dataKeys.push("inventory");
        }

        if (options.includeBelief !== false) {
          promises.push(this.beliefService.getBelief({characterId}));
          dataKeys.push("belief");
        }

        if (options.includeTactics !== false) {
          promises.push(this.tacticService.getTactics(characterId));
          dataKeys.push("tactics");
        }

        // 3. 等待所有并行请求完成
        const results = await Promise.allSettled(promises);

        // 4. 处理各子系统数据
        results.forEach((result, index) => {
          const dataKey = dataKeys[index];

          if (
            result.status === "fulfilled" &&
            XResultUtils.isSuccess(result.value)
          ) {
            switch (dataKey) {
              case "lineup":
                completeInfo.lineups = result.value.data;
                break;
              case "heroes":
                const heroes = result.value.data || [];
                completeInfo.heroCount = (heroes as any[]).length;
                break;
              case "inventory":
                completeInfo.inventorySummary = this.buildInventorySummary(
                  result.value.data
                );
                break;
              case "belief":
                completeInfo.beliefInfo = result.value.data;
                break;
              case "tactics":
                completeInfo.tacticInfo = result.value.data;
                break;
            }
          } else {
            this.logger.warn(`获取${dataKey}数据失败`, {
              characterId,
              error:
                result.status === "rejected" ? result.reason : "Unknown error",
            });
          }
        });

        // 5. 计算总战力（如果需要）
        if (options.includeBattlePower !== false) {
          const battlePowerResult =
            await this.calculateTotalBattlePower(characterId);
          if (XResultUtils.isSuccess(battlePowerResult)) {
            completeInfo.totalBattlePower = battlePowerResult.data;
          }
        }

        this.logger.log(`角色完整信息获取成功: ${characterId}`, {
          heroCount: completeInfo.heroCount,
          totalBattlePower: completeInfo.totalBattlePower,
        });

        return XResultUtils.ok(completeInfo);
      },
      {
        reason: "get_complete_character_info",
        metadata: {characterId, options},
      }
    );
  }

  /**
   * 角色初始化业务编排
   * 确保角色在所有子系统中都有完整的数据结构
   *
   * 🔄 业务编排流程：
   * 1. 初始化角色基础数据（优先级最高）
   * 2. 并行初始化独立子系统（lineup、inventory、tactic）
   * 3. 初始化依赖子系统（hero依赖character）
   * 4. 创建初始球员和阵容（新角色）
   * 5. 验证初始化完整性
   * 6. 返回初始化结果摘要
   *
   * 🔒 事务保证：使用事务确保所有子系统初始化的原子性
   */
  async initialize(
    characterId: string,
    forceReinit?: boolean
  ): Promise<XResult<InitializeCharacterResultDto>> {
    const initStartTime = Date.now();
    this.logger.log(`🚀 角色初始化开始: ${characterId}`, {
      forceReinit,
      timestamp: new Date().toISOString()
    });

    // 使用事务确保初始化的原子性
    return this.executeTransaction(
      async (session) => {
        const initSteps: string[] = [];
        let initialHeroes: any[] = [];

        // 1. 初始化角色基础数据（必须优先完成，传递session确保事务一致性）
        this.logger.log(`📋 步骤1: 开始初始化角色基础数据: ${characterId}`);
        const characterStartTime = Date.now();

        const characterResult = await this.characterService.initializeBaseWithSession(characterId, session);
        const characterDuration = Date.now() - characterStartTime;

        if (XResultUtils.isFailure(characterResult)) {
          this.logger.error(`❌ 步骤1失败: 角色基础数据初始化失败: ${characterId}, 耗时: ${characterDuration}ms`, characterResult.message);
          return XResultUtils.error(
            `角色基础数据初始化失败: ${characterResult.message}`,
            characterResult.code
          );
        }
        this.logger.log(`✅ 步骤1完成: 角色基础数据初始化成功: ${characterId}, 耗时: ${characterDuration}ms`);
        initSteps.push("character_base");

        // 2. 并行初始化独立子系统（传递session确保事务一致性）
        // 🎯 性能优化：使用Promise.all替代Promise.allSettled，快速失败机制
        this.logger.log(`📋 步骤2: 开始并行初始化子系统: ${characterId}`);
        const parallelStartTime = Date.now();

        // 声明变量在try块外面，确保作用域正确
        let lineups: any = null;
        let inventory: any = null;
        let tactics: any = null;

        // 🎯 事务一致性优化：改为串行执行，确保事务完整性
        // Promise.all可能导致部分操作成功、部分失败的不一致状态

        this.logger.log(`📋 步骤2.1: 初始化阵容系统: ${characterId}`);
        const lineupResult = await this.lineupService.initializeLineupsWithSession(characterId, session);
        if (XResultUtils.isFailure(lineupResult)) {
          return XResultUtils.error(`阵容初始化失败: ${lineupResult.message}`, 'LINEUP_INIT_FAILED');
        }

        this.logger.log(`📋 步骤2.2: 初始化背包系统: ${characterId}`);
        const inventoryResult = await this.inventoryService.initializeInventoryWithSession(characterId, session);
        if (XResultUtils.isFailure(inventoryResult)) {
          return XResultUtils.error(`背包初始化失败: ${inventoryResult.message}`, 'INVENTORY_INIT_FAILED');
        }

        this.logger.log(`📋 步骤2.3: 初始化战术系统: ${characterId}`);
        const tacticResult = await this.tacticService.initializeTacticsWithSession(characterId, session);
        if (XResultUtils.isFailure(tacticResult)) {
          return XResultUtils.error(`战术初始化失败: ${tacticResult.message}`, 'TACTIC_INIT_FAILED');
        }

        const parallelDuration = Date.now() - parallelStartTime;
        this.logger.log(`✅ 步骤2完成: 串行初始化完成: ${characterId}, 耗时: ${parallelDuration}ms`);

        // 提取数据（此时所有结果都已验证为成功）
        lineups = lineupResult.data;
        inventory = inventoryResult.data;
        tactics = tacticResult.data;

        initSteps.push("lineups", "inventory", "tactics");
        this.logger.log(`✅ 所有子系统初始化成功: ${characterId}`);

        // 3. 创建初始球员
        this.logger.log(`开始创建初始球员: ${characterId}`);

        // 3.1 创建初始球员（传递session以确保事务一致性）
        const initialHeroesResult = await this.createInitialHeroesWithSession(characterId, session);
        if (XResultUtils.isSuccess(initialHeroesResult)) {
          initialHeroes = initialHeroesResult.data || [];
          initSteps.push('initial_heroes');
          this.logger.log(`初始球员创建成功: ${characterId}, 数量: ${initialHeroes.length}`);
        } else {
          this.logger.error(`初始球员创建失败: ${characterId}`, initialHeroesResult.message);
          // 初始球员创建失败应该回滚事务
          return XResultUtils.error(`初始球员创建失败: ${initialHeroesResult.message}`, initialHeroesResult.code);
        }

        initSteps.push("new_character_setup");

        const result: InitializeCharacterResultDto = {
          character: characterResult.data,
          lineups: lineups,
          inventory,
          tactics,
          initialHeroes, // 添加初始球员信息
          initSteps,
        };

        const totalDuration = Date.now() - initStartTime;
        this.logger.log(`🎉 角色初始化完成: ${characterId}`, {
          initSteps: initSteps.length,
          completedSteps: initSteps,
          heroCount: initialHeroes.length,
          totalDuration: `${totalDuration}ms`,
          performance: totalDuration < 30000 ? '✅ 优秀' : totalDuration < 60000 ? '⚠️ 一般' : '❌ 需优化'
        });

        return XResultUtils.ok(result);
      },
      {
        timeout: 60000, // 角色初始化需要更长的超时时间
        maxRetries: 2,
        operationDescription: "角色初始化编排",
        enableDetailedLogging: true,
      }
    );
  }

  /**
   * 构建背包摘要信息
   * 提取背包的关键统计信息，避免返回完整的物品列表
   */
  private buildInventorySummary(inventory: any): any {
    if (!inventory) return null;

    return {
      totalItems: inventory.items?.length || 0,
      totalSlots: inventory.maxSlots || 0,
      usedSlots: inventory.usedSlots || 0,
      freeSlots: (inventory.maxSlots || 0) - (inventory.usedSlots || 0),
      // 可以添加更多摘要信息，如物品类型统计等
    };
  }

  /**
   * 计算总战力
   * 聚合hero、lineup、inventory、tactic等模块的战力贡献
   */
  private async calculateTotalBattlePower(
    characterId: string
  ): Promise<XResult<number>> {
    try {
      let totalBattlePower = 0;

      // 1. 获取英雄战力
      const heroesResult =
        await this.heroService.getHeroes(characterId);
      if (XResultUtils.isSuccess(heroesResult)) {
        const heroes = heroesResult.data || [];
        heroes.forEach((hero) => {
          // 基于英雄等级、星级、属性计算战力
          const heroLevel = hero.level || 1;
          const heroStar = hero.star || 1;
          const baseAttributes =
            hero.attack + hero.defense + hero.speed + hero.goalkeeping;
          const heroBattlePower =
            heroLevel * 100 + heroStar * 500 + baseAttributes * 10;
          totalBattlePower += heroBattlePower;
        });
      }

      // 2. 获取阵容加成
      const lineupsResult = await this.lineupService.getLineups(characterId);
      if (XResultUtils.isSuccess(lineupsResult) && lineupsResult.data) {
        const lineups = lineupsResult.data;
        const lineupBonus = Math.floor(
          ((lineups as any).attack + (lineups as any).defend) * 0.1
        );
        totalBattlePower += lineupBonus;
      }

      // 3. 获取装备加成
      const inventoryResult =
        await this.inventoryService.getInventory(characterId);
      if (XResultUtils.isSuccess(inventoryResult)) {
        const equippedItems = inventoryResult.data.items.filter(
          (item) =>
            (item as any).itemType === "equipment" && (item as any).isEquipped
        );
        equippedItems.forEach((item) => {
          const equipmentBonus = ((item as any).level || 1) * 200;
          totalBattlePower += equipmentBonus;
        });
      }

      // 4. 获取战术加成
      const tacticResult =
        await this.tacticService.getTactics(characterId);
      if (XResultUtils.isSuccess(tacticResult) && tacticResult.data?.tactics) {
        tacticResult.data.tactics.forEach((tactic) => {
          const tacticBonus = (tactic.level || 1) * 150;
          totalBattlePower += tacticBonus;
        });
      }

      // 5. 获取信念加成
      const beliefResult = await this.beliefService.getBelief({
        characterId,
      });
      if (XResultUtils.isSuccess(beliefResult) && beliefResult.data) {
        const beliefBonus = (beliefResult.data.level || 1) * 300;
        totalBattlePower += beliefBonus;
      }

      return XResultUtils.ok(Math.floor(totalBattlePower));
    } catch (error) {
      this.logger.error("计算总战力失败", error);
      return XResultUtils.error(
        "计算总战力失败",
        "CALCULATE_BATTLE_POWER_FAILED"
      );
    }
  }

  /**
   * 角色登录业务编排
   * 在角色登录时进行数据完整性检查和补偿初始化
   *
   * 🔄 业务编排流程：
   * 1. 执行角色登录逻辑
   * 2. 检查各子系统数据完整性
   * 3. 执行补偿初始化（如果需要）
   * 4. 返回完整的登录结果
   */
  async login(
    characterId: string,
    userId: string,
    serverId: string,
    sessionId: string
  ): Promise<XResult<any>> {
    return this.executeBusinessOperation(
      async () => {
        this.logger.log(`角色登录业务编排: ${characterId}`);

        // 1. 执行基础登录逻辑
        const loginResult = await this.characterService.loginCharacter({
          characterId,
          userId,
          serverId,
          sessionId,
        });

        if (XResultUtils.isFailure(loginResult)) {
          return loginResult;
        }

        // 2. 检查数据完整性并执行补偿初始化
        const integrityCheckResult =
          await this.checkAndRepairDataIntegrity(characterId);
        if (XResultUtils.isFailure(integrityCheckResult)) {
          this.logger.warn(
            `数据完整性检查失败: ${integrityCheckResult.message}`
          );
        }

        // 3. 获取完整角色信息
        const completeInfoResult = await this.getCompleteInfo(characterId, {
          includeLineup: true,
          includeHeroes: true,
          includeInventory: true,
          includeBelief: false, // 登录时不需要信念信息
          includeTactics: false, // 登录时不需要战术信息
          includeBattlePower: true,
        });

        if (XResultUtils.isSuccess(completeInfoResult)) {
          // 合并登录结果和完整信息
          const enhancedResult = {
            ...loginResult.data,
            completeCharacterInfo: completeInfoResult.data,
          };
          return XResultUtils.ok(enhancedResult);
        }

        // 如果获取完整信息失败，仍返回基础登录结果
        return loginResult;
      },
      {
        reason: "orchestrate_character_login",
        metadata: {characterId, userId},
      }
    );
  }

  /**
   * 创建初始球员（支持事务）
   * 基于old项目createInitBallerAndTeam逻辑，为新角色创建初始球员
   *
   * 🔄 业务流程：
   * 1. 获取角色信息以确定资质
   * 2. 调用HeroService创建初始球员（传递session）
   * 3. 验证球员数量（至少11个）
   * 4. 返回创建的球员列表
   *
   * @param characterId 角色ID
   * @param session 数据库事务session
   */
  private async createInitialHeroesWithSession(characterId: string, session: any): Promise<XResult<any[]>> {
    this.logger.log(`开始创建初始球员: ${characterId}`);

    // 获取角色信息以确定资质
    const characterResult = await this.characterService.getCharacter(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
    }

    const character = characterResult.data;
    const qualified = character?.qualified || 50; // 默认资质50

    // 调用HeroService创建初始球员（传递session以确保事务一致性）
    const heroesResult = await this.heroService.createInitialHeroesWithSession(characterId, qualified, session);

    if (XResultUtils.isSuccess(heroesResult)) {
      const heroes = (heroesResult.data as any[]) || [];
      this.logger.log(`初始球员创建完成: ${characterId}, 成功创建: ${heroes.length}个`);

      // 验证球员数量
      if (heroes.length < 11) {
        this.logger.warn(`初始球员数量不足: ${heroes.length}/11, 但允许继续`);
      }

      return XResultUtils.ok(heroes);
    } else {
      this.logger.error(`初始球员创建失败: ${characterId}`, heroesResult.message);
      return XResultUtils.error(`初始球员创建失败: ${heroesResult.message}`, heroesResult.code);
    }
  }


  /**
   * 检查并修复数据完整性
   * 确保角色在所有必要的子系统中都有数据
   */
  private async checkAndRepairDataIntegrity(
    characterId: string
  ): Promise<XResult<string[]>> {
    const repairedSystems: string[] = [];

    try {
      // 检查并修复阵容数据
      const lineupsResult = await this.lineupService.getLineups(characterId);
      if (XResultUtils.isFailure(lineupsResult) || !lineupsResult.data) {
        const initResult =  await this.lineupService.initializeLineups(characterId);
        if (XResultUtils.isSuccess(initResult)) {
          repairedSystems.push("lineup");
        }
      }

      // 检查并修复背包数据
      const inventoryResult =
        await this.inventoryService.getInventory(characterId);
      if (XResultUtils.isFailure(inventoryResult) || !inventoryResult.data) {
        const initResult =
          await this.inventoryService.initializeInventory(characterId);
        if (XResultUtils.isSuccess(initResult)) {
          repairedSystems.push("inventory");
        }
      }

      // 检查并修复战术数据
      const tacticResult =
        await this.tacticService.getTactics(characterId);
      if (XResultUtils.isFailure(tacticResult) || !tacticResult.data) {
        const initResult =
          await this.tacticService.initializeTactics(characterId);
        if (XResultUtils.isSuccess(initResult)) {
          repairedSystems.push("tactic");
        }
      }

      if (repairedSystems.length > 0) {
        this.logger.log(`数据完整性修复完成: ${characterId}`, {
          repairedSystems,
        });
      }

      return XResultUtils.ok(repairedSystems);
    } catch (error) {
      this.logger.error("数据完整性检查失败", error);
      return XResultUtils.error(
        "数据完整性检查失败",
        "DATA_INTEGRITY_CHECK_FAILED"
      );
    }
  }

  /**
   * 将角色数据转换为CharacterInfoDto格式
   * 避免调用CharacterService的私有方法
   */
  private convertCharacterToDto(character: any): any {
    return {
      characterId: character.characterId,
      name: character.name,
      avatar: character.avatar,
      faceIcon: character.faceIcon,
      faceUrl: character.faceUrl,
      level: character.level,
      cash: character.cash,
      gold: character.gold,
      energy: character.energy,
      fame: character.fame,
      allFame: character.allFame,
      trophy: character.trophy,
      worldCoin: character.worldCoin,
      chip: character.chip,
      integral: character.integral,
      vip: character.vipInfo?.vip || 0,
      vipExp: character.vipInfo?.vipExp || 0,
      fieldLevel: character.fieldLevel,
      league: character.league,
      isOnline: character.isOnline,
      isNewer: character.gameProgress?.isNewer || false,
      createRoleStep: character.gameProgress?.createRoleStep || 0,
      activeDay: character.gameProgress?.activeDay || 0,
      honor: character.honor || 0,
      beliefSkill: character.beliefSkill,
      footballGround: character.footballGround,
    };
  }
}