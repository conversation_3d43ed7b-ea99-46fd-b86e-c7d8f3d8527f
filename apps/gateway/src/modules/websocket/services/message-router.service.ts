import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 微服务通信
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES, MicroserviceName } from '@libs/shared/constants';

// 上下文服务
import { PayloadInjectorService } from '../context/payload-Injector.service';
import {AuthenticatedSocket} from "@gateway/modules/websocket/gateways/websocket.gateway";

// 类型定义
export type RoutingStrategy = 'normal' | 'cross_server' | 'global' | 'system';

export interface RouteResult {
  success: boolean;
  data?: any;
  error?: string;
  strategy: RoutingStrategy;
  executionTime: number;
}

/**
 * 消息路由服务
 * 负责WebSocket消息的策略分析和上下文增强
 *
 * 🚀 优化后的核心功能：
 * 1. 路由策略分析：智能识别cross_server、global、system等特殊路由
 * 2. 上下文增强：通过PayloadEnhancerService注入复杂的上下文信息
 * 3. 消息预处理：为不同路由策略添加特殊标记和前缀处理
 * 4. 执行监控：提供详细的路由策略和执行时间统计
 */
@Injectable()
export class MessageRouterService {
  private readonly logger = new Logger(MessageRouterService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly payloadInjectorService: PayloadInjectorService,
  ) {}

  /**
   * 路由消息到适当的微服务
   * 主要入口方法
   */
  async routeMessage(
    service: string,
    action: string,
    payload: any,
    client: AuthenticatedSocket
  ): Promise<RouteResult> {
    const startTime = Date.now();
    this.logger.debug(`🔍 Routing message: ${service}.${action} for user ${client.userId}`);

    try {
      // 1. 分析路由策略
      const strategy = this.analyzeRoutingStrategy(service, action);
      this.logger.debug(`📋 Routing strategy: ${strategy}`);

      // 2. 增强payload（注入上下文）
      const enhancedPayload = await this.payloadInjectorService.injectPayload(
        payload,
        client,
        strategy
      );

      // 3. 执行路由策略
      const result = await this.executeRoutingStrategy(
        service,
        action,
        enhancedPayload,
        strategy
      );

      const executionTime = Date.now() - startTime;
      this.logger.debug(`✅ Message routed successfully in ${executionTime}ms`);

      return {
        success: true,
        data: result,
        strategy,
        executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(`❌ Message routing failed: ${error.message}`, error);

      return {
        success: false,
        error: error.message,
        strategy: 'normal',
        executionTime,
      };
    }
  }

  /**
   * 分析路由策略
   * 根据service和action决定路由类型
   */
  analyzeRoutingStrategy(service: string, action: string): RoutingStrategy {
    // 跨服消息（cross.前缀）
    if (service.startsWith('cross.') || action.startsWith('cross.')) {
      return 'cross_server';
    }
    
    // 全服消息（global.前缀）
    if (service.startsWith('global.') || action.startsWith('global.')) {
      return 'global';
    }
    
    // 系统消息（system.前缀）
    if (service.startsWith('system.') || action.startsWith('system.')) {
      return 'system';
    }
    
    // 普通区服消息（默认）
    return 'normal';
  }

  /**
   * 执行路由策略
   */
  private async executeRoutingStrategy(
    service: string,
    action: string,
    payload: any,
    strategy: RoutingStrategy
  ): Promise<any> {
    switch (strategy) {
      case 'cross_server':
        return await this.handleCrossServerRoute(service, action, payload);
      case 'global':
        return await this.handleGlobalRoute(service, action, payload);
      case 'system':
        return await this.handleSystemRoute(service, action, payload);
      default:
        return await this.handleNormalRoute(service, action, payload);
    }
  }

  /**
   * 处理普通区服路由（优化：委托给MicroserviceClientService）
   */
  private async handleNormalRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🎯 Handling server-aware route: ${service}.${action}`);

    // 验证服务名称
    if (!this.isValidServiceName(service)) {
      throw new Error(`Invalid service name: ${service}`);
    }

    // 直接使用MicroserviceClientService的智能路由
    const result = await this.microserviceClient.call(
      service as MicroserviceName,
      action,
      payload
    );

    this.logger.debug(`✅ Server-aware route completed successfully`);
    return result;
  }

  /**
   * 处理跨服路由
   */
  private async handleCrossServerRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🌐 Handling cross-server route: ${service}.${action}`);
    
    // 移除cross.前缀
    const actualService = service.replace(/^cross\./, '');
    const actualAction = action.replace(/^cross\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 添加跨服标记
    const crossServerPayload = {
      ...payload,
      _crossServer: true,
      _sourceServer: payload.serverContext?.serverId,
      _messageType: 'cross_server',
    };

    // 调用跨服处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      crossServerPayload
    );

    this.logger.debug(`✅ Cross-server route completed successfully`);
    return result;
  }

  /**
   * 处理全服路由
   */
  private async handleGlobalRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`🌍 Handling global route: ${service}.${action}`);
    
    // 移除global.前缀
    const actualService = service.replace(/^global\./, '');
    const actualAction = action.replace(/^global\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 添加全服标记
    const globalPayload = {
      ...payload,
      _global: true,
      _allServers: true,
      _messageType: 'global',
    };

    // 调用全服处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      globalPayload
    );

    this.logger.debug(`✅ Global route completed successfully`);
    return result;
  }

  /**
   * 处理系统路由
   */
  private async handleSystemRoute(service: string, action: string, payload: any): Promise<any> {
    this.logger.debug(`⚙️ Handling system route: ${service}.${action}`);
    
    // 移除system.前缀
    const actualService = service.replace(/^system\./, '');
    const actualAction = action.replace(/^system\./, '');
    
    // 验证服务名称
    if (!this.isValidServiceName(actualService)) {
      throw new Error(`Invalid service name: ${actualService}`);
    }

    // 系统消息不需要区服上下文
    const systemPayload = {
      ...payload,
      _system: true,
      _messageType: 'system',
    };

    // 调用系统处理服务
    const result = await this.microserviceClient.call(
      actualService as MicroserviceName,
      actualAction,
      systemPayload
    );

    this.logger.debug(`✅ System route completed successfully`);
    return result;
  }

  /**
   * 验证服务名称是否有效
   */
  private isValidServiceName(service: string): boolean {
    const validServices = Object.values(MICROSERVICE_NAMES);
    return validServices.includes(service as MicroserviceName);
  }
}
