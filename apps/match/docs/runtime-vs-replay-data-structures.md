# 运行时与回放数据结构差异说明文档

## 🎯 核心概念澄清

### Round（回合）vs Period（阶段）定义

#### Round（回合）
- **定义**：一次完整的攻防过程，包含3个连续的Period
- **数据结构**：`BattleRound`（schema中的持久化结构）
- **包含内容**：事件时间、双方士气、攻击方、攻击模式、比分、3个Period的详细数据

#### Period（阶段）  
- **定义**：一个Round内的具体执行阶段
- **数量**：每个Round固定包含3个Period
- **阶段类型**：
  - Period 0：发起阶段（100%成功）
  - Period 1：推进阶段（基于双方实力计算成功率）
  - Period 2：射门阶段（射门vs门将，可能进球）

## 📊 数据结构对比分析

### 1. 运行时数据结构（battle-engine使用）

#### BattleTeam.roundInfo（当前项目命名）
```typescript
export interface RoundInfo {
  attackerType?: string;                    // 攻击方标识
  A1Info?: {                                // 主攻球员信息（统一使用Info命名）
    heroId: string;                         // 使用当前项目命名：heroId
    attrType1?: number;
    attrValue1?: number;
    addValue1?: number;
    attrType2?: number;
    attrValue2?: number;
    addValue2?: number;
  };
  A2Info?: {heroId: string; ...};          // 协攻球员信息（统一使用Info命名）
  BInfo?:  {heroId: string; ...};          // 防守球员信息（统一使用Info命名）
  GKInfo?: {heroId: string; ...};          // 门将信息（统一使用Info命名）
  attackMode?: AttackMode;                  // 攻击模式
  periodInfo?: {                            // 3个阶段的临时计算数据
    actionId: number;                       // 使用当前项目命名：actionId
    startCommentId: number;                 // 使用当前项目命名：startCommentId
    resultCommentId: number;                // 使用当前项目命名：resultCommentId
    percent: number;                        // 成功率
    result: number;                         // 结果
    skillEffectList?: any[];                // 技能效果
  }[];
}
```

**用途**：
- battle-engine运行时的临时数据容器
- 存储当前回合参与的球员信息
- 存储3个阶段的计算结果（用于后续转换）

### 2. 持久化数据结构（schema定义）

#### BattleRound（最终存储结构）
```typescript
export class BattleRound {
  eventTime: number;                        // 回合事件时间
  moraleA: number;                          // A队士气
  moraleB: number;                          // B队士气  
  attackerType: TeamType;                   // 攻击方
  attackMode: number;                       // 攻击模式
  scoreA: number;                           // A队比分
  scoreB: number;                           // B队比分
  periodInfo: PeriodInfo[];                 // 3个阶段的详细数据
}
```

#### PeriodInfo（阶段详细数据）
```typescript
export class PeriodInfo {
  A1Info?: HeroActionInfo;                  // 主攻球员动作信息
  A2Info?: HeroActionInfo;                  // 协攻球员动作信息
  BInfo?: HeroActionInfo;                   // 防守球员动作信息
  GKInfo?: HeroActionInfo;                  // 门将动作信息
  actionId: number;                         // 动作ID
  startCommentId?: number;                  // 开始评论ID
  resultCommentId?: number;                 // 结果评论ID
  actionPer?: number;                       // 成功率（千分制）
  actionResult?: number;                    // 动作结果
  moraleA?: number;                         // A队士气
  moraleB?: number;                         // B队士气
  skillEffectList?: any[];                  // 技能效果列表
}
```

## 🔄 数据转换流程

### 转换映射关系
```
运行时 → 持久化（统一使用Info命名）
BattleTeam.roundInfo.A1Info → PeriodInfo.A1Info (HeroActionInfo)
BattleTeam.roundInfo.A2Info → PeriodInfo.A2Info (HeroActionInfo)
BattleTeam.roundInfo.BInfo  → PeriodInfo.BInfo  (HeroActionInfo)
BattleTeam.roundInfo.GKInfo → PeriodInfo.GKInfo (HeroActionInfo)

BattleTeam.roundInfo.periodInfo[0] → BattleRound.periodInfo[0]
BattleTeam.roundInfo.periodInfo[1] → BattleRound.periodInfo[1]
BattleTeam.roundInfo.periodInfo[2] → BattleRound.periodInfo[2]
```

### 字段命名统一
- ✅ 全部使用当前项目命名：`heroId`, `actionId`, `startCommentId`, `resultCommentId`
- ✅ Old项目仅作为业务逻辑参考，不影响字段命名

## ⚠️ 容易混淆的概念

### 1. Round vs Period
- ❌ **错误理解**：Round和Period是同一层级的概念
- ✅ **正确理解**：1个Round包含3个Period，是包含关系

### 2. 运行时 vs 持久化
- ❌ **错误理解**：RoundInfo和PeriodInfo是同一类型
- ✅ **正确理解**：RoundInfo是运行时临时数据，PeriodInfo是持久化结构

### 3. 数据流向
- ❌ **错误理解**：直接将RoundInfo保存到数据库
- ✅ **正确理解**：RoundInfo → battle-record-generator转换 → BattleRound/PeriodInfo保存

## 🎯 修改实施计划

1. **修正RoundInfo定义**：使用当前项目命名规范
2. **保持Schema不变**：已经使用正确的当前项目命名
3. **完善转换层**：battle-record-generator处理数据转换
4. **移除类型掩盖**：消除所有`as any`强制转换

这样可以实现完整的类型安全，同时保持当前项目的命名一致性。
