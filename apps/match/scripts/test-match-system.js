/**
 * Match服务完整业务逻辑测试
 *
 * 支持运行特定模块的测试：
 * - node test-match-system.js                    # 运行所有测试
 * - node test-match-system.js league             # 只运行联赛系统测试
 * - node test-match-system.js battle             # 只运行战斗引擎测试
 * - node test-match-system.js business           # 只运行商业赛系统测试
 * - node test-match-system.js trophy             # 只运行杯赛系统测试
 * - node test-match-system.js tournament         # 只运行锦标赛系统测试
 * - node test-match-system.js ranking            # 只运行排名系统测试
 * - node test-match-system.js league,battle      # 运行多个指定模块
 *
 * 测试目标：
 * 1. 联赛系统的完整流程（获取数据、PVE战斗、奖励发放）
 * 2. 战斗计算引擎的PVE和PVP战斗
 * 3. 商业赛系统的匹配和战斗
 * 4. 杯赛系统的挑战流程
 * 5. 锦标赛系统的参赛和战斗
 * 6. 排名系统的查询和奖励
 */

const axios = require('axios');
const chalk = require('chalk');
const path = require('path');
const MicroserviceWebSocketClient = require('../../../scripts/common/websocket-client');

// 加载根目录的.env配置
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

// 解析命令行参数
const args = process.argv.slice(2);
const targetModules = args.length > 0 ? args[0].split(',').map(m => m.trim().toLowerCase()) : [];
const existingUsername = args.length > 1 ? args[1] : null; // 第二个参数是现有用户名

// 可用的测试模块
const AVAILABLE_MODULES = {
  league: { name: '联赛系统', file: 'test-league-system' },
  battle: { name: '战斗引擎', file: 'test-battle-engine' },
  business: { name: '商业赛系统', file: 'test-business-system' },
  trophy: { name: '杯赛系统', file: 'test-trophy-system' },
  tournament: { name: '锦标赛系统', file: 'test-tournament-system' },
  ranking: { name: '排名系统', file: 'test-ranking-system' }
};

// 配置
const CONFIG = {
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  AUTH_URL: 'http://127.0.0.1:3100',
  TEST_USER_ID: 'test-match-user-' + Date.now(),
  TEST_SERVER_ID: 'server_001',
  TIMEOUT: 30000  // 增加超时时间到30秒，用于调试
};

// 测试数据存储
const TEST_DATA = {
  token: null,
  characterId: null,
  socket: null
};

/**
 * 比赛系统业务逻辑测试类
 */
class MatchSystemTester extends MicroserviceWebSocketClient {
  constructor() {
    super({
      GATEWAY_WS_URL: CONFIG.GATEWAY_WS_URL,
      AUTH_URL: CONFIG.AUTH_URL,
      TIMEOUT: CONFIG.TIMEOUT
    });
    this.testResults = [];
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(chalk.blue('\n📖 Match服务测试脚本使用说明'));
    console.log(chalk.gray('====================================='));
    console.log(chalk.white('运行所有测试:'));
    console.log(chalk.green('  node test-match-system.js'));
    console.log(chalk.white('\n运行特定模块测试:'));

    Object.entries(AVAILABLE_MODULES).forEach(([key, module]) => {
      console.log(chalk.green(`  node test-match-system.js ${key.padEnd(12)} # ${module.name}测试`));
    });

    console.log(chalk.white('\n运行多个模块测试:'));
    console.log(chalk.green('  node test-match-system.js league,battle,business'));
    console.log(chalk.white('\n使用现有账号测试:'));
    console.log(chalk.green('  node test-match-system.js business quick-test-1758440113645'));
    console.log(chalk.gray('  (建议先运行 quick-test-setup.js 准备测试数据)'));
    console.log(chalk.gray('\n可用模块: ' + Object.keys(AVAILABLE_MODULES).join(', ')));
  }

  /**
   * 验证目标模块
   */
  validateTargetModules() {
    if (targetModules.length === 0) {
      return { valid: true, modules: Object.keys(AVAILABLE_MODULES) }; // 运行所有模块
    }

    const invalidModules = targetModules.filter(module => !AVAILABLE_MODULES[module]);

    if (invalidModules.length > 0) {
      console.log(chalk.red(`❌ 无效的测试模块: ${invalidModules.join(', ')}`));
      this.showHelp();
      return { valid: false };
    }

    return { valid: true, modules: targetModules };
  }

  /**
   * 获取要运行的测试模块
   */
  getTestModules(modules) {
    const testModules = [];

    modules.forEach(moduleKey => {
      const moduleInfo = AVAILABLE_MODULES[moduleKey];
      if (moduleInfo) {
        testModules.push({
          key: moduleKey,
          name: moduleInfo.name,
          file: moduleInfo.file
        });
      }
    });

    return testModules;
  }

  /**
   * 运行测试（支持选择性运行模块）
   */
  async runTests() {
    try {
      // 验证目标模块
      const validation = this.validateTargetModules();
      if (!validation.valid) {
        return;
      }

      const testModules = this.getTestModules(validation.modules);

      console.log(chalk.blue('🚀 开始Match服务业务逻辑测试'));
      console.log(chalk.gray('测试用户ID:'), CONFIG.TEST_USER_ID);
      console.log(chalk.gray('测试服务器ID:'), CONFIG.TEST_SERVER_ID);

      if (targetModules.length > 0) {
        console.log(chalk.yellow('🎯 目标测试模块:'), testModules.map(m => m.name).join(', '));
      } else {
        console.log(chalk.yellow('🎯 运行模式: 全部模块测试'));
      }

      // 1. 健康检查
      console.log(chalk.blue('\n=== 第1步：服务健康检查 ==='));
      const healthChecks = [
        { name: '网关服务', url: CONFIG.GATEWAY_WS_URL },
        { name: 'Auth服务', url: CONFIG.AUTH_URL },
      ];

      for (const check of healthChecks) {
        const isHealthy = await this.checkHttpHealth(check.name, check.url);
        if (!isHealthy) {
          throw new Error(`${check.name}健康检查失败`);
        }
      }

      ////////////////////////////////////////////////////

      // 第2.1步：账号认证
      console.log('\n=== 第2.1步：认证 ===');
      await this.getAuthToken(existingUsername);

      console.log(`测试用户: ${this.username}`);
      console.log(`测试服务器ID: ${this.serverId}`);

      // 第2.2：角色认证
      console.log('\n=== 第2.2步：创建测试角色 ===');
      await this.getCharacterToken();
      console.log(`测试角色ID: ${this.characterId}`);
      console.log(`测试服务器ID: ${this.serverId}`);

      // 第3.1步：使用角色Token连接WebSocket
      console.log('\n=== 第3步：使用角色Token连接WebSocket ===');
      await this.connectWebSocket();
      console.log('✅ WebSocket连接成功');

      // 第3.2步：获取角色消息（新角色触发角色初始化）
      console.log('\n=== 第3.2步：获取角色消息 ===');
      await this.getCharacterInfo();
      console.log('✅ 角色消息获取成功');

      // 第3.3步：检查阵容状态并自动布阵
      console.log('\n=== 第3.3步：检查阵容状态并自动布阵 ===');
      await this.checkAndSetupLineup();
      console.log('✅ 阵容检查和布阵完成');
      //////////////////////////////////////////////////////

      // 4. 准备测试数据
      console.log(chalk.blue('=== 第4步：准备测试数据 ==='));
      TEST_DATA.token = this.token;
      TEST_DATA.characterId = this.characterId;
      TEST_DATA.socket = this.socket;


      // 5. 运行选定的模块测试
      console.log(chalk.blue('=== 第5步：运行模块测试 ==='));

      let successCount = 0;
      let totalCount = testModules.length;

      for (const moduleInfo of testModules) {
        try {
          console.log(chalk.cyan(`\n🔧 开始 ${moduleInfo.name} 测试...`));

          // 动态导入测试模块
          const TestClass = require(path.join(__dirname, moduleInfo.file));
          const tester = new TestClass(this.socket, TEST_DATA);

          // 运行测试
          await tester.runTests();

          console.log(chalk.green(`✅ ${moduleInfo.name} 测试完成`));
          successCount++;

        } catch (error) {
          console.log(chalk.red(`❌ ${moduleInfo.name} 测试失败: ${error.message}`));
          // 继续运行其他模块的测试，不中断整个流程
        }
      }

      // 4. 测试结果汇总
      console.log(chalk.blue('\n=== 测试结果汇总 ==='));
      console.log(chalk.white(`总测试模块: ${totalCount}`));
      console.log(chalk.green(`成功模块: ${successCount}`));
      console.log(chalk.red(`失败模块: ${totalCount - successCount}`));

      if (successCount === totalCount) {
        console.log(chalk.green('\n🎉 所有测试完成！比赛系统运行正常'));
      } else {
        console.log(chalk.yellow(`\n⚠️ 部分测试失败，请检查失败的模块`));
      }

    } catch (error) {
      console.log(chalk.red(`\n❌ 测试失败: ${error.message}`));
      throw error;
    } finally {
      // 清理连接
      this.disconnect();
    }
  }

  /**
   * 检查阵容状态并自动布阵
   */
  async checkAndSetupLineup() {
    try {
      console.log('🔍 检查角色阵容状态...');

      // 1. 检查阵容状态
      const statusResult = await this.sendMessage('character.lineup.checkLineupStatus', {
        characterId: this.characterId,
        serverId: this.serverId
      });

      if (!statusResult.payload.success) {
        throw new Error(`检查阵容状态失败: ${statusResult.payload.message}`);
      }

      const formationStatus = statusResult.payload.data;
      console.log(`阵容状态: ${formationStatus.status}`);
      console.log(`球员数量: ${formationStatus.heroCount}`);
      console.log(`是否合法: ${formationStatus.isValid}`);

      // 2. 根据状态决定是否需要布阵
      if (formationStatus.status === 'READY') {
        console.log('✅ 阵容已就绪，可以参加比赛');
        return;
      }

      if (!formationStatus.canAutoFill) {
        console.log('⚠️ 无法自动布阵，可用球员不足');
        console.log('建议:', formationStatus.suggestions.join(', '));
        return;
      }

      // 3. 执行智能自动布阵
      console.log('🤖 执行智能自动布阵...');
      const formationResult = await this.sendMessage('character.lineup.smartAutoFillLineup', {
        characterId: this.characterId,
        strategy: 'balanced',
        serverId: this.serverId
      });

      if (!formationResult.payload.success) {
        throw new Error(`自动布阵失败: ${formationResult.payload.message}`);
      }

      const autoFillData = formationResult.payload.data;
      console.log('✅ 自动布阵成功');
      console.log(`布阵球员数: ${autoFillData.heroesPlaced}`);
      console.log(`阵容ID: ${autoFillData.formationId}`);
      console.log('球员分布:', JSON.stringify(autoFillData.details, null, 2));

      // 4. 再次检查阵容状态确认
      const finalStatusResult = await this.sendMessage('character.lineup.checkLineupStatus', {
        characterId: this.characterId,
        serverId: this.serverId
      });

      if (finalStatusResult.payload.success && finalStatusResult.payload.data.status === 'READY') {
        console.log('✅ 阵容布阵完成，已就绪参加比赛');
      } else {
        console.log('⚠️ 布阵后阵容仍有问题:', finalStatusResult.payload.data?.errors || ['未知错误']);
      }

    } catch (error) {
      console.log(`❌ 阵容检查和布阵失败: ${error.message}`);
      throw error;
    }
  }
}

// 运行测试
if (require.main === module) {
  // 检查是否请求帮助
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    const tester = new MatchSystemTester();
    tester.showHelp();
    process.exit(0);
  }

  const tester = new MatchSystemTester();
  tester.runTests().catch((error) => {
    console.error(chalk.red('测试执行失败:'), error);
    process.exit(1);
  });
}

module.exports = MatchSystemTester;
