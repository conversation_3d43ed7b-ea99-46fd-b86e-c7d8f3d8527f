import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, IsObject, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { HeroPosition, BattleType } from '@libs/game-constants';
import { BattleTeam } from '../../modules/battle/types/battle-data.types';

/**
 * 战斗系统相关DTO
 * 基于old项目battleService.js的接口定义
 */

/**
 * 队伍战斗数据DTO
 */
export class BattleTeamDataDto {
  @IsString()
  characterId: string; // 玩家ID或AI标识

  @IsString()
  teamName: string; // 队伍名称

  @IsNumber()
  formationId: number; // 阵型配置ID

  @IsNumber()
  tactic: number; // 战术ID

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BattleHeroInfoDto)
  heroes: BattleHeroInfoDto[]; // 英雄列表

  @IsNumber()
  totalAttack: number; // 总防御力

  @IsNumber()
  totalDefend: number; // 总防御力

  @IsNumber()
  morale: number; // 士气值

  @IsNumber()
  score: number; // 比分
}

/**
 * PVE战斗请求DTO
 */
export class PveBattleDto {
  @IsString()
  characterId: string;

  @IsObject()
  characterBattleData: BattleTeam;                       // 玩家战斗数据

  @IsObject()
  enemyBattleData: BattleTeam;                      // 敌方配置数据

  @IsString()
  battleType: BattleType;                    // 战斗类型 (league/trophy/tournament等)

  @IsOptional()
  @IsNumber()
  leagueId?: number;                     // 联赛ID

  @IsOptional()
  @IsNumber()
  teamCopyId?: number;                   // 副本ID

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * PVP战斗请求DTO
 */
export class PvpBattleDto {
  @IsString()
  homeCharacterId: string;                  // 主队玩家ID

  @IsString()
  awayCharacterId: string;                  // 客队玩家ID

  @IsObject()
  homeBattleData: BattleTeam;                   // 主队战斗数据

  @IsObject()
  awayBattleData: BattleTeam;                   // 客队战斗数据

  @IsString()
  battleType: BattleType;                    // 战斗类型

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 球员战斗信息DTO
 * 修复：使用项目统一的类型定义，确保架构一致性
 */
export class BattleHeroInfoDto {
  @IsString()
  heroId: string;                        // 修复：使用统一的heroId命名

  @IsNumber()
  attack: number;

  @IsNumber()
  defend: number;

  @IsNumber()
  speed: number;

  @IsNumber()
  power: number;

  @IsNumber()
  technique: number;

  @IsEnum(HeroPosition)
  position: HeroPosition;                // 修复：使用统一的HeroPosition枚举

  @IsNumber()
  level: number;

  @IsArray()
  @IsNumber({}, { each: true })
  skills: number[];
}

/**
 * 战斗结果响应DTO - 增强版本
 */
export class BattleResultResponseDto {
  @IsOptional()
  @IsString()
  roomId?: string;

  @IsOptional()
  @IsNumber()
  homeScore?: number;

  @IsOptional()
  @IsNumber()
  awayScore?: number;

  @IsOptional()
  @IsNumber()
  winner?: number;                       // 0平局 1主队胜 2客队胜

  @IsOptional()
  @IsObject()
  battleRecord?: any;                    // 战斗回放数据

  @IsOptional()
  @IsObject()
  statistics?: any;                      // 战斗统计数据

  // 🔧 评论系统相关数据
  @IsOptional()
  @IsObject()
  commentSummary?: {
    matchSummary: string;                // 比赛总结评论
    totalComments: number;               // 总评论数量
    goalComments: number;                // 进球相关评论数量
    keyMoments: string[];                // 关键时刻摘要
  };
}

/**
 * 获取战斗回放请求DTO
 */
export class GetBattleReplayDto {
  @IsString()
  roomId: string;

  @IsOptional()
  @IsString()
  characterId?: string;
}

/**
 * 获取战斗回放响应DTO - 符合优化后的schema结构
 */
export class GetBattleReplayResponseDto {
  @IsOptional()
  @IsObject()
  battleRecord?: {
    battleRoundInfo: any[];
    totalTime: number;
    totalRounds: number;
  };

  @IsOptional()
  @IsObject()
  teamAData?: any;  // TeamSnapshoot - 替代了原preBattleInfo[0]

  @IsOptional()
  @IsObject()
  teamBData?: any;  // TeamSnapshoot - 替代了原preBattleInfo[1]

  @IsOptional()
  @IsObject()
  battleResult?: {  // BattleResult - 包含了原battleEndInfo的所有内容
    roomId: string;
    battleType: string;
    homeScore: number;
    awayScore: number;
    winner: number;
    totalTime: number;
    totalRounds: number;
    teamAStats: any;
    teamBStats: any;
    goals: any[];
    rewards?: any;
    lootItems?: any[];
    skillRecords: any[];
    commentSummary?: any;
  };
}
