import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GroundMatch, GroundMatchDocument, GroundField, OccupyField, GroundMatchRecord, ReportRecord, SearchResult } from '../schemas/ground-match.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 个人球场争夺战数据访问层
 * 基于old项目footballGround.js和groundMatchService.js的业务逻辑
 * 
 * 🎯 核心功能：
 * - 球场争夺战数据管理
 * - 训练场占领系统
 * - 举报和保护机制
 * - 搜索和匹配功能
 * - 奖励计算和发放
 * 
 * 🚀 性能优化：
 * - 继承BaseRepository的所有优化功能
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 批量操作优化
 */
@Injectable()
export class GroundMatchRepository extends BaseRepository<GroundMatchDocument> {
  constructor(
    @InjectModel(GroundMatch.name) 
    private readonly groundMatchModel: Model<GroundMatchDocument>
  ) {
    super(groundMatchModel, 'GroundMatchRepository');
  }

  // ==================== 基础数据操作 ====================

  /**
   * 根据玩家UID查找球场争夺战数据
   */
  async findByUid(uid: string): Promise<XResult<GroundMatchDocument | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家UID查找球场争夺战数据（Lean查询优化版本）
   */
  async findByUidLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 创建或更新球场争夺战数据
   */
  async upsertGroundMatch(uid: string, groundMatchData: Partial<GroundMatch>): Promise<XResult<GroundMatchDocument>> {
    const result = await this.upsert(
      { uid },
      {
        ...groundMatchData,
        uid,
        lastUpdateTime: new Date()
      }
    );

    if (XResultUtils.isFailure(result)) {
      return result;
    }

    return XResultUtils.ok(result.data.document);
  }

  /**
   * 初始化玩家球场争夺战数据
   * 基于old项目initGroundMatch方法逻辑
   */
  async initializeGroundMatch(uid: string, gid: string, mainGroundLevel: number): Promise<XResult<GroundMatchDocument>> {
    // 计算训练场最大资源ID
    const maxResId = this.calcGroundMatchFieldMaxResId(mainGroundLevel);
    
    // 初始化训练场数据
    const fieldList: GroundField[] = [];
    for (let i = 0; i < 3; i++) {
      const field: GroundField = {
        resId: maxResId - i,              // 等级从高到低
        teamUid: '',
        startTime: new Date(),
        beOccupiedUid: '',
        beOccupiedGid: '',
        beOccupiedTeamUid: '',
        beOccupiedTeamName: '',
        name: '',
        faceUrl: '',
        formationResId: 0,
        attack: 0,
        defend: 0,
        atkTactic: 0,
        defTactic: 0,
        str: 0,
        occupyStartTime: new Date(0),
        protectType: 0,
        protectEndTime: new Date(0),
        recordList: []
      };
      fieldList.push(field);
    }

    // 初始化占领数据
    const occupyFieldList: OccupyField[] = [];
    for (let i = 0; i < 3; i++) {
      const occupy: OccupyField = {
        teamUid: '',
        teamName: '',
        resId: 0,
        occupyUid: '',
        occupyGid: '',
        occupyTeamUid: '',
        occupyFaceUrl: '',
        occupyTeamIndex: 3,               // 3表示没有占领
        name: '',
        protectType: 0,
        protectEndTime: new Date(0),
        occupyTime: new Date(0),
        ballFan: 0,
        mainGroundLevel: 0,
        lastBeReportTime: new Date(0),
        recordList: [],
        beReportedList: []
      };
      occupyFieldList.push(occupy);
    }

    const groundMatchData: Partial<GroundMatch> = {
      uid,
      gid,
      reportNum: 10,                      // 初始举报次数
      reportFreshTime: new Date(),
      lastReportTime: new Date(0),
      fieldList,
      occupyFieldList,
      searchList: [],
      lastUpdateTime: new Date()
    };

    return this.upsertGroundMatch(uid, groundMatchData);
  }

  // ==================== 训练场管理 ====================

  /**
   * 更新训练场数据
   */
  async updateField(uid: string, fieldIndex: number, updates: Partial<GroundField>): Promise<XResult<GroundMatch | null>> {
    const updateQuery: any = {};
    for (const [key, value] of Object.entries(updates)) {
      updateQuery[`fieldList.${fieldIndex}.${key}`] = value;
    }
    updateQuery.lastUpdateTime = new Date();

    return await this.updateOne({ uid }, { $set: updateQuery });
  }

  /**
   * 添加训练场记录
   */
  async addFieldRecord(uid: string, fieldIndex: number, record: GroundMatchRecord): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $push: { [`fieldList.${fieldIndex}.recordList`]: record },
        $set: { lastUpdateTime: new Date() }
      }
    );
  }

  /**
   * 设置训练场保护
   */
  async setFieldProtection(uid: string, fieldIndex: number, protectType: number, protectEndTime: Date): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $set: {
          [`fieldList.${fieldIndex}.protectType`]: protectType,
          [`fieldList.${fieldIndex}.protectEndTime`]: protectEndTime,
          lastUpdateTime: new Date()
        }
      }
    );
  }

  // ==================== 占领管理 ====================

  /**
   * 更新占领数据
   */
  async updateOccupyField(uid: string, occupyIndex: number, updates: Partial<OccupyField>): Promise<XResult<GroundMatch | null>> {
    const updateQuery: any = {};
    for (const [key, value] of Object.entries(updates)) {
      updateQuery[`occupyFieldList.${occupyIndex}.${key}`] = value;
    }
    updateQuery.lastUpdateTime = new Date();

    return await this.updateOne({ uid }, { $set: updateQuery });
  }

  /**
   * 更新举报次数
   */
  async updateReportNum(uid: string, reportNum: number, reportFreshTime: Date): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $set: {
          reportNum,
          reportFreshTime,
          lastUpdateTime: new Date()
        }
      }
    );
  }

  /**
   * 添加占领记录
   */
  async addOccupyRecord(uid: string, occupyIndex: number, record: GroundMatchRecord): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $push: { [`occupyFieldList.${occupyIndex}.recordList`]: record },
        $set: { lastUpdateTime: new Date() }
      }
    );
  }

  /**
   * 设置占领保护
   */
  async setOccupyProtection(uid: string, occupyIndex: number, protectType: number, protectEndTime: Date): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $set: {
          [`occupyFieldList.${occupyIndex}.protectType`]: protectType,
          [`occupyFieldList.${occupyIndex}.protectEndTime`]: protectEndTime,
          lastUpdateTime: new Date()
        }
      }
    );
  }

  // ==================== 举报系统 ====================

  /**
   * 添加举报记录
   */
  async addReport(uid: string, occupyIndex: number, report: ReportRecord): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $push: { [`occupyFieldList.${occupyIndex}.beReportedList`]: report },
        $set: {
          [`occupyFieldList.${occupyIndex}.lastBeReportTime`]: new Date(),
          lastUpdateTime: new Date()
        }
      }
    );
  }

  /**
   * 使用举报次数
   */
  async useReportChance(uid: string): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid, reportNum: { $gt: 0 } },
      {
        $inc: { reportNum: -1 },
        $set: {
          lastReportTime: new Date(),
          lastUpdateTime: new Date()
        }
      }
    );
  }

  /**
   * 刷新举报次数
   * 基于old项目footballGround.js中的reportNum逻辑
   * old项目中直接设置this.groundMatch.reportNum = 10
   */
  async refreshReportNum(uid: string, reportNum: number = 10): Promise<XResult<any>> {
    // 使用updateMany来获取modifiedCount，符合old项目的更新逻辑
    return await this.updateMany(
      { uid },
      {
        $set: {
          reportNum,
          reportFreshTime: new Date(),
          lastUpdateTime: new Date()
        }
      }
    );
  }

  // ==================== 搜索系统 ====================

  /**
   * 添加搜索结果
   */
  async addSearchResult(uid: string, searchResult: SearchResult): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $push: { searchList: searchResult },
        $set: { lastUpdateTime: new Date() }
      }
    );
  }

  /**
   * 清空搜索结果
   */
  async clearSearchResults(uid: string): Promise<XResult<GroundMatch | null>> {
    return await this.updateOne(
      { uid },
      {
        $set: {
          searchList: [],
          lastUpdateTime: new Date()
        }
      }
    );
  }

  // ==================== 工具方法 ====================

  /**
   * 计算训练场最大资源ID
   * 基于old项目calcGroundMatchFieldMaxResId方法
   */
  private calcGroundMatchFieldMaxResId(mainGroundLevel: number): number {
    // 基于主球场等级计算最大资源ID
    // 这里简化实现，实际应该根据配置表计算
    return Math.min(mainGroundLevel + 2, 10);
  }

  // ==================== 批量查询 ====================

  /**
   * 批量查询球场争夺战数据
   */
  async findGroundMatchesByUids(uids: string[]): Promise<XResult<GroundMatchDocument[]>> {
    return this.findMany({ uid: { $in: uids } });
  }

  /**
   * 批量查询球场争夺战数据（Lean查询优化版本）
   */
  async findGroundMatchesByUidsLean(uids: string[]): Promise<XResult<any[]>> {
    return this.findManyLean({ uid: { $in: uids } });
  }

  /**
   * 查找被占领的训练场
   */
  async findOccupiedFields(occupyUid: string): Promise<XResult<GroundMatchDocument[]>> {
    return this.findMany({ 'fieldList.beOccupiedUid': occupyUid });
  }

  /**
   * 查找正在占领的训练场
   */
  async findOccupyingFields(uid: string): Promise<XResult<GroundMatchDocument[]>> {
    return this.findMany({ 'occupyFieldList.occupyUid': uid });
  }

  // ==================== 统计分析 ====================

  /**
   * 获取球场争夺战统计信息
   */
  async getGroundMatchStatistics(): Promise<XResult<any>> {
    try {
      const pipeline = [
        {
          $group: {
            _id: null,
            totalPlayers: { $sum: 1 },
            totalOccupiedFields: {
              $sum: {
                $size: {
                  $filter: {
                    input: '$fieldList',
                    cond: { $ne: ['$$this.beOccupiedUid', ''] }
                  }
                }
              }
            },
            totalOccupyingFields: {
              $sum: {
                $size: {
                  $filter: {
                    input: '$occupyFieldList',
                    cond: { $ne: ['$$this.occupyUid', ''] }
                  }
                }
              }
            },
            avgReportNum: { $avg: '$reportNum' }
          }
        }
      ];

      const result = await this.aggregate(pipeline);
      if (XResultUtils.isFailure(result) || !result.data || result.data.length === 0) {
        return XResultUtils.ok({
          totalPlayers: 0,
          totalOccupiedFields: 0,
          totalOccupyingFields: 0,
          avgReportNum: 0,
          timestamp: new Date()
        });
      }

      return XResultUtils.ok({
        ...result.data[0],
        timestamp: new Date()
      });
    } catch (error) {
      return XResultUtils.error(`获取球场争夺战统计信息失败: ${error.message}`, 'STATISTICS_QUERY_FAILED');
    }
  }
}
