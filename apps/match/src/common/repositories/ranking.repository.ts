import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { GlobalRanking, CharacterRanking, RankingEntry, RankingReward } from '../schemas/ranking.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';
import { GlobalRankingRepository } from './global-ranking.repository';
import { CharacterRankingRepository } from './character-ranking.repository';

/**
 * 排名数据访问层 - 组合服务
 * 组合GlobalRankingRepository和CharacterRankingRepository，提供统一的排名服务接口
 *
 * ⚠️ 注意：此类将被逐步废弃，建议直接使用GlobalRankingRepository和CharacterRankingRepository
 *
 * 🎯 核心功能：
 * - 全球排名管理（委托给GlobalRankingRepository）
 * - 玩家排名数据管理（委托给CharacterRankingRepository）
 * - 排名奖励系统
 * - 排名统计分析
 * - 过期数据清理
 *
 * 🚀 性能优化：
 * - 使用独立Repository的优化功能
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class RankingRepository {
  constructor(
    private readonly globalRankingRepository: GlobalRankingRepository,
    private readonly characterRankingRepository: CharacterRankingRepository,
  ) {}

  // ==================== 委托方法 - 全球排名相关 ====================

  /**
   * 根据排名类型查找全球排名
   * 委托给GlobalRankingRepository
   */
  async findGlobalRanking(rankType: string, season?: number): Promise<XResult<GlobalRanking | null>> {
    return this.globalRankingRepository.findGlobalRanking(rankType, season);
  }

  /**
   * 根据排名类型查找全球排名（Lean查询优化版本）
   * 委托给GlobalRankingRepository
   */
  async findGlobalRankingLean(rankType: string, season?: number): Promise<XResult<any | null>> {
    return this.globalRankingRepository.findGlobalRankingLean(rankType, season);
  }

  /**
   * 创建或更新全球排名
   * 委托给GlobalRankingRepository
   */
  async upsertGlobalRanking(rankType: string, rankings: RankingEntry[], season: number = 0): Promise<XResult<GlobalRanking>> {
    return this.globalRankingRepository.upsertGlobalRanking(rankType, rankings, season);
  }

  /**
   * 获取全球排名列表（分页）
   * 使用BaseRepository的findOne方法优化性能
   */
  async getGlobalRankingList(rankType: string, limit: number = 100, offset: number = 0, season?: number): Promise<XResult<RankingEntry[]>> {
    const query: any = { rankType };
    if (season !== undefined) {
      query.season = season;
    }

    const globalRankingResult = await this.findGlobalRankingLean(rankType, season);
    if (XResultUtils.isFailure(globalRankingResult) || !globalRankingResult.data) {
      return XResultUtils.ok([]);
    }

    const rankings = globalRankingResult.data.rankings?.slice(offset, offset + limit) || [];
    return XResultUtils.ok(rankings);
  }

  /**
   * 获取玩家在全球排名中的位置
   * 使用BaseRepository的findOne方法优化性能
   */
  async getCharacterGlobalRank(rankType: string, characterId: string, season?: number): Promise<XResult<number>> {
    const globalRankingResult = await this.findGlobalRankingLean(rankType, season);
    if (XResultUtils.isFailure(globalRankingResult) || !globalRankingResult.data) {
      return XResultUtils.ok(0);
    }

    const globalRanking = globalRankingResult.data;
    const characterEntry = globalRanking.rankings?.find(entry => entry.characterId === characterId);
    return XResultUtils.ok(characterEntry ? characterEntry.rank : 0);
  }

  // ==================== 委托方法 - 玩家排名相关 ====================

  /**
   * 根据玩家UID查找排名数据
   * 委托给CharacterRankingRepository
   */
  async findCharacterRanking(uid: string): Promise<XResult<CharacterRanking | null>> {
    return this.characterRankingRepository.findCharacterRanking(uid);
  }

  /**
   * 根据玩家UID查找排名数据（Lean查询优化版本）
   * 委托给CharacterRankingRepository
   */
  async findCharacterRankingLean(uid: string): Promise<XResult<any | null>> {
    return this.characterRankingRepository.findCharacterRankingLean(uid);
  }

  /**
   * 创建或更新玩家排名数据
   * 委托给CharacterRankingRepository
   */
  async upsertCharacterRanking(uid: string, characterRankingData: Partial<CharacterRanking>): Promise<XResult<CharacterRanking>> {
    return this.characterRankingRepository.upsertCharacterRanking(uid, characterRankingData);
  }

  // ==================== 业务逻辑方法 ====================

  /**
   * 添加排名奖励记录
   * 业务逻辑方法，委托底层操作给CharacterRankingRepository
   */
  async addRankingReward(uid: string, reward: RankingReward): Promise<XResult<CharacterRanking | null>> {
    const characterRankingResult = await this.findCharacterRanking(uid);

    if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
      // 创建新的玩家排名数据
      return await this.upsertCharacterRanking(uid, {
        characterId: uid,
        rewardHistory: [reward],
        currentRanks: {},
        bestRanks: {},
      });
    }

    const characterRanking = characterRankingResult.data;

    // 限制奖励历史记录数量为100条
    if (characterRanking.rewardHistory.length >= 100) {
      const removeCount = characterRanking.rewardHistory.length - 100 + 1;
      characterRanking.rewardHistory.splice(0, removeCount);
    }

    // 添加新奖励记录
    characterRanking.rewardHistory.push(reward);

    // 按时间排序（最新的在前）
    characterRanking.rewardHistory.sort((a, b) => {
      return new Date(b.rewardTime).getTime() - new Date(a.rewardTime).getTime();
    });

    return await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
  }

  /**
   * 更新玩家当前排名
   * 业务逻辑方法，委托底层操作给CharacterRankingRepository
   */
  async updateCharacterCurrentRanks(uid: string, ranks: any): Promise<XResult<CharacterRanking | null>> {
    const characterRankingResult = await this.findCharacterRankingLean(uid);

    const characterRanking = XResultUtils.isSuccess(characterRankingResult) ? characterRankingResult.data : null;
    const currentRanks = characterRanking ? { ...characterRanking.currentRanks, ...ranks } : ranks;
    const bestRanks = characterRanking ? { ...characterRanking.bestRanks } : {};

    // 更新历史最佳排名
    for (const [rankType, rank] of Object.entries(ranks)) {
      if (typeof rank === 'number' && rank > 0) {
        if (!bestRanks[rankType] || rank < bestRanks[rankType]) {
          bestRanks[rankType] = rank;
        }
      }
    }

    return await this.upsertCharacterRanking(uid, { currentRanks, bestRanks });
  }

  /**
   * 获取未领取的排名奖励
   * 使用BaseRepository的findOne方法优化性能
   */
  async getUnclaimedRewards(uid: string): Promise<XResult<RankingReward[]>> {
    const characterRankingResult = await this.findCharacterRankingLean(uid);
    if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
      return XResultUtils.ok([]);
    }

    const characterRanking = characterRankingResult.data;
    const unclaimedRewards = characterRanking.rewardHistory?.filter(reward => reward.status === 0) || [];
    return XResultUtils.ok(unclaimedRewards);
  }

  /**
   * 标记奖励为已领取
   * 业务逻辑方法，委托底层操作给CharacterRankingRepository
   */
  async markRewardAsClaimed(uid: string, rankType: string, season: number): Promise<XResult<boolean>> {
    const characterRankingResult = await this.findCharacterRanking(uid);
    if (XResultUtils.isFailure(characterRankingResult) || !characterRankingResult.data) {
      return XResultUtils.ok(false);
    }

    const characterRanking = characterRankingResult.data;
    const reward = characterRanking.rewardHistory.find(
      r => r.rankType === rankType && r.season === season && r.status === 0
    );

    if (!reward) {
      return XResultUtils.ok(false);
    }

    reward.status = 1;
    const updateResult = await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
    return XResultUtils.ok(XResultUtils.isSuccess(updateResult));
  }

  // ==================== 统计和管理 ====================

  /**
   * 获取排名统计信息
   * 委托给各自的Repository进行统计
   */
  async getStatistics(): Promise<XResult<any>> {
    // 并行执行所有统计查询
    const [globalResult, characterResult] = await Promise.all([
      this.globalRankingRepository.getGlobalRankingStatistics(),
      this.characterRankingRepository.getCharacterRankingStatistics()
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(globalResult) || XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error('获取排名统计信息失败', 'STATISTICS_QUERY_FAILED');
    }

    return XResultUtils.ok({
      global: globalResult.data,
      character: characterResult.data,
      timestamp: new Date(),
    });
  }

  /**
   * 批量查询玩家排名数据
   * 委托给CharacterRankingRepository
   */
  async findCharacterRankingsByIds(ids: string[]): Promise<XResult<CharacterRanking[]>> {
    return this.characterRankingRepository.findCharacterRankings(ids);
  }

  /**
   * 批量查询玩家排名数据（Lean查询优化版本）
   * 委托给CharacterRankingRepository
   */
  async findCharacterRankingsByIdsLean(ids: string[]): Promise<XResult<any[]>> {
    return this.characterRankingRepository.findCharacterRankingsLean(ids);
  }
}
