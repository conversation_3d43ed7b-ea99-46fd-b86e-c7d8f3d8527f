import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 杯赛副本数据
 * 基于old项目trophyCopy.js的copyData结构
 */
@Schema({ _id: false })
export class TrophyCopyData {
  @Prop({ required: true })
  teamId: number;                        // 球队副本ID 配置的球队副本ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数
}

/**
 * 单个杯赛数据结构
 * 基于old项目trophyCopy.js的oneTrophyObj结构
 */
@Schema({ _id: false })
export class OneTrophy {
  @Prop({ required: true })
  id: number;                            // 杯赛副本ID，不需要自己生成,直接使用配置的ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数

  @Prop({ type: [TrophyCopyData], default: [] })
  copyData: TrophyCopyData[];            // 副本数据
}

/**
 * 杯赛奖励信息
 * 基于old项目trophyCopy.js的奖励处理逻辑
 */
@Schema({ _id: false })
export class TrophyRewardInfo {
  @Prop({ type: [Object], default: [] })
  itemIdList: any[];                    // 物品奖励列表

  @Prop({ type: [Object], default: [] })
  heroIdList: any[];                    // 球员奖励列表

  @Prop({ default: 0 })
  code: number;                          // 奖励处理结果代码
}

/**
 * 杯赛战斗结果
 * 基于old项目trophyCopy.js的战斗结果结构
 */
@Schema({ _id: false })
export class TrophyBattleResult {
  @Prop({ required: true })
  trophyId: number;                      // 杯赛ID

  @Prop({ required: true })
  teamId: number;                        // 队伍ID

  @Prop({ default: 0 })
  result: number;                        // 战斗结果 (0失败 1胜利)

  @Prop({ default: 0 })
  selfScore: number;                     // 己方比分

  @Prop({ default: 0 })
  enemyScore: number;                    // 敌方比分

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: TrophyRewardInfo })
  rewardInfo: TrophyRewardInfo;          // 奖励信息
}

/**
 * 杯赛主文档
 * 基于old项目trophyCopy.js的主要数据结构
 */
@Schema({
  collection: 'trophy_copies',
  timestamps: true,
  versionKey: false,
})
export class TrophyCopy extends Document {
  @Prop({ required: true, unique: true })
  characterId: string;                    // 角色ID

  @Prop({ type: [OneTrophy], default: [] })
  allTrophyCopys: OneTrophy[];           // 所有杯赛数据 trophyId => oneTrophyObj

  @Prop({ default: 0 })
  lastUpdateTimes: number;               // 最后更新时间戳

  @Prop({ type: [TrophyBattleResult], default: [] })
  battleHistory: TrophyBattleResult[];   // 战斗历史记录

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const TrophyCopySchema = SchemaFactory.createForClass(TrophyCopy);

// 定义方法接口 - 基于TrophyService的真实业务逻辑
export interface TrophyCopyMethods {
  // 杯赛管理 - 基于TrophyService
  getTrophy(trophyId: number): OneTrophy | null;
  addTrophy(trophy: OneTrophy): void;
  updateTrophy(trophyId: number, updates: Partial<OneTrophy>): boolean;

  // 副本管理 - 基于TrophyService
  getCopyData(trophyId: number, teamId: number): TrophyCopyData | null;
  updateCopyData(trophyId: number, teamId: number, updates: Partial<TrophyCopyData>): boolean;

  // 挑战次数管理 - 基于TrophyService
  canChallenge(trophyId: number, teamId?: number): boolean;
  consumeChallenge(trophyId: number, teamId?: number): boolean;
  addChallengeTimes(trophyId: number, times: number, teamId?: number): boolean;
  resetDailyTimes(): void;

  // 购买次数管理 - 基于TrophyService
  canPurchase(trophyId: number, teamId?: number): boolean;
  purchaseChallengeTimes(trophyId: number, times: number, teamId?: number): boolean;
  getPurchaseInfo(trophyId: number, teamId?: number): { purchased: number; maxPurchase: number };

  // 战斗记录管理 - 基于TrophyService
  addBattleResult(result: TrophyBattleResult): void;
  getBattleHistory(trophyId?: number, limit?: number): TrophyBattleResult[];
  getLastBattleResult(trophyId?: number): TrophyBattleResult | null;

  // 统计分析 - 基于TrophyService
  getTrophyStats(trophyId: number): any;
  getOverallStats(): any;
  getWinRate(trophyId: number): number;
  getTotalBattles(trophyId: number): number;

  // 数据验证 - 基于TrophyService
  needsDailyReset(): boolean;
  validateTrophyData(): boolean;

  // 数据转换 - 基于TrophyService
  toClientTrophyData(): any[];
  getTrophySummary(): any;
}

// 定义Document类型
export type TrophyCopyDocument = TrophyCopy & Document & TrophyCopyMethods;

// 创建索引
TrophyCopySchema.index({ uid: 1 });
TrophyCopySchema.index({ 'allTrophyCopys.id': 1 });
TrophyCopySchema.index({ lastUpdateTimes: 1 });
TrophyCopySchema.index({ lastUpdateTime: 1 });

// ==================== 实例方法实现 ====================

/**
 * 获取指定杯赛
 * 基于TrophyService: 杯赛查询逻辑
 */
TrophyCopySchema.methods.getTrophy = function(trophyId: number): OneTrophy | null {
  return this.allTrophyCopys.find((trophy: OneTrophy) => trophy.id === trophyId) || null;
};

/**
 * 添加杯赛
 * 基于TrophyService: initTrophyCopyData方法逻辑
 */
TrophyCopySchema.methods.addTrophy = function(trophy: OneTrophy): void {
  // 检查是否已存在
  const existingIndex = this.allTrophyCopys.findIndex((t: OneTrophy) => t.id === trophy.id);
  if (existingIndex !== -1) {
    this.allTrophyCopys[existingIndex] = trophy;
  } else {
    this.allTrophyCopys.push(trophy);
  }
  this.lastUpdateTime = new Date();
  this.lastUpdateTimes = Date.now();
};

/**
 * 更新杯赛信息
 * 基于TrophyService: 杯赛更新逻辑
 */
TrophyCopySchema.methods.updateTrophy = function(trophyId: number, updates: Partial<OneTrophy>): boolean {
  const trophy = this.getTrophy(trophyId);
  if (!trophy) return false;

  Object.assign(trophy, updates);
  this.lastUpdateTime = new Date();
  this.lastUpdateTimes = Date.now();
  return true;
};

/**
 * 获取副本数据
 * 基于TrophyService: 副本查询逻辑
 */
TrophyCopySchema.methods.getCopyData = function(trophyId: number, teamId: number): TrophyCopyData | null {
  const trophy = this.getTrophy(trophyId);
  if (!trophy) return null;

  return trophy.copyData.find((copy: TrophyCopyData) => copy.teamId === teamId) || null;
};

/**
 * 更新副本数据
 * 基于TrophyService: 副本数据更新逻辑
 */
TrophyCopySchema.methods.updateCopyData = function(trophyId: number, teamId: number, updates: Partial<TrophyCopyData>): boolean {
  const copy = this.getCopyData(trophyId, teamId);
  if (!copy) return false;

  Object.assign(copy, updates);
  this.lastUpdateTime = new Date();
  this.lastUpdateTimes = Date.now();
  return true;
};

/**
 * 检查是否可以挑战
 * 基于TrophyService: pveTrophyBattle方法中的次数检查逻辑
 */
TrophyCopySchema.methods.canChallenge = function(trophyId: number, teamId?: number): boolean {
  if (teamId) {
    // 检查特定队伍的挑战次数
    const copy = this.getCopyData(trophyId, teamId);
    if (!copy) return false;

    // 基于TrophyService中的限制逻辑，假设每个队伍每日限制1次
    return copy.num < 1;
  } else {
    // 检查杯赛总体挑战次数
    const trophy = this.getTrophy(trophyId);
    if (!trophy) return false;

    // 基于TrophyService中的限制逻辑，假设每个杯赛每日限制2次
    return trophy.num < 2;
  }
};

/**
 * 消耗挑战次数
 * 基于TrophyService: pveTrophyBattle方法中的次数消耗逻辑
 */
TrophyCopySchema.methods.consumeChallenge = function(trophyId: number, teamId?: number): boolean {
  if (teamId) {
    if (!this.canChallenge(trophyId, teamId)) return false;

    const copy = this.getCopyData(trophyId, teamId);
    if (copy) {
      copy.num += 1;
      this.lastUpdateTime = new Date();
      this.lastUpdateTimes = Date.now();
      return true;
    }
  } else {
    if (!this.canChallenge(trophyId)) return false;

    const trophy = this.getTrophy(trophyId);
    if (trophy) {
      trophy.num += 1;
      this.lastUpdateTime = new Date();
      this.lastUpdateTimes = Date.now();
      return true;
    }
  }

  return false;
};

/**
 * 增加挑战次数
 * 基于TrophyService: buyTrophyTimes方法逻辑
 */
TrophyCopySchema.methods.addChallengeTimes = function(trophyId: number, times: number, teamId?: number): boolean {
  if (teamId) {
    const copy = this.getCopyData(trophyId, teamId);
    if (!copy) return false;

    // 增加购买次数记录
    copy.alreadyPurchaseNum += times;
    this.lastUpdateTime = new Date();
    this.lastUpdateTimes = Date.now();
    return true;
  } else {
    const trophy = this.getTrophy(trophyId);
    if (!trophy) return false;

    trophy.alreadyPurchaseNum += times;
    this.lastUpdateTime = new Date();
    this.lastUpdateTimes = Date.now();
    return true;
  }
};

/**
 * 重置每日次数
 * 基于TrophyService: checkTrophyData方法中的重置逻辑
 */
TrophyCopySchema.methods.resetDailyTimes = function(): void {
  for (const trophy of this.allTrophyCopys) {
    trophy.num = 0;
    trophy.alreadyPurchaseNum = 0;

    for (const copy of trophy.copyData) {
      copy.num = 0;
      copy.alreadyPurchaseNum = 0;
    }
  }

  this.lastUpdateTime = new Date();
  this.lastUpdateTimes = Date.now();
};

/**
 * 检查是否可以购买
 * 基于TrophyService: buyTrophyTimes方法中的购买限制逻辑
 */
TrophyCopySchema.methods.canPurchase = function(trophyId: number, teamId?: number): boolean {
  if (teamId) {
    const copy = this.getCopyData(trophyId, teamId);
    if (!copy) return false;

    // 基于配置的最大购买次数限制，假设每个队伍最多购买2次
    return copy.alreadyPurchaseNum < 2;
  } else {
    const trophy = this.getTrophy(trophyId);
    if (!trophy) return false;

    // 基于配置的最大购买次数限制，假设每个杯赛最多购买2次
    return trophy.alreadyPurchaseNum < 2;
  }
};

/**
 * 购买挑战次数
 * 基于TrophyService: buyTrophyTimes方法逻辑
 */
TrophyCopySchema.methods.purchaseChallengeTimes = function(trophyId: number, times: number, teamId?: number): boolean {
  if (!this.canPurchase(trophyId, teamId)) return false;

  return this.addChallengeTimes(trophyId, times, teamId);
};

/**
 * 获取购买信息
 * 基于TrophyService: 购买信息查询逻辑
 */
TrophyCopySchema.methods.getPurchaseInfo = function(trophyId: number, teamId?: number): { purchased: number; maxPurchase: number } {
  if (teamId) {
    const copy = this.getCopyData(trophyId, teamId);
    return {
      purchased: copy ? copy.alreadyPurchaseNum : 0,
      maxPurchase: 2 // 基于配置
    };
  } else {
    const trophy = this.getTrophy(trophyId);
    return {
      purchased: trophy ? trophy.alreadyPurchaseNum : 0,
      maxPurchase: 2 // 基于配置
    };
  }
};

/**
 * 添加战斗结果
 * 基于TrophyService: pveTrophyBattle方法中的战斗结果记录逻辑
 */
TrophyCopySchema.methods.addBattleResult = function(result: TrophyBattleResult): void {
  this.battleHistory.unshift(result); // 添加到开头

  // 保持历史记录数量限制
  if (this.battleHistory.length > 100) {
    this.battleHistory = this.battleHistory.slice(0, 100);
  }

  this.lastUpdateTime = new Date();
  this.lastUpdateTimes = Date.now();
};

/**
 * 获取战斗历史
 * 基于TrophyService: 战斗历史查询逻辑
 */
TrophyCopySchema.methods.getBattleHistory = function(trophyId?: number, limit?: number): TrophyBattleResult[] {
  let history = this.battleHistory;

  if (trophyId) {
    history = history.filter((battle: TrophyBattleResult) => battle.trophyId === trophyId);
  }

  if (limit) {
    history = history.slice(0, limit);
  }

  return history.sort((a: TrophyBattleResult, b: TrophyBattleResult) =>
    b.battleTime.getTime() - a.battleTime.getTime()
  );
};

/**
 * 获取最后一次战斗结果
 * 基于TrophyService: 最近战斗查询逻辑
 */
TrophyCopySchema.methods.getLastBattleResult = function(trophyId?: number): TrophyBattleResult | null {
  const history = this.getBattleHistory(trophyId, 1);
  return history.length > 0 ? history[0] : null;
};

/**
 * 获取杯赛统计
 * 基于TrophyService: 统计分析逻辑
 */
TrophyCopySchema.methods.getTrophyStats = function(trophyId: number): any {
  const trophy = this.getTrophy(trophyId);
  if (!trophy) return null;

  const battles = this.getBattleHistory(trophyId);
  const totalBattles = battles.length;
  const wins = battles.filter((battle: TrophyBattleResult) => battle.result === 1).length;
  const winRate = totalBattles > 0 ? Math.round((wins / totalBattles) * 100) : 0;

  // 统计总进球和失球
  let totalGoals = 0;
  let totalConceded = 0;
  for (const battle of battles) {
    totalGoals += battle.selfScore;
    totalConceded += battle.enemyScore;
  }

  return {
    trophyId,
    totalBattles,
    wins,
    losses: totalBattles - wins,
    winRate,
    totalGoals,
    totalConceded,
    goalDifference: totalGoals - totalConceded,
    challengesUsed: trophy.num,
    purchasedTimes: trophy.alreadyPurchaseNum,
    canChallenge: this.canChallenge(trophyId),
    canPurchase: this.canPurchase(trophyId),
    copyStats: trophy.copyData.map((copy: TrophyCopyData) => ({
      teamId: copy.teamId,
      challengesUsed: copy.num,
      purchasedTimes: copy.alreadyPurchaseNum,
      canChallenge: this.canChallenge(trophyId, copy.teamId),
      canPurchase: this.canPurchase(trophyId, copy.teamId)
    }))
  };
};

/**
 * 获取总体统计
 * 基于TrophyService: 总体统计逻辑
 */
TrophyCopySchema.methods.getOverallStats = function(): any {
  let totalBattles = 0;
  let totalWins = 0;
  let totalGoals = 0;
  let totalConceded = 0;
  let totalChallengesUsed = 0;
  let totalPurchased = 0;

  const trophyStats: any = {};

  for (const trophy of this.allTrophyCopys) {
    const stats = this.getTrophyStats(trophy.id);
    if (stats) {
      trophyStats[trophy.id] = stats;
      totalBattles += stats.totalBattles;
      totalWins += stats.wins;
      totalGoals += stats.totalGoals;
      totalConceded += stats.totalConceded;
      totalChallengesUsed += stats.challengesUsed;
      totalPurchased += stats.purchasedTimes;
    }
  }

  return {
    totalTrophies: this.allTrophyCopys.length,
    totalBattles,
    totalWins,
    totalLosses: totalBattles - totalWins,
    overallWinRate: totalBattles > 0 ? Math.round((totalWins / totalBattles) * 100) : 0,
    totalGoals,
    totalConceded,
    overallGoalDifference: totalGoals - totalConceded,
    totalChallengesUsed,
    totalPurchased,
    trophies: trophyStats,
    lastUpdateTime: this.lastUpdateTime
  };
};

/**
 * 获取胜率
 * 基于TrophyService: 胜率计算逻辑
 */
TrophyCopySchema.methods.getWinRate = function(trophyId: number): number {
  const battles = this.getBattleHistory(trophyId);
  if (battles.length === 0) return 0;

  const wins = battles.filter((battle: TrophyBattleResult) => battle.result === 1).length;
  return Math.round((wins / battles.length) * 100);
};

/**
 * 获取总战斗次数
 * 基于TrophyService: 战斗次数统计逻辑
 */
TrophyCopySchema.methods.getTotalBattles = function(trophyId: number): number {
  return this.getBattleHistory(trophyId).length;
};

/**
 * 检查是否需要每日重置
 * 基于TrophyService: checkTrophyData方法中的重置检查逻辑
 */
TrophyCopySchema.methods.needsDailyReset = function(): boolean {
  const now = new Date();
  const lastUpdate = new Date(this.lastUpdateTimes);

  return now.getDate() !== lastUpdate.getDate() ||
         now.getMonth() !== lastUpdate.getMonth() ||
         now.getFullYear() !== lastUpdate.getFullYear();
};

/**
 * 验证杯赛数据
 * 基于TrophyService: checkTrophyData方法逻辑
 */
TrophyCopySchema.methods.validateTrophyData = function(): boolean {
  // 检查是否需要重置
  if (this.needsDailyReset()) {
    this.resetDailyTimes();
  }

  // 验证数据完整性
  for (const trophy of this.allTrophyCopys) {
    if (!trophy.id || trophy.id <= 0) return false;
    if (trophy.num < 0 || trophy.alreadyPurchaseNum < 0) return false;

    for (const copy of trophy.copyData) {
      if (!copy.teamId || copy.teamId <= 0) return false;
      if (copy.num < 0 || copy.alreadyPurchaseNum < 0) return false;
    }
  }

  return true;
};

/**
 * 转换为客户端杯赛数据
 * 基于TrophyService: makeClientTrophyList方法逻辑
 */
TrophyCopySchema.methods.toClientTrophyData = function(): any[] {
  // 确保数据有效性
  this.validateTrophyData();

  return this.allTrophyCopys.map((trophy: OneTrophy) => {
    const stats = this.getTrophyStats(trophy.id);

    return {
      id: trophy.id,
      num: trophy.num,
      alreadyPurchaseNum: trophy.alreadyPurchaseNum,
      canChallenge: this.canChallenge(trophy.id),
      canPurchase: this.canPurchase(trophy.id),
      purchaseInfo: this.getPurchaseInfo(trophy.id),
      stats: {
        totalBattles: stats?.totalBattles || 0,
        wins: stats?.wins || 0,
        winRate: stats?.winRate || 0,
        totalGoals: stats?.totalGoals || 0,
        totalConceded: stats?.totalConceded || 0
      },
      copyData: trophy.copyData.map((copy: TrophyCopyData) => ({
        teamId: copy.teamId,
        num: copy.num,
        alreadyPurchaseNum: copy.alreadyPurchaseNum,
        canChallenge: this.canChallenge(trophy.id, copy.teamId),
        canPurchase: this.canPurchase(trophy.id, copy.teamId),
        purchaseInfo: this.getPurchaseInfo(trophy.id, copy.teamId)
      }))
    };
  });
};

/**
 * 获取杯赛摘要
 * 基于TrophyService: 摘要信息获取需求
 */
TrophyCopySchema.methods.getTrophySummary = function(): any {
  const overallStats = this.getOverallStats();

  // 获取可挑战的杯赛数量
  const availableChallenges = this.allTrophyCopys.filter((trophy: OneTrophy) =>
    this.canChallenge(trophy.id)
  ).length;

  // 获取可购买次数的杯赛数量
  const availablePurchases = this.allTrophyCopys.filter((trophy: OneTrophy) =>
    this.canPurchase(trophy.id)
  ).length;

  return {
    uid: this.uid,
    totalTrophies: overallStats.totalTrophies,
    availableChallenges,
    availablePurchases,
    totalBattles: overallStats.totalBattles,
    overallWinRate: overallStats.overallWinRate,
    totalChallengesUsed: overallStats.totalChallengesUsed,
    totalPurchased: overallStats.totalPurchased,
    needsReset: this.needsDailyReset(),
    lastUpdateTime: this.lastUpdateTime.toISOString()
  };
};

/**
 * 转换为客户端杯赛数据
 * 基于TrophyService: makeClientTrophyList方法逻辑
 */
TrophyCopySchema.methods.toClientTrophyData = function(): any[] {
  // 确保数据有效性
  this.validateTrophyData();

  return this.allTrophyCopys.map((trophy: OneTrophy) => {
    const stats = this.getTrophyStats(trophy.id);

    return {
      id: trophy.id,
      num: trophy.num,
      alreadyPurchaseNum: trophy.alreadyPurchaseNum,
      canChallenge: this.canChallenge(trophy.id),
      canPurchase: this.canPurchase(trophy.id),
      purchaseInfo: this.getPurchaseInfo(trophy.id),
      stats: {
        totalBattles: stats?.totalBattles || 0,
        wins: stats?.wins || 0,
        winRate: stats?.winRate || 0,
        totalGoals: stats?.totalGoals || 0,
        totalConceded: stats?.totalConceded || 0
      },
      copyData: trophy.copyData.map((copy: TrophyCopyData) => ({
        teamId: copy.teamId,
        num: copy.num,
        alreadyPurchaseNum: copy.alreadyPurchaseNum,
        canChallenge: this.canChallenge(trophy.id, copy.teamId),
        canPurchase: this.canPurchase(trophy.id, copy.teamId),
        purchaseInfo: this.getPurchaseInfo(trophy.id, copy.teamId)
      })),
      recentBattles: this.getBattleHistory(trophy.id, 5).map((battle: TrophyBattleResult) => ({
        teamId: battle.teamId,
        result: battle.result,
        selfScore: battle.selfScore,
        enemyScore: battle.enemyScore,
        battleTime: battle.battleTime.toISOString(),
        rewardInfo: battle.rewardInfo
      }))
    };
  });
};

/**
 * 获取杯赛摘要
 * 基于TrophyService: 摘要信息获取需求
 */
TrophyCopySchema.methods.getTrophySummary = function(): any {
  const overallStats = this.getOverallStats();

  // 获取可挑战的杯赛数量
  const availableChallenges = this.allTrophyCopys.filter((trophy: OneTrophy) =>
    this.canChallenge(trophy.id)
  ).length;

  // 获取可购买次数的杯赛数量
  const availablePurchases = this.allTrophyCopys.filter((trophy: OneTrophy) =>
    this.canPurchase(trophy.id)
  ).length;

  // 获取最近的战斗记录
  const recentBattles = this.getBattleHistory(undefined, 10);

  // 统计奖励信息
  let totalRewards = 0;
  let totalItemRewards = 0;
  let totalHeroRewards = 0;

  for (const battle of this.battleHistory) {
    if (battle.rewardInfo) {
      totalRewards++;
      totalItemRewards += battle.rewardInfo.itemIdList?.length || 0;
      totalHeroRewards += battle.rewardInfo.heroIdList?.length || 0;
    }
  }

  return {
    uid: this.uid,
    totalTrophies: overallStats.totalTrophies,
    availableChallenges,
    availablePurchases,
    totalBattles: overallStats.totalBattles,
    overallWinRate: overallStats.overallWinRate,
    totalGoals: overallStats.totalGoals,
    totalConceded: overallStats.totalConceded,
    goalDifference: overallStats.overallGoalDifference,
    totalChallengesUsed: overallStats.totalChallengesUsed,
    totalPurchased: overallStats.totalPurchased,
    rewardSummary: {
      totalRewards,
      totalItemRewards,
      totalHeroRewards
    },
    recentBattles: recentBattles.map((battle: TrophyBattleResult) => ({
      trophyId: battle.trophyId,
      teamId: battle.teamId,
      result: battle.result,
      selfScore: battle.selfScore,
      enemyScore: battle.enemyScore,
      battleTime: battle.battleTime.toISOString()
    })),
    needsReset: this.needsDailyReset(),
    lastUpdateTime: this.lastUpdateTime.toISOString()
  };
};
