import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleType, BattleTypeUtils } from '@libs/game-constants';
import { TeamType } from '../../common/schemas/battle.schema';
import { BattlePeriodCalculator } from './calculators/battle-period-calculator';
import { BattleSkillSystem } from './systems/battle-skill-system';
import { BattleAttributeCalculator } from './calculators/battle-attribute-calculator';
import { BattleHeroSelector } from './selectors/battle-hero-selector';
import { BattleRecordGenerator } from './generators/battle-record-generator';
import { BattleCommentSystem } from './systems/battle-comment-system';
import { BattleInitializer } from './initializers/battle-initializer';
import { BattleRewardCalculator } from './calculators/battle-reward-calculator';
import {
  BattleData,
  BattleResult,
  BattleTeam,
  BattleHero,
  RoundData,
  TestConfig,
  HeroSearchResult,
  TiebreakerResult,
  AttackMode
} from './types/battle-data.types';
/**
 * 战斗计算引擎
 * 基于old项目room.js的核心战斗计算逻辑
 * 
 * 核心功能：
 * - 战斗回合计算
 * - 进攻防守计算
 * - 技能效果处理
 * - 战斗结果生成
 */
export class BattleEngine {
  private readonly logger = new Logger(BattleEngine.name);

  // 🔧 模块化组件 - 拆分后的战斗系统组件
  private periodCalculator: BattlePeriodCalculator; // 🔧 移除readonly，支持动态创建
  private readonly skillSystem: BattleSkillSystem;
  private readonly attributeCalculator: BattleAttributeCalculator;
  private readonly heroSelector: BattleHeroSelector;
  private readonly recordGenerator: BattleRecordGenerator;
  private readonly commentSystem: BattleCommentSystem;
  private readonly battleInitializer: BattleInitializer;

  constructor(private readonly gameConfig: GameConfigFacade) {
    // 🔧 先初始化BattleSkillSystem，然后注入到BattlePeriodCalculator
    this.skillSystem = new BattleSkillSystem(gameConfig);
    // 🔧 periodCalculator将在executeBattleRounds中根据battleType动态创建
    this.periodCalculator = new BattlePeriodCalculator(gameConfig, this.skillSystem); // 默认实例
    this.attributeCalculator = new BattleAttributeCalculator(gameConfig);
    this.heroSelector = new BattleHeroSelector(gameConfig);
    // 初始化BattleRecordGenerator，使用BattleRewardCalculator
    const battleRewardCalculator = new BattleRewardCalculator(gameConfig);
    this.recordGenerator = new BattleRecordGenerator(gameConfig, battleRewardCalculator);
    this.commentSystem = new BattleCommentSystem(gameConfig);
    this.battleInitializer = new BattleInitializer(gameConfig, this.attributeCalculator, this.skillSystem);
  }

  /**
   * 🔧 从BattleTeam数组直接计算战斗 - 重构版本
   * 消除BattleRoom中转，直接从业务数据进行计算，支持特殊战斗类型的额外初始化
   *
   * 📋 业务流程：
   * 1. 标准战斗数据初始化（BattleTeam → BattleData）
   * 2. 根据battleType应用特殊初始化步骤
   * 3. 执行战斗回合计算
   * 4. 生成战斗结果
   *
   * 🎯 支持的特殊战斗类型：
   * - PVP_WAR_OF_FAITH: 信仰之战（需要信仰等级参数）
   * - GUEST: 游客模式战斗
   * - PVE_TROPHY, PVE_LEAGUE等: PVE特殊初始化
   * - PVP_BUSINESS, PVP_GROUND_MATCH等: PVP特殊初始化
   */
  async executeBattleFromTeams(
    battleTeams: BattleTeam[],
    battleType: string,
    battleId?: string,
    specialConfig?: any  // 特殊配置参数（如信仰等级、PVE配置等）
  ): Promise<BattleResult> {
    this.logger.log(`🔧 开始战斗计算 - 类型: ${battleType}, 队伍数: ${battleTeams.length}`);

    try {
      // 第一步：标准战斗数据初始化（BattleTeam → BattleData）
      const battleData = await this.initializeBattleData(battleTeams, battleType, battleId);

      // 第二步：根据battleType应用特殊初始化步骤
      await this.applySpecialBattleInitialization(battleData, battleType, specialConfig);

      // 第三步：执行战斗回合计算
      const battleResult = await this.executeBattleRounds(battleData);

      // 第四步：生成战斗结果
      return await this.generateBattleResult(battleResult);
    } catch (error) {
      this.logger.error('❌ 战斗计算失败', error);
      throw error;
    }
  }

  /**
   * 🔧 应用特殊战斗类型的初始化步骤
   * 根据battleType调用相应的特殊初始化方法
   *
   * 📋 基于old项目room.js的初始化顺序：
   * 1. 先执行基础PVE/PVP初始化 (pveInit/pvpInit)
   * 2. 再执行特殊战斗类型初始化 (如initWarOfFaithBattleData)
   *
   * @param battleData 战斗数据 - 已完成标准初始化的数据
   * @param battleType 战斗类型 - 决定使用哪种特殊初始化
   * @param specialConfig 特殊配置 - 包含特殊战斗类型需要的参数
   */
  private async applySpecialBattleInitialization(
    battleData: BattleData,
    battleType: string,
    specialConfig?: any
  ): Promise<void> {
    this.logger.debug(`🔧 应用特殊战斗初始化 - 类型: ${battleType}`);

    try {
      // 🔧 根据战斗类型选择初始化策略
      switch (battleType) {
        // 信仰之战：先PVP基础初始化，再特殊初始化
        case BattleType.PVP_WAR_OF_FAITH:
          const pvpConfig = specialConfig?.pvpConfig;
          await this.battleInitializer.initPvpBattle(battleData, pvpConfig);
          this.logger.debug(`✅ PVP基础初始化完成 - 信仰之战`);

          const faithLevel = specialConfig?.faithLevel || 1;
          await this.battleInitializer.initWarOfFaithBattle(battleData, faithLevel);
          this.logger.debug(`✅ 信仰之战特殊初始化完成 - 等级: ${faithLevel}`);
          break;

        // 游客模式：独立的完整初始化（不需要基础PVE/PVP初始化）
        case BattleType.GUEST:
          await this.battleInitializer.initGuestModeBattle(battleData);
          this.logger.debug(`✅ 游客模式完整初始化完成`);
          break;

        // 测试战斗：独立的完整初始化
        case BattleType.TEST:
          await this.battleInitializer.initTestBattle(battleData);
          this.logger.debug(`✅ 测试战斗完整初始化完成`);
          break;

        // 标准PVE战斗类型：仅基础PVE初始化
        default:
          if (BattleTypeUtils.isPveBattle(battleType)) {
            await this.battleInitializer.initPveBattle(battleData);
            this.logger.debug(`✅ PVE基础初始化完成 - 类型: ${battleType}`);
          } else if (BattleTypeUtils.isPvpBattle(battleType)) {
            const pvpConfig = specialConfig?.pvpConfig;
            await this.battleInitializer.initPvpBattle(battleData, pvpConfig);
            this.logger.debug(`✅ PVP基础初始化完成 - 类型: ${battleType}`);
          } else {
            this.logger.debug(`📋 未知战斗类型，跳过特殊初始化 - 类型: ${battleType}`);
          }
          break;
      }
    } catch (error) {
      this.logger.error(`❌ 特殊战斗初始化失败 - 类型: ${battleType}`, error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 🔧 执行战斗回合 - 完整业务流程
   * 基于old项目room.js的calcEventFlow逻辑，集成完整的回合管理系统
   *
   * 业务流程：
   * 1. 初始化战斗数据和回合记录
   * 2. 循环执行回合直到战斗结束
   * 3. 每回合包含：开始处理 → 事件计算 → 三阶段战斗 → 结束处理
   * 4. 处理决胜局和最终结果
   */
  public async executeBattleRounds(battleData: BattleData): Promise<BattleData> {
    this.logger.debug('开始执行战斗回合循环');

    // 🔧 根据battleType动态创建BattlePeriodCalculator
    this.periodCalculator = new BattlePeriodCalculator(this.gameConfig, this.skillSystem, battleData.battleType);

    // 第一步：初始化战斗数据（委托给BattleInitializer）
    this.battleInitializer.initRoundResultData(battleData);

    // 第二步：主战斗循环 - 基于old项目的事件驱动模式
    while (battleData.battleTime < battleData.maxBattleTime) {
      try {
        this.logger.debug(`🔄 开始第${battleData.roundIndex + 1}回合 - 当前时间: ${battleData.battleTime}/${battleData.maxBattleTime}`);

        // 2.1 回合开始处理 - 计算事件时间和攻击方
        this.eachRoundStart(battleData);

        // 2.2 检查战斗是否应该结束
        if (battleData.battleTime >= battleData.maxBattleTime) {
          this.logger.debug('⏰ 战斗时间到达上限，结束战斗');
          break;
        }

        this.logger.debug(`⚔️ 回合${battleData.roundIndex}执行 - 攻击方: ${battleData.roundAttacker}, 时间: ${battleData.battleTime}`);
        this.logger.debug(`📊 当前比分: ${battleData.teamA.score} : ${battleData.teamB.score}`);

        // 2.3 执行当前回合的三阶段战斗
        this.logger.debug(`🎯 开始执行三阶段战斗`);
        const roundResult = await this.executeBattleThreePeriod(battleData);
        this.logger.debug(`✅ 三阶段战斗完成 - 是否进球: ${roundResult.isGoal}`);

        // 🚨 注意：比分更新已在calcEndPeriod中处理，此处不应重复更新！
        // 2.4 更新统计数据（不更新比分，避免重复）
        if (roundResult.isGoal) {
          this.logger.debug(`⚽ 检测到进球 - 攻击方: ${battleData.roundAttacker}`);
          this.logger.debug(`📊 比分更新前: ${battleData.teamA.score}:${battleData.teamB.score}`);

          // 只更新统计数据，不更新比分（比分已在calcEndPeriod中更新）
          if (battleData.roundAttacker === 'teamA') {
            battleData.teamA.statistic.shotsOnTarget++;
          } else if (battleData.roundAttacker === 'teamB') {
            battleData.teamB.statistic.shotsOnTarget++;
          }

          this.logger.debug(`📊 比分更新后: ${battleData.teamA.score}:${battleData.teamB.score}`);
        }

        // 注意：统计数据更新应该在具体的计算函数中处理，不在主循环中
        // 射门统计在calcEndPeriod中处理，突破统计在calcMiddlePeriod中处理

        // 2.5 回合结束处理 - 清理临时数据和技能效果
        this.logger.debug(`🔚 开始回合结束处理`);
        this.eachRoundEnd(battleData);
        this.logger.debug(`✅ 回合${battleData.roundIndex}结束 - 下一回合准备中`);

        // 2.6 安全检查：防止无限循环
        if (battleData.roundIndex >= 100) {
          this.logger.warn(`⚠️ 战斗回合数过多，强制结束: ${battleData.roundIndex}`);
          break;
        }

        // 2.7 检查比分是否导致提前结束
        const currentScore = `${battleData.teamA.score}:${battleData.teamB.score}`;
        this.logger.debug(`📊 回合结束时比分: ${currentScore}`);

      } catch (error) {
        this.logger.error(`回合${battleData.roundIndex}执行失败`, error);
        break;
      }
    }

    // 第三步：处理决胜局（如果需要）
    const finalScore = { teamA: battleData.teamA.score, teamB: battleData.teamB.score };
    if (finalScore.teamA === finalScore.teamB) {
      this.logger.debug('比分平局，进入决胜局处理');
      const tiebreakerResult = this.tiebreaker(battleData);
      this.logger.debug(`决胜局结果: ${tiebreakerResult.winner} 获胜`);
    }

    // 第四步：更新最终统计数据
    this.updateFinalStatistics(battleData);

    this.logger.debug(`战斗结束，最终比分 ${battleData.teamA.score}:${battleData.teamB.score}，总回合数: ${battleData.roundIndex + 1}`);
    return battleData;
  }

  /**
   * 🚀 执行单回合战斗 - 核心三阶段战斗逻辑
   * 基于old项目room.js的executeRoundBattle方法，完整实现足球战斗系统
   *
   * 📋 完整业务流程：
   *
   * 【第一阶段：回合初始化】
   * 1. 确定攻击方(roundAttacker)和防守方
   * 2. 基于队伍士气和随机因素计算攻击模式(AttackMode 1-9)
   * 3. 初始化回合数据结构(RoundData)
   * 4. 记录回合开始时的队伍状态和时间
   *
   * 【第二阶段：三阶段战斗计算】
   * 5. 🎯 发起阶段 (Start Period)：
   *    - 基于攻击模式选择A1主攻球员(BattleHeroSelector)
   *    - 使用AttackMode配置表计算基础成功率
   *    - 调用getHeroBattleAttr获取球员属性加成
   *    - 应用技能效果和士气影响
   *    - 生成动作ID和战斗评论
   *
   * 6. 🎯 推进阶段 (Middle Period)：
   *    - 发起成功后选择防守球员B
   *    - 基于配置表进行A1 vs B的突破计算
   *    - 处理位置克制和战术影响
   *    - 决定是否成功推进到射门阶段
   *
   * 7. 🎯 射门阶段 (End Period)：
   *    - 推进成功后选择A2协助球员和门将GK
   *    - 进行A1+A2 vs GK+B的最终射门计算
   *    - 基于门将属性和防守加成判定结果
   *    - 更新比分、统计数据和球员评分
   *
   * 【第三阶段：结果处理与记录】
   * 8. 生成完整的三阶段战斗记录(BattleRecord)
   * 9. 更新球员评分映射(ballerScoreMap)
   * 10. 处理技能持续效果和冷却时间
   * 11. 生成战斗评论和动作描述
   * 12. 返回标准化的回合结果数据
   *
   * @param battleData 完整战斗数据 - 包含双方队伍、配置、技能状态等
   * @returns 回合结果 - { isGoal: boolean, roundData: RoundData }
   */
  private async executeBattleThreePeriod(battleData: BattleData): Promise<{ isGoal: boolean; roundData: RoundData }> {
    const attackerTeam = this.getTeamInfoByroundAttacker(battleData, battleData.roundAttacker!);
    const defenderTeam = this.getRoundDefenderType(battleData.roundAttacker!) === 'teamA'
      ? battleData.teamA
      : battleData.teamB;
    // 第一步：计算攻击模式
    const attackMode = this.periodCalculator.calcAttackMode(attackerTeam);

    // 第二步：执行三阶段战斗，集成球员选择和战报记录
    // 2.1 发起阶段 (Start Period)
    // 2.1 发起阶段 (Start Period)
    // 选择A1攻击球员并存储到roundInfo中
    const attackerA1 = this.heroSelector.getRandomAttacker(
      attackerTeam, 'A1', attackMode, 0
    );

    // 将选择的球员存储到roundInfo中，供后续计算使用（包含属性信息）
    const attackerA1Hero = this.getHeroById(attackerTeam, attackerA1);
    attackerTeam.roundInfo.A1Info = {
      heroId: attackerA1,
      attrType1: 1, // 速度属性
      attrValue1: attackerA1Hero?.attributes?.speed?.cur || 0,
      addValue1: 0, // 技能加成，暂时为0
      attrType2: 2, // 射门属性
      attrValue2: attackerA1Hero?.attributes?.finishing?.cur || 0,
      addValue2: 0
    };

    // 特殊情况选择防守球员（角球等）
    if (attackMode === AttackMode.CornerKick) { // 角球
      const defenderA1 = this.heroSelector.getRandomDefender(
        defenderTeam, 'A1', 0
      );
      // 存储防守球员信息（包含属性信息）
      const defenderA1Hero = this.getHeroById(defenderTeam, defenderA1);
      defenderTeam.roundInfo.A1Info = {
        heroId: defenderA1,
        attrType1: 4, // 防守属性
        attrValue1: defenderA1Hero?.attributes?.standingTackle?.cur || 0,
        addValue1: 0,
        attrType2: 6, // 体力属性
        attrValue2: defenderA1Hero?.attributes?.strength?.cur || 0,
        addValue2: 0
      };
    }

    const startResult = await this.periodCalculator.calcStartPeriod(
      attackerTeam, defenderTeam, attackMode, battleData.roundIndex, 0
    );

    // 计算并设置发起阶段评论ID
    if (this.commentSystem && startResult.actionID) {
      const startComment = await this.commentSystem.calcComment(
        startResult.actionID,
        0, // 发起阶段
        startResult.success,
        attackMode,
        [attackerA1Hero?.name || attackerA1 || '']
      );
      startResult.startCommentId = startComment.startCommentID;
      startResult.resultCommentId = startComment.resultCommentID;
    }

    // 记录发起阶段战报
    this.recordGenerator.recordStartPeriod(
      battleData.battleRecord.battleRoundInfo,
      battleData.roundIndex,
      attackerTeam,
      defenderTeam,
      attackMode,
      startResult
    );

    let middleResult = null;
    let endResult = null;
    let isGoal = false;

    // 2.2 推进阶段 (Middle Period) - 头球、任意球、点球跳过推进阶段
    if (startResult.success && this.periodCalculator.needBreakThroughPeriod(attackMode)) {
      // 选择防守球员B（特定攻击模式）
      if ([AttackMode.LongShot, AttackMode.Push, AttackMode.Scramble, AttackMode.Lob, AttackMode.OneOnOne, AttackMode.CornerKick].includes(attackMode)) {
        const defenderB = this.heroSelector.getRandomDefender(
          defenderTeam, 'A1', 1
        );
        // 存储防守球员B信息（包含属性信息）
        const defenderBHero = this.getHeroById(defenderTeam, defenderB);
        defenderTeam.roundInfo.BInfo = {
          heroId: defenderB,
          attrType1: 4, // 防守属性
          attrValue1: defenderBHero?.attributes?.standingTackle?.cur || 0,
          addValue1: 0,
          attrType2: 6, // 体力属性
          attrValue2: defenderBHero?.attributes?.strength?.cur || 0,
          addValue2: 0
        };
      }

      middleResult = await this.periodCalculator.calcMiddlePeriod(
        attackerTeam, defenderTeam, attackMode, battleData.roundIndex, 0
      );

      // 计算并设置推进阶段评论ID
      if (this.commentSystem && middleResult.actionID) {
        const middleComment = await this.commentSystem.calcComment(
          middleResult.actionID,
          1, // 推进阶段
          middleResult.success,
          attackMode,
          [attackerA1Hero?.name || attackerA1 || '', defenderTeam.roundInfo.BInfo?.heroId || '']
        );
        middleResult.startCommentId = middleComment.startCommentID;
        middleResult.resultCommentId = middleComment.resultCommentID;
      }

      // 记录推进阶段战报
      this.recordGenerator.recordMiddlePeriod(
        battleData.battleRecord,
        battleData.roundIndex,
        attackerTeam,
        defenderTeam,
        attackMode,
        middleResult
      );

      // 2.3 射门阶段 (End Period) - 推进成功或跳过推进阶段才执行
      if ((middleResult && middleResult.success) || !this.periodCalculator.needBreakThroughPeriod(attackMode)) {
        // 选择A2攻击球员（特定攻击模式）
        if ([AttackMode.Header, AttackMode.Push, AttackMode.Scramble, AttackMode.CornerKick].includes(attackMode)) {
          const attackerA2 = this.heroSelector.getRandomAttacker(
            attackerTeam, 'A2', attackMode, 2
          );
          // 存储攻击球员A2信息（包含属性信息）
          const attackerA2Hero = this.getHeroById(attackerTeam, attackerA2);
          attackerTeam.roundInfo.A2Info = {
            heroId: attackerA2,
            attrType1: 3, // 传球属性
            attrValue1: attackerA2Hero?.attributes?.passing?.cur || 0,
            addValue1: 0,
            attrType2: 5, // 盘带属性
            attrValue2: attackerA2Hero?.attributes?.dribbling?.cur || 0,
            addValue2: 0
          };
        }

        // 选择防守球员B（射门阶段）
        if ([AttackMode.Header, AttackMode.Push, AttackMode.Scramble].includes(attackMode)) {
          const defenderB = this.heroSelector.getRandomDefender(
            defenderTeam, 'A1', 2
          );
          // 存储防守球员B信息（射门阶段，包含属性信息）
          const defenderBHero2 = this.getHeroById(defenderTeam, defenderB);
          defenderTeam.roundInfo.BInfo = {
            heroId: defenderB,
            attrType1: 4, // 防守属性
            attrValue1: defenderBHero2?.attributes?.standingTackle?.cur || 0,
            addValue1: 0,
            attrType2: 6, // 体力属性
            attrValue2: defenderBHero2?.attributes?.strength?.cur || 0,
            addValue2: 0
          };
        }

        // 选择门将（射门阶段总是需要门将）
        const goalkeeper = this.heroSelector.getRandomDefender(
          defenderTeam, 'GK', 2
        );
        // 存储门将信息（包含属性信息）
        const goalkeeperHero = this.getHeroById(defenderTeam, goalkeeper);
        defenderTeam.roundInfo.GKInfo = {
          heroId: goalkeeper,
          attrType1: 7, // 扑救属性
          attrValue1: goalkeeperHero?.attributes?.save?.cur || 0,
          addValue1: 0,
          attrType2: 6, // 体力属性
          attrValue2: goalkeeperHero?.attributes?.strength?.cur || 0,
          addValue2: 0
        };

        endResult = await this.periodCalculator.calcEndPeriod(
          attackerTeam, defenderTeam, attackMode, battleData.roundIndex, 0
        );
        isGoal = endResult ? endResult.success : false;

        // 计算并设置射门阶段评论ID
        if (this.commentSystem && endResult && endResult.actionID) {
          const endComment = await this.commentSystem.calcComment(
            endResult.actionID,
            2, // 射门阶段
            endResult.success,
            attackMode,
            [
              attackerA1Hero?.name || attackerA1 || '',
              attackerTeam.roundInfo.A2Info?.heroId || '',
              defenderTeam.roundInfo.BInfo?.heroId || '',
              goalkeeperHero?.name || goalkeeper || ''
            ]
          );
          endResult.startCommentId = endComment.startCommentID;
          endResult.resultCommentId = endComment.resultCommentID;
        }

        // 记录射门阶段战报
        this.recordGenerator.recordEndPeriod(
          battleData.battleRecord,
          battleData.roundIndex,
          attackerTeam,
          defenderTeam,
          attackMode,
          endResult,
          { teamA: battleData.teamA.score || 0, teamB: battleData.teamB.score || 0 }
        );
      }
    }

    // 第三步：记录回合数据
    const roundData: RoundData = {
      roundIndex: battleData.roundIndex,
      attackerType: battleData.roundAttacker!,
      attackMode,
      startPeriod: startResult,
      middlePeriod: middleResult,
      endPeriod: endResult,
      isGoal,
      battleTime: battleData.battleTime
    };

    // 第四步：生成回合摘要
    if (this.commentSystem) {
      const summaryResult = await this.commentSystem.generateRoundSummary(
        battleData.roundIndex,
        attackerTeam,
        defenderTeam,
        [startResult, middleResult, endResult].filter(Boolean)
      );
      roundData.roundSummary = summaryResult.roundSummary;
    }

    return { isGoal, roundData };
  }





  /**
   * 🔧 生成战斗结果 - 完整业务流程
   * 基于old项目的三阶段战斗结果格式，使用模块化组件
   *
   * 业务流程：
   * 1. 确定获胜方和最终比分
   * 2. 生成完整的战斗记录
   * 3. 计算统计数据
   * 4. 格式化返回结果
   */
  /**
   * 🚀 初始化战斗数据（重构版本）
   * 现在支持从BattleTeam数组直接初始化，委托给BattleInitializer处理
   *
   * @param battleTeams 战斗队伍数组 - [teamA, teamB]
   * @param battleType 战斗类型
   * @param battleId 战斗ID（可选）
   * @returns Promise<BattleData> 完整的战斗数据 - 可直接用于战斗执行
   */
  public async initializeBattleData(
    battleTeams: BattleTeam[],
    battleType: string,
    battleId?: string
  ): Promise<BattleData> {
    return await this.battleInitializer.initializeBattleData(battleTeams, battleType, battleId);
  }

  /**
   * 🔧 信仰之战初始化（公共接口）
   * 为BattleManager提供的信仰之战初始化接口
   */
  public async initWarOfFaithBattle(battleData: BattleData, faithLevel: number): Promise<void> {
    return await this.battleInitializer.initWarOfFaithBattle(battleData, faithLevel);
  }

  /**
   * 🔧 游客模式初始化（公共接口）
   * 为BattleManager提供的游客模式初始化接口
   */
  public async initGuestModeBattle(battleData: BattleData): Promise<void> {
    return await this.battleInitializer.initGuestModeBattle(battleData);
  }

  /**
   * 🔧 PVE战斗初始化（公共接口）
   * 为BattleManager提供的PVE战斗初始化接口
   */
  public async initPveBattle(battleData: BattleData): Promise<void> {
    return await this.battleInitializer.initPveBattle(battleData);
  }

  /**
   * 🔧 PVP战斗初始化（公共接口）
   * 为BattleManager提供的PVP战斗初始化接口
   */
  public async initPvpBattle(battleData: BattleData, pvpConfig?: any): Promise<void> {
    return await this.battleInitializer.initPvpBattle(battleData, pvpConfig);
  }

  /**
   * 🔧 生成战斗结果 - 增强版本
   * 唯一的战斗结果生成方法，生成标准BattleResult格式，集成评论系统数据
   */
  public async generateBattleResult(battleData: BattleData): Promise<BattleResult> {
    // 确定获胜方
    const winner = battleData.teamA.score > battleData.teamB.score
      ? TeamType.TeamA
      : battleData.teamB.score > battleData.teamA.score
        ? TeamType.TeamB
        : 'draw';

    // 从skillSystem获取技能记录
    const skillRecords = this.skillSystem.getTriggeredSkillRecords();
    const formattedSkillRecords = [
      {
        durRecord: skillRecords.durative[0] || [],
        insRecord: skillRecords.instant[0] || [],
        nextAtkRecord: skillRecords.nextAttack[0] || []
      },
      {
        durRecord: skillRecords.durative[1] || [],
        insRecord: skillRecords.instant[1] || [],
        nextAtkRecord: skillRecords.nextAttack[1] || []
      }
    ];

    // 🔧 生成评论系统相关数据
    const commentSummary = await this.commentSystem.generateCommentSummary(battleData);

    // 构建完整的战斗结果
    const battleResult: BattleResult = {
      battleId: battleData.battleId!,
      battleType: battleData.battleType,
      winner: winner as any,
      finalScore: {
        teamA: battleData.teamA.score,
        teamB: battleData.teamB.score
      },
      battleRecord: battleData.battleRecord,
      statistics: {
        totalTime: battleData.battleTime,
        totalRounds: battleData.roundIndex + 1,
        teamA: battleData.teamA.statistic,
        teamB: battleData.teamB.statistic
      },
      skillRecords: formattedSkillRecords,
      commentSummary,
      timestamp: Date.now()
    };

    this.logger.debug(`战斗结果生成完成: ${winner} 获胜, 比分 ${battleData.teamA.score}:${battleData.teamB.score}`);
    return battleResult;
  }

  /**
   * 🔧 计算下一次事件触发时间以及事件队列
   * 基于old项目: Room.prototype.calcNextEvent
   * 核心功能：基于士气槽和士气加速度计算下次事件时间
   */
  calcNextEvent(battleData: BattleData): { nextEventTime: number; attacker: string } {
    try {
      // 从系统参数获取最大士气槽值 - 基于old项目SystemParam ID=1
      const maxMoraleSlot = 200000; // old项目中的实际值

      // 计算剩余士气槽
      const leftMoraleSlotA = maxMoraleSlot - (battleData.teamA.attr.moraleSlot || 0);
      const leftMoraleSlotB = maxMoraleSlot - (battleData.teamB.attr.moraleSlot || 0);

      // 计算到达满士气槽所需时间
      const moraleAccelerationA = battleData.teamA.attr.moraleAcceleration || 1;
      const moraleAccelerationB = battleData.teamB.attr.moraleAcceleration || 1;

      let costTimeA = Math.round(leftMoraleSlotA / moraleAccelerationA * 2);
      let costTimeB = Math.round(leftMoraleSlotB / moraleAccelerationB * 2);

      this.logger.debug(`士气时间计算: A队=${costTimeA}, B队=${costTimeB}`);

      // 特殊战斗类型处理（如首场战斗）
      if (battleData.battleType == BattleType.FIRST_BATTLE && battleData.roundIndex !== undefined) {
        // 基于预设的战斗回合信息
        const roundInfo = battleData.firstBattleData?.battleRoundInfo?.[battleData.roundIndex];
        if (roundInfo) {
          const attacker = roundInfo.attackerType;
          if (attacker === 'teamA') {
            costTimeA = roundInfo.eventTime - (battleData.battleTime || 0);
            costTimeB = costTimeA + 1;
          } else {
            costTimeA = costTimeB + 1;
            costTimeB = roundInfo.eventTime - (battleData.battleTime || 0);
          }
        }
      }

      // 确定下次事件的发起方
      let nextAttacker: 'teamA' | 'teamB' = 'teamA';
      let nextEventTime = costTimeA;

      if (costTimeA <= costTimeB) {
        nextAttacker = 'teamA';
        nextEventTime = costTimeA;
        // 基于old项目：更新双方士气槽
        battleData.teamA.attr.moraleSlot = maxMoraleSlot;
        battleData.teamB.attr.moraleSlot += Math.round(costTimeA * moraleAccelerationB / 2);
        if (battleData.teamB.attr.moraleSlot > maxMoraleSlot) {
          battleData.teamB.attr.moraleSlot = maxMoraleSlot;
        }
      } else {
        nextAttacker = 'teamB';
        nextEventTime = costTimeB;
        // 基于old项目：更新双方士气槽
        battleData.teamB.attr.moraleSlot = maxMoraleSlot;
        battleData.teamA.attr.moraleSlot += Math.round(costTimeB * moraleAccelerationA / 2);
        if (battleData.teamA.attr.moraleSlot > maxMoraleSlot) {
          battleData.teamA.attr.moraleSlot = maxMoraleSlot;
        }
      }

      // 更新战斗时间
      const oldBattleTime = battleData.battleTime || 0;
      battleData.battleTime = oldBattleTime + nextEventTime;
      battleData.roundAttacker = nextAttacker as any; // 临时类型转换，需要修复calcNextEvent返回类型

      this.logger.debug(`⏰ 时间推进: ${oldBattleTime} + ${nextEventTime} = ${battleData.battleTime} (最大: ${battleData.maxBattleTime})`);
      this.logger.debug(`👥 下次攻击方: ${nextAttacker}`);

      return {
        nextEventTime,
        attacker: nextAttacker
      };

    } catch (error) {
      this.logger.error('计算下次事件失败', error);
      // 返回默认值
      return {
        nextEventTime: 100,
        attacker: 'teamA'
      };
    }
  }

  /**
   * 🔧 根据攻击方获取队伍信息
   * 基于old项目: Room.prototype.getTeamInfoByroundAttacker
   */
  getTeamInfoByroundAttacker(battleData: BattleData, roundAttacker: string): BattleTeam {
    try {
      if (roundAttacker === 'teamA') {
        return battleData.teamA;
      } else if (roundAttacker === 'teamB') {
        return battleData.teamB;
      } else {
        this.logger.warn(`未知的攻击方类型: ${roundAttacker}`);
        return battleData.teamA; // 默认返回A队
      }
    } catch (error) {
      this.logger.error('获取攻击方队伍信息失败', error);
      return battleData.teamA;
    }
  }

  /**
   * 🔧 根据队伍类型获取队伍信息
   * 基于old项目: Room.prototype.getTeamInfoByTeamType
   */
  getTeamInfoByTeamType(battleData: BattleData, teamType: string): BattleTeam {
    try {
      if (teamType === 'teamA') {
        return battleData.teamA;
      } else if (teamType === 'teamB') {
        return battleData.teamB;
      } else {
        this.logger.warn(`未知的队伍类型: ${teamType}`);
        return battleData.teamA; // 默认返回A队
      }
    } catch (error) {
      this.logger.error('获取队伍信息失败', error);
      return battleData.teamA;
    }
  }

  /**
   * 🔧 获取回合防守方类型
   * 基于old项目: Room.prototype.getRoundDefenderType
   */
  getRoundDefenderType(roundAttacker: string): string {
    try {
      if (roundAttacker === 'teamA') {
        return 'teamB';
      } else if (roundAttacker === 'teamB') {
        return 'teamA';
      } else {
        this.logger.warn(`未知的攻击方类型: ${roundAttacker}`);
        return 'teamB'; // 默认返回B队作为防守方
      }
    } catch (error) {
      this.logger.error('获取防守方类型失败', error);
      return 'teamB';
    }
  }

  /**
   * 🔧 判断是否PVE战斗
   * 基于old项目: Room.prototype.isPveBattle
   */
  isPveBattle(battleType: string): boolean {
    try {
      const pveTypes = [
        'PVE',
        'League',
        'Cup',
        'Tournament',
        'Challenge',
        'FirstBattle',
        'MiddleEast',
        'GulfCup',
        'MLS'
      ];

      return pveTypes.includes(battleType);
    } catch (error) {
      this.logger.error('判断PVE战斗类型失败', error);
      return false;
    }
  }





  /**
   * 🔧 每回合开始处理
   * 基于old项目: Room.prototype.eachRoundStart
   */
  eachRoundStart(battleData: BattleData): void {
    try {
      // 1. 计算本次触发事件的时间和队伍
      this.calcNextEvent(battleData);

      if (battleData.battleTime > (battleData.maxBattleTime || 5400)) { // 90分钟 = 5400秒
        this.logger.log('战斗时间超限，战斗结束');
        return;
      }

      // 2. 回合数据初始化
      const battleRoundInfo = {
        eventTime: battleData.battleTime,
        moraleA: battleData.teamA.attr?.morale || 0,
        moraleB: battleData.teamB.attr?.morale || 0,
        attackerType: battleData.roundAttacker,
        attackMode: 0,
        periodInfo: [],
        scoreA: battleData.teamA.score || 0,
        scoreB: battleData.teamB.score || 0
      };

      // 添加到战斗记录
      if (!battleData.battleRecord.battleRoundInfo) {
        battleData.battleRecord.battleRoundInfo = [];
      }

      battleData.battleRecord.battleRoundInfo.push(battleRoundInfo);

      // 更新回合索引
      battleData.roundIndex = battleData.battleRecord.battleRoundInfo.length - 1;

      this.logger.debug(`回合开始: 第${battleData.roundIndex + 1}回合, 攻击方: ${battleData.roundAttacker}`);

    } catch (error) {
      this.logger.error('回合开始处理失败', error);
    }
  }

  /**
   * 🔧 每回合结束处理
   * 基于old项目: Room.prototype.eachRoundEnd
   */
  eachRoundEnd(battleData: BattleData): void {
    try {
      // 🔧 完整的回合临时数据清理（基于old项目逻辑）
      this.cleanupRoundData(battleData.teamA);
      this.cleanupRoundData(battleData.teamB);

      // 🔧 清空发起进攻者的气槽值
      if (battleData.roundAttacker === 'teamA') {
        battleData.teamA.attr.moraleSlot = 0;
      } else if (battleData.roundAttacker === 'teamB') {
        battleData.teamB.attr.moraleSlot = 0;
      }

      // 🔧 处理技能效果的持续时间
      this.processSkillDuration(battleData);

      // 🔧 回合Index+1
      battleData.roundIndex++;

      this.logger.debug(`回合结束: 第${battleData.roundIndex}回合处理完成`);

    } catch (error) {
      this.logger.error('回合结束处理失败', error);
    }
  }

  /**
   * 🔧 清理队伍回合数据
   * 基于old项目: eachRoundEnd中的详细清理逻辑
   */
  private cleanupRoundData(team: any): void { // 保持any类型，因为需要访问接口中未定义的属性
    try {
      if (!team.roundInfo) {
        team.roundInfo = {};
      }

      // 🔧 清理攻击者类型
      team.roundInfo.attackerType = "";

      // 🔧 清理球员信息（A1, A2, B, GK）
      const defaultHeroInfo = {
        heroUid: "",
        attrType1: 0,
        attrValue1: 0,
        addValue1: 0,
        attrType2: 0,
        attrValue2: 0,
        addValue2: 0
      };

      team.roundInfo.A1Info = { ...defaultHeroInfo };
      team.roundInfo.A2Info = { ...defaultHeroInfo };
      team.roundInfo.BInfo = { ...defaultHeroInfo };
      team.roundInfo.GKInfo = { ...defaultHeroInfo };

      // 🔧 清理阶段信息（三个阶段）- 使用当前项目命名
      team.roundInfo.periodInfo = [
        { actionId: 0, startCommentId: 0, resultCommentId: 0, percent: 0, result: 0, skillEffectList: [] },
        { actionId: 0, startCommentId: 0, resultCommentId: 0, percent: 0, result: 0, skillEffectList: [] },
        { actionId: 0, startCommentId: 0, resultCommentId: 0, percent: 0, result: 0, skillEffectList: [] }
      ];

      // 🔧 清理攻击模式
      team.roundInfo.attackMode = 0;

      this.logger.debug(`队伍${team.teamSide}回合数据清理完成`);
    } catch (error) {
      this.logger.error(`清理队伍回合数据失败: ${team.teamSide}`, error);
    }
  }

  /**
   * 🔧 处理技能持续时间
   * 辅助方法：处理技能效果的持续时间递减
   */
  private processSkillDuration(battleData: any): void {
    try {
      // 处理A队技能效果
      if (battleData.teamA.skillEffects) {
        battleData.teamA.skillEffects = battleData.teamA.skillEffects.filter((effect: any) => {
          if (effect.duration > 0) {
            effect.duration--;
            return effect.duration > 0;
          }
          return false;
        });
      }

      // 处理B队技能效果
      if (battleData.teamB.skillEffects) {
        battleData.teamB.skillEffects = battleData.teamB.skillEffects.filter((effect: any) => {
          if (effect.duration > 0) {
            effect.duration--;
            return effect.duration > 0;
          }
          return false;
        });
      }

    } catch (error) {
      this.logger.error('处理技能持续时间失败', error);
    }
  }

  /**
   * 🔧 决胜局处理
   * 基于old项目: Room.prototype.tiebreaker
   * 处理平局情况下的决胜逻辑
   */
  tiebreaker(battleData: BattleData): TiebreakerResult {
    try {
      const teamAScore = battleData.teamA.score || 0;
      const teamBScore = battleData.teamB.score || 0;

      // 如果不是平局，直接返回结果
      if (teamAScore !== teamBScore) {
        return {
          winner: teamAScore > teamBScore ? TeamType.TeamA : TeamType.TeamB,
          needTiebreaker: false,
          finalScoreA: teamAScore,
          finalScoreB: teamBScore
        };
      }

      this.logger.debug('进入决胜局处理');

      // 决胜局逻辑：基于队伍综合实力
      const teamAPower = this.calculateTeamPower(battleData.teamA);
      const teamBPower = this.calculateTeamPower(battleData.teamB);

      // 添加随机因素
      const randomFactorA = Math.random() * 0.1; // 10%随机因素
      const randomFactorB = Math.random() * 0.1;

      const finalPowerA = teamAPower * (1 + randomFactorA);
      const finalPowerB = teamBPower * (1 + randomFactorB);

      const winner = finalPowerA > finalPowerB ? TeamType.TeamA : TeamType.TeamB;

      // 决胜局比分：原比分基础上获胜方+1
      const finalScoreA = teamAScore + (winner === TeamType.TeamA ? 1 : 0);
      const finalScoreB = teamBScore + (winner === TeamType.TeamB ? 1 : 0);

      // 更新战斗数据
      battleData.teamA.score = finalScoreA;
      battleData.teamB.score = finalScoreB;

      this.logger.debug(`决胜局结果: ${winner} 获胜, 比分 ${finalScoreA}:${finalScoreB}`);

      return {
        winner,
        needTiebreaker: true,
        finalScoreA,
        finalScoreB,
        powerA: teamAPower,
        powerB: teamBPower
      };

    } catch (error) {
      this.logger.error('决胜局处理失败', error);
      // 返回默认结果
      return {
        winner: TeamType.TeamA,
        needTiebreaker: false,
        finalScoreA: battleData.teamA.score || 0,
        finalScoreB: battleData.teamB.score || 0
      };
    }
  }

  /**
   * 🔧 更新最终统计数据
   * 基于old项目room.js的battleEndInfoJsonToClient逻辑
   */
  private updateFinalStatistics(battleData: BattleData): void {
    try {
      // 基于old项目逻辑：控球率基于士气计算
      const moraleA = battleData.teamA.attr?.morale || 500;
      const moraleB = battleData.teamB.attr?.morale || 500;
      const ctrlBallPerA = Math.round(moraleA / (moraleA + moraleB) * 100);
      const ctrlBallPerB = 100 - ctrlBallPerA;

      battleData.teamA.statistic.possession = ctrlBallPerA;
      battleData.teamB.statistic.possession = ctrlBallPerB;

      // 基于old项目逻辑：突破成功率计算
      // breakPer = Math.round(breakSucNum / breakNum * 100)
      // 注意：breaks和successfulBreaks在战斗过程中已经更新，这里不需要重新计算

      this.logger.debug(`统计数据更新完成: A队控球率${ctrlBallPerA}%, B队控球率${ctrlBallPerB}%`);
      this.logger.debug(`A队统计: 射门${battleData.teamA.statistic.shots}, 突破${battleData.teamA.statistic.breaks}/${battleData.teamA.statistic.successfulBreaks}`);
      this.logger.debug(`B队统计: 射门${battleData.teamB.statistic.shots}, 突破${battleData.teamB.statistic.breaks}/${battleData.teamB.statistic.successfulBreaks}`);
    } catch (error) {
      this.logger.error('更新最终统计数据失败', error);
    }
  }



  /**
   * 🔧 计算队伍综合实力
   * 辅助方法：用于决胜局的实力对比
   */
  private calculateTeamPower(team: BattleTeam): number {
    try {
      const attack = team.totalAttack || 0;
      const defend = team.totalDefend || 0;
      const morale = team.attr?.morale || 500;

      // 综合实力 = 攻击力 * 0.4 + 防守力 * 0.4 + 士气 * 0.2
      const power = attack * 0.4 + defend * 0.4 + morale * 0.2;

      return power;
    } catch (error) {
      this.logger.error('计算队伍实力失败', error);
      return 1000; // 默认实力值
    }
  }

  /**
   * 🔧 获取PVE通用配置
   * 基于old项目: Room.prototype.getPveCommonConfig
   */
  async getPveCommonConfig(battleType: string, configId?: number): Promise<any> {
    try {
      // 根据战斗类型获取对应的配置
      switch (battleType) {
        case 'League':
          // 使用系统参数表作为通用配置
          return await this.gameConfig.systemParam.get(configId || 1);
        case 'Cup':
          return await this.gameConfig.systemParam.get(configId || 1);
        case 'Tournament':
          return await this.gameConfig.systemParam.get(configId || 1);
        case 'MiddleEast':
          return await this.gameConfig.middleEastCup.get(configId || 1);
        case 'GulfCup':
          return await this.gameConfig.gulfCupTeam.get(configId || 1);
        case 'MLS':
          return await this.gameConfig.mLS.get(configId || 1);
        default:
          this.logger.warn(`未知的PVE战斗类型: ${battleType}`);
          return null;
      }
    } catch (error) {
      this.logger.error('获取PVE配置失败', error);
      return null;
    }
  }

  /**
   * 🔧 根据战斗数据初始化玩家
   * 基于old项目: Room.prototype.initPlayerByBattleData
   */
  async initHeroByBattleData(battleData: any, teamType: string): Promise<void> {
    try {
      const team = this.getTeamInfoByTeamType(battleData, teamType);

      if (!team) {
        throw new Error(`队伍数据不存在: ${teamType}`);
      }

      // 初始化角色基础信息（新架构）
      team.characterId = team.characterId || 'unknown_character';
      team.level = team.level || 1;

      // 初始化队伍属性（委托给BattleInitializer）
      await this.battleInitializer.initHeroTeamData(team);

      this.logger.debug(`角色队伍初始化完成`);

    } catch (error) {
      this.logger.error('根据战斗数据初始化玩家失败', error);
      throw error;
    }
  }

  /**
   * 🔧 获取英雄对象
   * 基于old项目: Room.prototype.getHero
   */
  getHero(battleData: BattleData, teamType: string, heroUid: string): HeroSearchResult {
    try {
      const team = this.getTeamInfoByTeamType(battleData, teamType);

      if (!team || !team.heroes) {
        this.logger.warn(`队伍或英雄数据不存在: ${teamType}`);
        return null;
      }

      // 查找指定的英雄
      const hero = team.heroes.find((h: any) => h.uid === heroUid || h.id === heroUid);

      if (!hero) {
        this.logger.warn(`英雄不存在: ${heroUid}`);
        return { hero: null, found: false };
      }

      return { hero, found: true };

    } catch (error) {
      this.logger.error('获取英雄对象失败', error);
      return { hero: null, found: false };
    }
  }

  /**
   * 🔧 获取英雄列表
   * 基于old项目: Room.prototype.getHeroList
   *
   * @param battleData 战斗数据 - 包含队伍和球员信息
   * @param teamType 队伍类型 - 'teamA'或'teamB'
   * @returns BattleHero[] 英雄列表
   */
  getHeroList(battleData: BattleData, teamType: string): BattleHero[] {
    try {
      const team = this.getTeamInfoByTeamType(battleData, teamType);

      if (!team || !team.heroes) {
        this.logger.warn(`队伍或英雄数据不存在: ${teamType}`);
        return [];
      }

      return team.heroes || [];

    } catch (error) {
      this.logger.error('获取英雄列表失败', error);
      return [];
    }
  }

  /**
   * 🔧 测试配置初始化
   * 基于old项目: Room.prototype.initByTestConfig
   * 用于测试环境的特殊初始化
   */
  async initByTestConfig(testConfig: TestConfig): Promise<BattleData> {
    try {
      // 创建测试战斗数据
      const battleData = {
        battleType: BattleType.TEST,
        battleTime: 0,
        roundIndex: 0,
        maxBattleTime: 5400,
        teamA: {
          ...testConfig.teamA,
          score: 0,
          attr: { morale: 500, moraleSlot: 0, moraleAcceleration: 1 }
        },
        teamB: {
          ...testConfig.teamB,
          score: 0,
          attr: { morale: 500, moraleSlot: 0, moraleAcceleration: 1 }
        },
        battleRecord: { battleRoundInfo: [] }
      };

      // 初始化测试队伍数据
      await this.initTestTeamInfo(battleData.teamA);
      await this.initTestTeamInfo(battleData.teamB);

      this.logger.debug('测试配置初始化完成');
      return battleData as BattleData;

    } catch (error) {
      this.logger.error('测试配置初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 初始化测试队伍信息
   * 辅助方法：为测试环境初始化队伍
   */
  private async initTestTeamInfo(team: any): Promise<void> {
    try {
      // 设置默认测试数据
      team.playerId = team.playerId || 'test_player';
      team.playerName = team.playerName || '测试玩家';
      team.level = team.level || 1;
      team.totalAttack = team.totalAttack || 1000;
      team.totalDefend = team.totalDefend || 1000;
      team.tactic = team.tactic || 101;

      // 初始化英雄数据
      if (!team.heroes || team.heroes.length === 0) {
        team.heroes = await this.createTestHeroes();
      }

      this.logger.debug('测试队伍信息初始化完成');
    } catch (error) {
      this.logger.error('初始化测试队伍信息失败', error);
      throw error;
    }
  }

  /**
   * 🔧 创建测试英雄
   * 辅助方法：为测试环境创建默认英雄
   */
  private async createTestHeroes(): Promise<any[]> {
    try {
      // 创建11个测试英雄（标准足球阵容）
      const testHeroes = [];
      const positions = ['GK', 'DC', 'DC', 'DL', 'DR', 'MC', 'MC', 'ML', 'MR', 'ST', 'ST'];

      for (let i = 0; i < 11; i++) {
        testHeroes.push({
          uid: `test_hero_${i + 1}`,
          id: i + 1,
          name: `测试球员${i + 1}`,
          position: positions[i],
          attack: 80 + Math.random() * 20,
          defend: 80 + Math.random() * 20,
          level: 1
        });
      }

      return testHeroes;
    } catch (error) {
      this.logger.error('创建测试英雄失败', error);
      return [];
    }
  }





  /**
   * 🔧 根据PVE配置初始化敌人
   * 基于old项目: Room.prototype.initEnemyByPveConfig
   * 根据PVE配置创建和初始化AI敌人
   */
  async initEnemyByPveConfig(battleData: any, pveConfig: any): Promise<void> {
    try {
      if (!pveConfig) {
        throw new Error('PVE配置不存在');
      }

      // 创建敌人队伍基础数据
      const enemyTeam = {
        teamSide: 'teamB',
        playerId: 'pve_enemy',
        playerName: pveConfig.enemyName || '挑战者',
        level: pveConfig.level || 1,
        teamResId: pveConfig.teamId || 1,

        // 从配置获取属性
        totalAttack: pveConfig.attack || 1000,
        totalDefend: pveConfig.defend || 1000,
        tactic: pveConfig.tactic || 101,

        // 初始化基础属性
        score: 0,
        attr: {
          morale: pveConfig.morale || 500,
          moraleSlot: 0,
          moraleAcceleration: pveConfig.moraleSpeed || 1,
          attackTacticID: pveConfig.attackTactic || 101,
          defendTacticID: pveConfig.defendTactic || 202
        },

        // 创建英雄列表
        heroes: await this.createEnemyHeroes(pveConfig),

        // 初始化统计数据
        statistic: {
          shots: 0,
          shotsOnTarget: 0,
          possession: 50,
          passes: 0,
          fouls: 0,
          ballerScoreMap: new Map()
        }
      };

      // 设置到战斗数据
      battleData.teamB = enemyTeam;
      battleData.pveConfig = pveConfig;

      this.logger.debug(`PVE敌人初始化完成: ${enemyTeam.playerName}, 等级=${enemyTeam.level}`);

    } catch (error) {
      this.logger.error('根据PVE配置初始化敌人失败', error);
      throw error;
    }
  }

  /**
   * 🔧 创建敌人英雄
   * 辅助方法：根据PVE配置创建敌人的英雄阵容
   */
  private async createEnemyHeroes(pveConfig: any): Promise<any[]> {
    try {
      const heroes = [];

      // 如果配置中指定了英雄列表
      if (pveConfig.heroIds && Array.isArray(pveConfig.heroIds)) {
        for (let i = 0; i < pveConfig.heroIds.length; i++) {
          const heroId = pveConfig.heroIds[i];
          const heroConfig = await this.gameConfig.hero.get(heroId);

          if (heroConfig) {
            heroes.push({
              uid: `enemy_hero_${i}`,
              id: heroId,
              name: heroConfig.cnName || heroConfig.name || `敌方球员${i + 1}`,
              position: pveConfig.positions?.[i] || 'ST',
              attack: heroConfig.attack || 80,
              defend: (heroConfig.standingTackle || 0) + (heroConfig.slidingTackle || 0) / 2 || 80,
              level: pveConfig.heroLevel || 1,
              isEnemy: true
            });
          }
        }
      }

      // 如果没有足够的英雄，补充默认英雄
      while (heroes.length < 11) {
        const index = heroes.length;
        const positions = ['GK', 'DC', 'DC', 'DL', 'DR', 'MC', 'MC', 'ML', 'MR', 'ST', 'ST'];

        heroes.push({
          uid: `enemy_hero_${index}`,
          id: 10000 + index,
          name: `敌方球员${index + 1}`,
          position: positions[index] || 'ST',
          attack: 70 + Math.random() * 30,
          defend: 70 + Math.random() * 30,
          level: pveConfig.heroLevel || 1,
          isEnemy: true
        });
      }

      this.logger.debug(`敌人英雄创建完成: ${heroes.length}个英雄`);
      return heroes;

    } catch (error) {
      this.logger.error('创建敌人英雄失败', error);
      return [];
    }
  }

  /**
   * 🔧 根据ID获取球员信息
   */
  private getHeroById(team: BattleTeam, heroId: string): any {
    return team.heroes?.find(hero => hero.heroId === heroId);
  }
}
