import { Injectable, Logger } from "@nestjs/common";
import { GameConfigFacade } from "@libs/game-config";
import { BattleEngine } from "./battle-engine";
import { BattleResult, BattleTeam } from "./types/battle-data.types";

/**
 * 🔧 战斗管理器 - 特殊战斗类型支持
 * 基于old项目的特殊战斗模式，提供信仰之战、游客模式等特殊战斗类型的完整业务流程
 *
 * 🎯 核心功能：
 * - 信仰之战：包含信仰等级加成的PVP战斗
 * - 游客模式：为未注册用户提供的体验战斗
 * - PVE/PVP特殊初始化：需要额外配置的战斗类型
 *
 * 🔧 架构设计：
 * - 委托模式：所有计算委托给BattleEngine处理
 * - 职责分离：专注于特殊战斗类型的业务逻辑
 * - 扩展性：新的特殊战斗类型可以轻松添加
 *
 * 📋 业务流程：
 * 1. 接收BattleTeam数组和特殊配置参数
 * 2. 调用BattleEngine进行标准初始化
 * 3. 应用特殊战斗类型的初始化逻辑
 * 4. 执行战斗计算并返回结果
 */
@Injectable()
export class BattleManager {
  private readonly logger = new Logger(BattleManager.name);
  private readonly battleEngine: BattleEngine;

  constructor(private readonly gameConfig: GameConfigFacade) {
    this.battleEngine = new BattleEngine(gameConfig);
  }

  /**
   * 🔧 信仰之战 - 完整业务流程
   * 基于old项目的信仰之战逻辑，包含特殊的信仰等级加成和PVP战斗机制
   *
   * 📋 业务流程：
   * 1. 标准战斗数据初始化（BattleTeam → BattleData）
   * 2. 应用信仰之战特殊初始化：
   *    - 设置信仰等级和特殊标识
   *    - 计算信仰加成属性
   *    - 初始化信仰技能效果
   * 3. 执行PVP战斗计算
   * 4. 生成包含信仰奖励的战斗结果
   *
   * @param battleTeams 战斗队伍数组 [teamA, teamB] - 双方玩家队伍
   * @param faithLevel 信仰等级 (1-10) - 影响属性加成和奖励
   * @param battleId 战斗ID（可选） - 用于战斗记录追踪
   * @returns Promise<BattleResult> 包含信仰奖励的完整战斗结果
   */
  async warOfFaithBattle(
    battleTeams: BattleTeam[],
    faithLevel: number = 1,
    battleId?: string
  ): Promise<BattleResult> {
    this.logger.log(`🔥 信仰之战开始 - 信仰等级: ${faithLevel}, 队伍数: ${battleTeams.length}`);

    try {
      // 第一步：标准战斗数据初始化
      const battleData = await this.battleEngine.initializeBattleData(
        battleTeams,
        "PVP_WAR_OF_FAITH",
        battleId
      );

      // 第二步：应用信仰之战特殊初始化
      await this.battleEngine.initWarOfFaithBattle(battleData, faithLevel);

      // 第三步：执行战斗计算
      const battleResult = await this.battleEngine.executeBattleRounds(battleData);

      // 第四步：生成最终结果（包含信仰奖励）
      return await this.battleEngine.generateBattleResult(battleResult);
    } catch (error) {
      this.logger.error(`❌ 信仰之战失败 - 信仰等级: ${faithLevel}`, error);
      throw error;
    }
  }

  /**
   * 🔧 游客模式战斗 - 完整业务流程
   * 基于old项目的游客模式逻辑，为未注册用户提供完整的战斗体验
   *
   * 📋 业务流程：
   * 1. 标准战斗数据初始化（BattleTeam → BattleData）
   * 2. 应用游客模式特殊初始化：
   *    - 设置游客模式标识和限制
   *    - 创建临时游客队伍数据
   *    - 简化战斗奖励和记录
   * 3. 执行简化的战斗计算
   * 4. 生成适合游客的战斗结果
   *
   * @param battleTeams 战斗队伍数组 [teamA, teamB] - 游客队伍vs系统队伍
   * @param battleId 战斗ID（可选） - 用于临时战斗记录
   * @returns Promise<BattleResult> 简化的战斗结果（无持久化奖励）
   */
  async guestModeBattle(
    battleTeams: BattleTeam[],
    battleId?: string
  ): Promise<BattleResult> {
    this.logger.log(`👤 游客模式战斗开始 - 队伍数: ${battleTeams.length}`);

    try {
      // 第一步：标准战斗数据初始化
      const battleData = await this.battleEngine.initializeBattleData(
        battleTeams,
        "GUEST",
        battleId
      );

      // 第二步：应用游客模式特殊初始化
      await this.battleEngine.initGuestModeBattle(battleData);

      // 第三步：执行战斗计算
      const battleResult = await this.battleEngine.executeBattleRounds(battleData);

      // 第四步：生成游客适用的结果
      return await this.battleEngine.generateBattleResult(battleResult);
    } catch (error) {
      this.logger.error(`❌ 游客模式战斗失败`, error);
      throw error;
    }
  }

  /**
   * 🔧 PVE战斗（带特殊初始化）- 完整业务流程
   * 基于old项目的PVE战斗逻辑，支持联赛、奖杯赛等需要特殊初始化的PVE战斗
   *
   * 📋 业务流程：
   * 1. 标准战斗数据初始化（BattleTeam → BattleData）
   * 2. 应用PVE特殊初始化：
   *    - 初始化AI敌方队伍数据
   *    - 设置PVE难度和奖励配置
   *    - 加载关卡特殊规则
   * 3. 执行PVE战斗计算
   * 4. 生成包含PVE奖励的战斗结果
   *
   * @param battleTeams 战斗队伍数组 [playerTeam, aiTeam] - 玩家队伍vs AI队伍
   * @param battleType 战斗类型 - PVE_LEAGUE, PVE_TROPHY等
   * @param battleId 战斗ID（可选） - 用于战斗记录追踪
   * @returns Promise<BattleResult> 包含PVE奖励的完整战斗结果
   */
  async pveBattleWithInit(
    battleTeams: BattleTeam[],
    battleType: string,
    battleId?: string
  ): Promise<BattleResult> {
    this.logger.log(`⚔️ PVE战斗开始 - 类型: ${battleType}, 队伍数: ${battleTeams.length}`);

    try {
      // 第一步：标准战斗数据初始化
      const battleData = await this.battleEngine.initializeBattleData(
        battleTeams,
        battleType,
        battleId
      );

      // 第二步：应用PVE特殊初始化
      await this.battleEngine.initPveBattle(battleData);

      // 第三步：执行战斗计算
      const battleResult = await this.battleEngine.executeBattleRounds(battleData);

      // 第四步：生成最终结果（包含PVE奖励）
      return await this.battleEngine.generateBattleResult(battleResult);
    } catch (error) {
      this.logger.error(`❌ PVE战斗失败 - 类型: ${battleType}`, error);
      throw error;
    }
  }

  /**
   * 🔧 PVP战斗（带特殊初始化）- 完整业务流程
   * 基于old项目的PVP战斗逻辑，支持球场争夺战等需要特殊配置的PVP战斗
   *
   * 📋 业务流程：
   * 1. 标准战斗数据初始化（BattleTeam → BattleData）
   * 2. 应用PVP特殊初始化：
   *    - 处理特殊PVP配置（球场争夺战等）
   *    - 设置双方队伍的特殊属性
   *    - 初始化PVP奖励和排名系统
   * 3. 执行PVP战斗计算
   * 4. 生成包含PVP奖励的战斗结果
   *
   * @param battleTeams 战斗队伍数组 [teamA, teamB] - 双方玩家队伍
   * @param battleType 战斗类型 - PVP_BUSINESS, PVP_GROUND_MATCH等
   * @param pvpConfig PVP特殊配置 - 球场争夺战配置、排名信息等
   * @param battleId 战斗ID（可选） - 用于战斗记录追踪
   * @returns Promise<BattleResult> 包含PVP奖励的完整战斗结果
   */
  async pvpBattleWithInit(
    battleTeams: BattleTeam[],
    battleType: string,
    pvpConfig?: any,
    battleId?: string
  ): Promise<BattleResult> {
    this.logger.log(`⚔️ PVP战斗开始 - 类型: ${battleType}, 队伍数: ${battleTeams.length}`);

    try {
      // 第一步：标准战斗数据初始化
      const battleData = await this.battleEngine.initializeBattleData(
        battleTeams,
        battleType,
        battleId
      );

      // 第二步：应用PVP特殊初始化（包含特殊配置）
      await this.battleEngine.initPvpBattle(battleData, pvpConfig);

      // 第三步：执行战斗计算
      const battleResult = await this.battleEngine.executeBattleRounds(battleData);

      // 第四步：生成最终结果（包含PVP奖励）
      return await this.battleEngine.generateBattleResult(battleResult);
    } catch (error) {
      this.logger.error(`❌ PVP战斗失败 - 类型: ${battleType}`, error);
      throw error;
    }
  }
}
