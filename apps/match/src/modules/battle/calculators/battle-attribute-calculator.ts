import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleTeam } from '../types/battle-data.types';

/**
 * 战斗属性计算器
 * 基于old项目room.js的属性计算逻辑
 * 
 * 职责：
 * - 士气计算 (calcMoraleAttr)
 * - 阵型加成计算 (calculateFormationBonus)
 * - 战术加成计算 (calculateTacticBonus)
 * - 战力综合计算
 */
export class BattleAttributeCalculator {
  private readonly logger = new Logger(BattleAttributeCalculator.name);

  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 🔧 计算士气属性
   * 基于old项目: Room.prototype.calcMoraleAttr
   *
   * @param teamA A队数据 - 包含攻击和防守属性
   * @param teamB B队数据 - 包含攻击和防守属性
   */
  calcMoraleAttr(teamA: BattleTeam, teamB: BattleTeam): void {
    const calcMoraleFormula = (attackA: number, defendA: number, attackB: number, defendB: number): number => {
      let morale = 0;
      if (attackA >= defendB) {
        let a = attackA + defendA - attackB - defendB;
        if (a < 0) a = 0;
        else if (a > 120000) a = 120000;
        morale = Math.round(362 * (a / 120000) + 378);
      } else {
        morale = Math.round(300 * (1 / ((defendB - attackA) * 10 / 1000 + 1)));
      }
      return Math.max(300, Math.min(740, morale));
    };

    // 计算双方士气 - 使用正确的属性访问路径
    teamA.attr.morale = calcMoraleFormula(teamA.totalAttack, teamA.totalDefend, teamB.totalAttack, teamB.totalDefend);
    teamB.attr.morale = calcMoraleFormula(teamB.totalAttack, teamB.totalDefend, teamA.totalAttack, teamA.totalDefend);

    this.logger.debug(`士气计算完成: teamA=${teamA.attr.morale}, teamB=${teamB.attr.morale}`);
  }

  /**
   * 🔧 计算阵型加成
   * 基于old项目的阵型配置表逻辑
   *
   * @param team 队伍数据 - 包含阵型和球员信息
   * @param formationId 阵型ID - 用于查询配置表
   * @returns Promise<阵型加成> 包含攻击和防守加成
   */
  async calculateFormationBonus(team: BattleTeam, formationId: number): Promise<{ attackBonus: number; defendBonus: number }> {
    let attackBonus = 0;
    let defendBonus = 0;

    try {
      // 从配置表读取阵型加成数据
      // 基于old项目的TeamFormation.json配置
      // 每个位置有不同的AttackArg和DefendArg权重值

      const definition = await this.gameConfig.teamFormation.get(formationId);
      if (!definition) {
        this.logger.warn(`阵型配置不存在: ${formationId}`);
        return { attackBonus: 0, defendBonus: 0 };
      }

      // 计算阵型加成
      for (const hero of team.heroes || []) {
        const position = hero.position;
        const positionConfig = this.getPositionConfig(definition, position);

        if (positionConfig) {
          // 基于old项目的AttackArg和DefendArg权重计算
          const attackWeight = positionConfig.AttackArg || 100;
          const defendWeight = positionConfig.DefendArg || 100;

          // 球员属性贡献 - 使用BattleHero接口中定义的属性
          const heroAttack = hero.attack || 0;
          const heroDefend = hero.defend || 0;

          // 位置适配度影响
          const positionMatch = this.calculatePositionMatchRate(hero, position);

          attackBonus += (heroAttack * attackWeight / 1000) * positionMatch;
          defendBonus += (heroDefend * defendWeight / 1000) * positionMatch;
        }
      }

      // 遍历球员计算位置加成
      if (team.heroes && Array.isArray(team.heroes)) {
        for (const hero of team.heroes) {
          if (!hero.position) continue;

          // 根据位置获取权重
          const positionWeight = this.getPositionWeight(definition, hero.position);
          if (positionWeight) {
            attackBonus += hero.attack * positionWeight.attackArg / 1000;
            defendBonus += hero.defend * positionWeight.defendArg / 1000;
          }
        }
      }

      this.logger.debug(`阵型加成计算: 阵型=${formationId}, 攻击加成=${attackBonus}, 防守加成=${defendBonus}`);
    } catch (error) {
      this.logger.error('阵型加成计算失败', error);
    }

    return { attackBonus, defendBonus };
  }

  /**
   * 🔧 计算战术加成
   * 基于old项目的战术配置表逻辑
   *
   * @param team 队伍数据 - 包含战术和球员信息
   * @param tacticId 战术ID - 用于查询配置表
   * @returns Promise<战术加成> 包含攻击和防守加成
   */
  async calculateTacticBonus(team: BattleTeam, tacticId: number): Promise<{ attackBonus: number; defendBonus: number }> {
    let attackBonus = 0;
    let defendBonus = 0;

    try {
      // 从配置表读取战术加成数据
      // 基于old项目的Tactic.json配置
      // 战术有AddType和AddValue字段定义加成类型和数值

      const tacticConfig = await this.gameConfig.tactic.get(tacticId);
      if (!tacticConfig) {
        this.logger.warn(`战术配置不存在: ${tacticId}`);
        return { attackBonus: 0, defendBonus: 0 };
      }

      // 处理战术加成配置
      // 基于old项目Tactic.json的结构，直接从配置中读取AddType和AddValue
      const addType = (tacticConfig as any).AddType || 0;
      const addValue = (tacticConfig as any).AddValue || 0;

      if (addType > 0 && addValue > 0) {
        // 基于old项目的AddType映射
        switch (addType) {
          case 1: // 速度加成 - 影响攻击
            attackBonus += addValue * 0.4;
            break;
          case 2: // 射门加成 - 主要影响攻击
            attackBonus += addValue * 0.8;
            break;
          case 3: // 传球加成 - 影响攻击
            attackBonus += addValue * 0.5;
            break;
          case 4: // 防守加成 - 主要影响防守
            defendBonus += addValue * 0.8;
            break;
          case 5: // 身体加成 - 攻防都有影响
            attackBonus += addValue * 0.3;
            defendBonus += addValue * 0.4;
            break;
          case 6: // 盘带加成 - 影响攻击
            attackBonus += addValue * 0.6;
            break;
          case 7: // 门将加成 - 影响防守
            defendBonus += addValue * 0.9;
            break;
          case 8: // 团队攻击加成
            attackBonus += addValue;
            break;
          case 9: // 团队防守加成
            defendBonus += addValue;
            break;
          case 10: // 全属性加成
            attackBonus += addValue * 0.5;
            defendBonus += addValue * 0.5;
            break;
          default:
            this.logger.warn(`未知的战术加成类型: ${addType}`);
        }
      }

      // 计算战术加成 - 使用实际的配置字段
      const baseAttack = team.totalAttack || 0;
      const baseDefend = team.totalDefend || 0;

      // 处理多个加成类型
      if (tacticConfig.addType1 && tacticConfig.addValue1) {
        const bonus = this.calculateSingleTacticBonus(baseAttack, baseDefend, tacticConfig.addType1, tacticConfig.addValue1);
        attackBonus += bonus.attackBonus;
        defendBonus += bonus.defendBonus;
      }

      if (tacticConfig.addType2 && tacticConfig.addValue2) {
        const bonus = this.calculateSingleTacticBonus(baseAttack, baseDefend, tacticConfig.addType2, tacticConfig.addValue2);
        attackBonus += bonus.attackBonus;
        defendBonus += bonus.defendBonus;
      }

      this.logger.debug(`战术加成计算: 战术=${tacticId}, 攻击加成=${attackBonus}, 防守加成=${defendBonus}`);
    } catch (error) {
      this.logger.error('战术加成计算失败', error);
    }

    return { attackBonus, defendBonus };
  }

  /**
   * 🔧 计算单个战术加成
   */
  private calculateSingleTacticBonus(baseAttack: number, baseDefend: number, addType: number, addValue: number): { attackBonus: number; defendBonus: number } {
    let attackBonus = 0;
    let defendBonus = 0;

    switch (addType) {
      case 1: // 攻击加成
        attackBonus = baseAttack * addValue / 100;
        break;
      case 2: // 防守加成
        defendBonus = baseDefend * addValue / 100;
        break;
      case 3: // 全属性加成
        attackBonus = baseAttack * addValue / 100;
        defendBonus = baseDefend * addValue / 100;
        break;
    }

    return { attackBonus, defendBonus };
  }



  /**
   * 🔧 计算综合战力
   * 整合基础属性、阵型加成、战术加成
   *
   * @param team 队伍数据 - 包含所有计算所需的属性和配置
   */
  async calculateTotalPower(team: BattleTeam): Promise<void> {
    // 基础战力
    let baseAttack = 0;
    let baseDefend = 0;

    if (team.heroes && Array.isArray(team.heroes)) {
      for (const hero of team.heroes) {
        baseAttack += hero.attack || 0;
        baseDefend += hero.defend || 0;
      }
    }

    // 阵型加成
    const formationBonus = await this.calculateFormationBonus(team, team.formationId);

    // 战术加成
    const tacticBonus = await this.calculateTacticBonus(team, team.tactic);

    // 综合战力
    team.totalAttack = Math.round(baseAttack + formationBonus.attackBonus + tacticBonus.attackBonus);
    team.totalDefend = Math.round(baseDefend + formationBonus.defendBonus + tacticBonus.defendBonus);

    this.logger.debug(`综合战力计算: ${team.teamName} - 基础攻击=${baseAttack}, 基础防守=${baseDefend}, 最终攻击=${team.totalAttack}, 最终防守=${team.totalDefend}`);
  }

  /**
   * 🔧 获取位置权重
   * 基于old项目的位置配置
   */
  private getPositionWeight(formationConfig: any, position: string): { attackArg: number; defendArg: number } | null {
    // 位置映射到配置字段
    const positionMapping: { [key: string]: string } = {
      'GK': 'Position1',   // 门将
      'DC': 'Position2',   // 中后卫
      'DL': 'Position3',   // 左后卫
      'DR': 'Position4',   // 右后卫
      'MC': 'Position5',   // 中场
      'ML': 'Position6',   // 左中场
      'MR': 'Position7',   // 右中场
      'ST': 'Position8',   // 前锋
      'WL': 'Position9',   // 左边锋
      'WR': 'Position10'   // 右边锋
    };

    const positionKey = positionMapping[position];
    if (!positionKey || !formationConfig[positionKey]) {
      return null;
    }

    const positionData = formationConfig[positionKey];
    return {
      attackArg: positionData.AttackArg || 0,
      defendArg: positionData.DefendArg || 0
    };
  }

  /**
   * 🔧 计算位置适配度
   * 基于old项目的位置匹配逻辑
   */
  calculatePositionMatchRate(hero: any, position: string): number {
    // 基于球员的主位置和当前位置计算适配度
    // 影响球员属性的发挥程度

    if (!hero.mainPosition) {
      return 1.0; // 默认100%适配
    }

    // 主位置100%适配
    if (hero.mainPosition === position) {
      return 1.0;
    }

    // 相近位置适配度较高
    const positionCompatibility: { [key: string]: { [key: string]: number } } = {
      'GK': { 'GK': 1.0 }, // 门将只能门将
      'DC': { 'DC': 1.0, 'DL': 0.8, 'DR': 0.8, 'MC': 0.6 },
      'DL': { 'DL': 1.0, 'DC': 0.8, 'ML': 0.9, 'WL': 0.7 },
      'DR': { 'DR': 1.0, 'DC': 0.8, 'MR': 0.9, 'WR': 0.7 },
      'MC': { 'MC': 1.0, 'ML': 0.8, 'MR': 0.8, 'DC': 0.6, 'ST': 0.6 },
      'ML': { 'ML': 1.0, 'MC': 0.8, 'DL': 0.9, 'WL': 0.9 },
      'MR': { 'MR': 1.0, 'MC': 0.8, 'DR': 0.9, 'WR': 0.9 },
      'ST': { 'ST': 1.0, 'MC': 0.6, 'WL': 0.7, 'WR': 0.7 },
      'WL': { 'WL': 1.0, 'ML': 0.9, 'DL': 0.7, 'ST': 0.7 },
      'WR': { 'WR': 1.0, 'MR': 0.9, 'DR': 0.7, 'ST': 0.7 }
    };

    const compatibility = positionCompatibility[hero.mainPosition];
    return compatibility?.[position] || 0.5; // 不兼容位置50%适配
  }

  /**
   * 🔧 获取位置配置
   * 从阵型配置中获取指定位置的配置信息
   */
  private getPositionConfig(formationConfig: any, position: string): any {
    try {
      // 根据位置映射到配置中的Position字段
      const positionMapping: { [key: string]: string } = {
        'GK': 'Position1',   // 门将
        'DC': 'Position2',   // 中后卫
        'DL': 'Position3',   // 左后卫
        'DR': 'Position4',   // 右后卫
        'MC': 'Position5',   // 中场
        'ML': 'Position6',   // 左中场
        'MR': 'Position7',   // 右中场
        'WL': 'Position8',   // 左边锋
        'WR': 'Position9',   // 右边锋
        'ST': 'Position10'   // 前锋
      };

      const positionKey = positionMapping[position];
      if (!positionKey) {
        this.logger.warn(`未知位置: ${position}`);
        return null;
      }

      const positionConfig = formationConfig[positionKey];
      if (!positionConfig) {
        this.logger.warn(`阵型配置中缺少位置配置: ${positionKey}`);
        return null;
      }

      return positionConfig;
    } catch (error) {
      this.logger.error(`获取位置配置失败: ${position}`, error);
      return null;
    }
  }

  /**
   * 🔧 计算战术克制关系
   * 基于old项目: Room.prototype.getTacticsRestrainTeam
   * 完整实现战术克制系统，影响士气和战力计算
   *
   * @param teamA A队数据 - 包含战术配置
   * @param teamB B队数据 - 包含战术配置
   * @returns Promise<战术克制结果> 包含克制关系和加成效果
   */
  async getTacticsRestrainTeam(teamA: BattleTeam, teamB: BattleTeam): Promise<any> {
    try {
      // 初始化克制结果对象
      const restrainTeam = {
        atkTacticsAState: 0, // NO_RESTRAIN
        atkTacticsBState: 0, // NO_RESTRAIN
        attackAFactor: 1,
        defendAFactor: 1,
        attackBFactor: 1,
        defendBFactor: 1,
        attackTrainerAFactor: 0,
        defendTrainerAFactor: 0,
        attackTrainerBFactor: 0,
        defendTrainerBFactor: 0
      };

      // 获取队伍阵型信息
      const formationA = await this.getMainTeamFormation(teamA);
      const formationB = await this.getMainTeamFormation(teamB);

      if (!formationA || !formationB) {
        this.logger.warn('队伍阵型信息缺失，使用默认克制关系');
        return restrainTeam;
      }

      // 获取战术配置
      const tacticsList = {
        attackTacticsA: formationA.UseTactics || teamA.tactic || 0,
        defendTacticsA: formationA.UseDefTactics || teamA.tactic || 0,
        attackTacticsB: formationB.UseTactics || teamB.tactic || 0,
        defendTacticsB: formationB.UseDefTactics || teamB.tactic || 0
      };

      this.logger.debug('战术配置:', tacticsList);

      // 检查战术有效性
      if (tacticsList.attackTacticsA === 0 || tacticsList.defendTacticsA === 0 ||
          tacticsList.attackTacticsB === 0 || tacticsList.defendTacticsB === 0) {
        this.logger.debug('存在无效战术，返回默认克制关系');
        return restrainTeam;
      }

      // 获取战术配置表和克制关系表
      const tacticConfig = await this.getTacticConfig();
      const restrainConfig = await this.getTacticRestrainConfig();

      if (!tacticConfig || !restrainConfig) {
        this.logger.warn('战术配置表缺失，使用默认克制关系');
        return restrainTeam;
      }

      // 🔧 补充：初始化教练数据
      const trainerList = {
        teamA: await this.getTeamTrainers(teamA),
        teamB: await this.getTeamTrainers(teamB)
      };

      // 计算战术克制关系（包含教练加成）
      const restrainResult = this.calculateTacticRestrainWithTrainers(
        tacticsList,
        tacticConfig,
        restrainConfig,
        trainerList,
        teamA,
        teamB
      );

      // 合并结果
      Object.assign(restrainTeam, restrainResult);

      this.logger.debug('战术克制计算完成:', restrainTeam);
      return restrainTeam;

    } catch (error) {
      this.logger.error('计算战术克制关系失败', error);
      return {
        atkTacticsAState: 0,
        atkTacticsBState: 0,
        attackAFactor: 1,
        defendAFactor: 1,
        attackBFactor: 1,
        defendBFactor: 1,
        attackTrainerAFactor: 0,
        defendTrainerAFactor: 0,
        attackTrainerBFactor: 0,
        defendTrainerBFactor: 0
      };
    }
  }

  /**
   * 🔧 获取主队阵型信息
   * 基于old项目的阵型获取逻辑
   *
   * @param team 队伍数据 - 包含阵型ID和配置信息
   * @returns Promise<阵型配置> 阵型的详细配置数据
   */
  private async getMainTeamFormation(team: BattleTeam): Promise<any> {
    try {
      // 从队伍数据中获取阵型配置
      if (team.formationId && team.formationId > 0) {
        const formationConfig = await this.gameConfig.teamFormation.get(team.formationId);
        if (formationConfig) {
          return {
            ...formationConfig,
            UseTactics: team.tactic || 0,
            UseDefTactics: team.tactic || 0,
            Attack: team.totalAttack || 0,
            Defend: team.totalDefend || 0
          };
        }
      }

      // 返回默认阵型
      return {
        UseTactics: team.tactic || 0,
        UseDefTactics: team.tactic || 0,
        Attack: team.totalAttack || 0,
        Defend: team.totalDefend || 0
      };
    } catch (error) {
      this.logger.error('获取主队阵型失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取战术配置表
   * 基于old项目的战术配置
   */
  private async getTacticConfig(): Promise<any> {
    try {
      // 从游戏配置中获取战术配置
      const tacticConfigs = await this.gameConfig.tactic.getAll();
      return tacticConfigs || {};
    } catch (error) {
      this.logger.error('获取战术配置失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取战术克制关系配置表
   * 基于old项目的战术克制配置
   */
  private async getTacticRestrainConfig(): Promise<any> {
    try {
      // 从游戏配置中获取战术克制配置
      const restrainConfigs = await this.gameConfig.tacticRestrain.getAll();
      return restrainConfigs || {};
    } catch (error) {
      this.logger.error('获取战术克制配置失败', error);
      return null;
    }
  }

  /**
   * 🚀 计算具体的战术克制关系
   * 基于old项目room.js第334-527行的完整逻辑
   *
   * old项目真实逻辑：
   * 1. 遍历双方的进攻和防守战术
   * 2. 通过TacticRestrain配置表查找克制关系
   * 3. 使用effectNameAndValue计算克制效果
   * 4. 支持多重克制关系（RestrainType1/2/3）
   */
  private calculateTacticRestrain(
    tacticsList: any,
    tacticConfig: any,
    restrainConfig: any,
    teamA: any,
    teamB: any
  ): any {
    try {
      const restrainTeam = {
        atkTacticsAState: 0, // NO_RESTRAIN
        atkTacticsBState: 0, // NO_RESTRAIN
        attackAFactor: 1,
        defendAFactor: 1,
        attackBFactor: 1,
        defendBFactor: 1,
        attackTrainerAFactor: 0,
        defendTrainerAFactor: 0,
        attackTrainerBFactor: 0,
        defendTrainerBFactor: 0
      };

      // 🚀 基于old项目第369-372行的辅助函数
      const getOppIndex = (index: number) => index === 0 ? 1 : 0;

      // 🚀 基于old项目第373-407行的effectNameAndValue函数
      const effectNameAndValue = (factorType: number, factorValue: number, obj: string) => {
        let name = "";
        let value = 1;
        let trainerName = "";

        // 保留2位小数，基于old项目第387-405行
        if (factorType === 1) {
          name = "attack" + obj + "Factor";
          value = 1 + factorValue / 10000;
          trainerName = "attackTrainer" + obj + "Factor";
        } else if (factorType === 2) {
          name = "defend" + obj + "Factor";
          value = 1 - factorValue / 10000;
          trainerName = "defendTrainer" + obj + "Factor";
        } else if (factorType === 3) {
          name = "defend" + obj + "Factor";
          value = 1 + factorValue / 10000;
          trainerName = "defendTrainer" + obj + "Factor";
        } else if (factorType === 4) {
          name = "attack" + obj + "Factor";
          value = 1 - factorValue / 10000;
          trainerName = "attackTrainer" + obj + "Factor";
        } else {
          this.logger.error("effectNameAndValue factorType错误:", factorType);
        }

        return { name, value, trainerName };
      };

      // 🚀 基于old项目第408-524行的主要克制计算逻辑
      const teamList = ["A", "B"];
      const typeList = ["attack", "defend"];

      for (let i = 0; i < teamList.length; i++) {
        for (let j = 0; j < typeList.length; j++) {
          const teamSide1 = teamList[i];
          const mode1 = typeList[j];
          const tactics1 = tacticsList[mode1 + "Tactics" + teamSide1];

          if (!tacticConfig[tactics1]) {
            continue;
          }

          const type1 = tacticConfig[tactics1].Type;

          for (let k = 0; k < teamList.length; k++) {
            for (let l = 0; l < typeList.length; l++) {
              const teamSide2 = teamList[k];
              const mode2 = typeList[l];
              const tactics2 = tacticsList[mode2 + "Tactics" + teamSide2];

              if (!tacticConfig[tactics2]) {
                continue;
              }

              const type2 = tacticConfig[tactics2].Type;

              // 🚀 检查克制关系（基于old项目第442-523行）
              for (let m = 1; m <= 3; m++) {
                if (restrainConfig[type1] && restrainConfig[type1]["RestrainType" + m] === type2) {
                  // 设置克制状态
                  let replaceTeamSide = teamSide1;
                  let replaceState = 1; // RESTRAIN
                  if (mode1 === "defend") {
                    replaceTeamSide = teamSide2;
                    replaceState = 2; // BE_RESTRAINED
                  }
                  restrainTeam["atkTactics" + replaceTeamSide + "State"] = replaceState;

                  // 计算基础克制效果
                  const result1 = effectNameAndValue(
                    tacticConfig[tactics1]["Restrain1"],
                    tacticConfig[tactics1]["Restrain1Value"],
                    teamSide1
                  );

                  const result2 = effectNameAndValue(
                    tacticConfig[tactics1]["Restrain2"],
                    tacticConfig[tactics1]["Restrain2Value"],
                    teamSide2
                  );

                  // 应用克制效果
                  if (result1.name) {
                    restrainTeam[result1.name] += (result1.value - 1);
                    restrainTeam[result1.trainerName] += (result1.value - 1);
                  }

                  if (result2.name) {
                    restrainTeam[result2.name] += (result2.value - 1);
                    restrainTeam[result2.trainerName] += (result2.value - 1);
                  }

                  this.logger.debug(`战术克制触发: ${tactics1}(${type1}) 克制 ${tactics2}(${type2}), 效果:`, result1, result2);
                }
              }
            }
          }
        }
      }

      this.logger.debug("战术克制计算完成:", restrainTeam);
      return restrainTeam;

    } catch (error) {
      this.logger.error('计算战术克制关系失败', error);
      return {
        atkTacticsAState: 0,
        atkTacticsBState: 0,
        attackAFactor: 1,
        defendAFactor: 1,
        attackBFactor: 1,
        defendBFactor: 1,
        attackTrainerAFactor: 0,
        defendTrainerAFactor: 0,
        attackTrainerBFactor: 0,
        defendTrainerBFactor: 0
      };
    }
  }



  /**
   * 🔧 获取队伍教练信息
   * 基于old项目: Room.prototype.getTacticsRestrainTeam中的教练初始化逻辑
   *
   * @param team 队伍数据 - 包含教练配置信息
   * @returns Promise<教练列表> 队伍的教练配置数据
   */
  private async getTeamTrainers(team: BattleTeam): Promise<any[]> {
    try {
      const trainers: any[] = [];
      const formation = await this.getMainTeamFormation(team);

      if (!formation || !formation.Trainers) {
        return trainers;
      }

      // 遍历阵型中的教练配置
      for (const trainerSlot of formation.Trainers) {
        if (trainerSlot.status === 3) { // 激活状态
          // 从配置表获取教练信息
          const coachUpstarConfigs = await this.gameConfig.coachUpstar.getAll();

          for (const config of coachUpstarConfigs) {
            if (config.coachId === trainerSlot.resId && config.star === trainerSlot.star) {
              trainers.push({
                resId: trainerSlot.resId,
                star: trainerSlot.star,
                type: trainerSlot.type, // 1主教练 2进攻教练 3防守教练 4-5-6助理
                trainerConfig: config
              });
              break;
            }
          }
        }
      }

      this.logger.debug(`队伍教练信息获取完成: ${trainers.length}个教练`);
      return trainers;
    } catch (error) {
      this.logger.error('获取队伍教练信息失败', error);
      return [];
    }
  }

  /**
   * 🚀 计算战术克制关系（包含教练加成）
   * 基于old项目: Room.prototype.getTacticsRestrainTeam的完整逻辑
   *
   * 职责分工：
   * 1. calculateTacticRestrain：处理基础战术克制逻辑
   * 2. calculateTacticRestrainWithTrainers：在基础克制上添加教练加成
   */
  private calculateTacticRestrainWithTrainers(
    tacticsList: any,
    tacticConfig: any,
    restrainConfig: any,
    trainerList: any,
    teamA: any,
    teamB: any
  ): any {
    try {
      // 🚀 1. 先计算基础战术克制关系
      const baseRestrainResult = this.calculateTacticRestrain(
        tacticsList,
        tacticConfig,
        restrainConfig,
        teamA,
        teamB
      );

      // 🚀 2. 在基础克制结果上添加教练加成
      // 基于old项目中教练对战术克制的影响逻辑
      const restrainTeam = { ...baseRestrainResult };

      // 🔧 教练加成计算（基于old项目的教练系统）
      // 这里需要根据trainerList中的教练配置计算额外的战术加成
      if (trainerList.teamA && trainerList.teamA.length > 0) {
        // A队教练加成
        for (const trainer of trainerList.teamA) {
          if (trainer && trainer.tactics) {
            // 根据教练的战术专长计算加成
            const trainerBonus = this.calculateTrainerTacticBonus(trainer, tacticsList.attackTacticsA, tacticsList.defendTacticsA);
            restrainTeam.attackTrainerAFactor += trainerBonus.attack;
            restrainTeam.defendTrainerAFactor += trainerBonus.defend;
          }
        }
      }

      if (trainerList.teamB && trainerList.teamB.length > 0) {
        // B队教练加成
        for (const trainer of trainerList.teamB) {
          if (trainer && trainer.tactics) {
            // 根据教练的战术专长计算加成
            const trainerBonus = this.calculateTrainerTacticBonus(trainer, tacticsList.attackTacticsB, tacticsList.defendTacticsB);
            restrainTeam.attackTrainerBFactor += trainerBonus.attack;
            restrainTeam.defendTrainerBFactor += trainerBonus.defend;
          }
        }
      }

      this.logger.debug("战术克制（含教练加成）计算完成:", restrainTeam);
      return restrainTeam;

    } catch (error) {
      this.logger.error('计算战术克制关系（含教练）失败', error);
      return {
        atkTacticsAState: 0,
        atkTacticsBState: 0,
        attackAFactor: 1,
        defendAFactor: 1,
        attackBFactor: 1,
        defendBFactor: 1,
        attackTrainerAFactor: 0,
        defendTrainerAFactor: 0,
        attackTrainerBFactor: 0,
        defendTrainerBFactor: 0
      };
    }
  }

  /**
   * 🔧 计算教练战术加成
   * 基于old项目中教练对战术的影响逻辑
   */
  private calculateTrainerTacticBonus(trainer: any, attackTactic: number, defendTactic: number): { attack: number; defend: number } {
    try {
      let attackBonus = 0;
      let defendBonus = 0;

      // 基于教练的战术专长和等级计算加成
      if (trainer.tactics && trainer.tactics.length > 0) {
        for (const tacticSkill of trainer.tactics) {
          // 检查教练是否专精当前使用的战术
          if (tacticSkill.type === attackTactic) {
            attackBonus += tacticSkill.level * 0.01; // 每级1%加成
          }
          if (tacticSkill.type === defendTactic) {
            defendBonus += tacticSkill.level * 0.01; // 每级1%加成
          }
        }
      }

      return { attack: attackBonus, defend: defendBonus };
    } catch (error) {
      this.logger.warn('计算教练战术加成失败', error);
      return { attack: 0, defend: 0 };
    }
  }
}
