import { BattleSkillSystem } from './../systems/battle-skill-system';
import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleTeam, AttackMode, PeriodResult, BattleHero, AttackModeConfig } from '../types/battle-data.types';
import { BattleType } from '@libs/game-constants';



/**
 * 三阶段战斗计算器 - 性能优化版本
 * 基于old项目room.js的三阶段战斗逻辑
 *
 * 职责：
 * - 发起阶段计算 (calcStartPeriod)
 * - 推进阶段计算 (calcMiddlePeriod)
 * - 射门阶段计算 (calcEndPeriod)
 * - 进攻方式计算 (calcAttackMode)
 *
 * 🚀 性能优化：
 * - 预计算攻击模式权重
 * - 缓存重复计算结果
 * - 优化随机数生成
 * - 减少对象创建
 */
export class BattlePeriodCalculator {
  private readonly logger = new Logger(BattlePeriodCalculator.name);

  // 🚀 性能优化：预计算攻击模式权重
  private readonly attackModes: AttackModeConfig[] = [
    { id: AttackMode.Header, weight: 30, name: '头球' },      // 头球进攻
    { id: AttackMode.LongShot, weight: 15, name: '远射' },   // 远射
    { id: AttackMode.Push, weight: 25, name: '推射' },       // 推射
    { id: AttackMode.Scramble, weight: 20, name: '抢点' },   // 抢点
    { id: AttackMode.Lob, weight: 8, name: '吊射' },         // 吊射
    { id: AttackMode.OneOnOne, weight: 2, name: '单刀' }     // 单刀（稀有）
  ];

  // 🚀 性能优化：预计算总权重
  private readonly totalBaseWeight = this.attackModes.reduce((sum, mode) => sum + mode.weight, 0);

  // 🚀 性能优化：缓存计算结果
  private readonly calculationCache = new Map<string, any>();
  private readonly maxCacheSize = 1000; // 最大缓存条目数
  private cacheAccessCount = 0; // 缓存访问计数器

  // 🚀 性能优化：随机数生成器优化
  private randomSeed = Date.now();

  constructor(
    private readonly gameConfig: GameConfigFacade,
    private readonly battleSkillSystem?: BattleSkillSystem,
    private readonly battleType?: BattleType,
  ) {}



  /**
   * 🚀 缓存管理 - 防止内存泄漏
   */
  private manageCache(): void {
    this.cacheAccessCount++;

    // 每1000次访问清理一次缓存
    if (this.cacheAccessCount % 1000 === 0) {
      if (this.calculationCache.size > this.maxCacheSize) {
        // 清理最旧的50%缓存条目
        const entries = Array.from(this.calculationCache.entries());
        const keepCount = Math.floor(this.maxCacheSize * 0.5);

        this.calculationCache.clear();

        // 保留最近的条目
        for (let i = entries.length - keepCount; i < entries.length; i++) {
          this.calculationCache.set(entries[i][0], entries[i][1]);
        }

        this.logger.debug(`缓存清理完成，保留 ${keepCount} 个条目`);
      }
    }
  }

  /**
   * 🚀 优化的随机数生成器 - 使用线性同余生成器提升性能
   */
  private fastRandom(): number {
    this.randomSeed = (this.randomSeed * 1664525 + 1013904223) % 4294967296;
    return this.randomSeed / 4294967296;
  }

  /**
   * 🚀 计算进攻方式 - 性能优化版本
   * 基于old项目: Room.prototype.calcAttackModeByTactic
   *
   * 优化点：
   * - 使用预计算的攻击模式数组
   * - 缓存权重调整结果
   * - 优化随机数生成
   * - 减少对象创建和数组操作
   */
  calcAttackMode(attackerTeam: BattleTeam): AttackMode {
    try {
      // 🚀 优化：特殊情况检查（快速路径）
      const specialMode = this.checkSpecialAttackMode(attackerTeam);
      if (specialMode !== null) {
        return specialMode;
      }

      // 🚀 优化：生成缓存键
      const cacheKey = `attackMode_${attackerTeam.totalAttack}_${attackerTeam.totalDefend}_${attackerTeam.attr.morale}`;

      // 🚀 优化：检查缓存
      if (this.calculationCache.has(cacheKey)) {
        this.manageCache(); // 缓存管理
        const cachedModes = this.calculationCache.get(cacheKey);
        return this.selectWeightedRandom(cachedModes);
      }

      // 🚀 优化：根据队伍属性调整权重（只计算一次）
      const adjustedModes = this.adjustAttackModeWeights(attackerTeam, this.attackModes);

      // 🚀 优化：缓存调整后的权重
      this.calculationCache.set(cacheKey, adjustedModes);

      return this.selectWeightedRandom(adjustedModes);
    } catch (error) {
      this.logger.error('计算进攻方式失败', error);
      return AttackMode.Header; // 默认返回头球攻击
    }
  }

  /**
   * 🚀 优化的加权随机选择
   * 使用快速随机数生成器和预计算总权重
   *
   * @param modes 攻击模式配置数组 - 包含id、weight、name的配置对象
   * @returns AttackMode 选中的攻击模式枚举值
   */
  private selectWeightedRandom(modes: AttackModeConfig[]): AttackMode {
    // 🚀 优化：预计算总权重
    const totalWeight = modes.reduce((sum, mode) => sum + mode.weight, 0);
    let randomValue = this.fastRandom() * totalWeight;

    // 🚀 优化：使用for循环而不是forEach，性能更好
    for (let i = 0; i < modes.length; i++) {
      randomValue -= modes[i].weight;
      if (randomValue <= 0) {
        this.logger.debug(`选择进攻方式: ${modes[i].name} (ID: ${modes[i].id})`);
        return modes[i].id;
      }
    }

    // 默认返回头球攻击
    return AttackMode.Header;
  }

  /**
   * 🚀 预计算队伍对比数据 - 避免重复计算
   *
   * @param attackerTeam 攻击方队伍数据 - 包含总攻击力等属性
   * @param defenderTeam 防守方队伍数据 - 包含总防守力等属性
   * @returns 对比数据对象 - 包含攻击值和防守值
   */
  private calculateTeamComparison(attackerTeam: BattleTeam, defenderTeam: BattleTeam): {
    attackValue: number;
    defendValue: number;
    baseRate: number;
    moraleBonus: number;
  } {
    const cacheKey = `teamComp_${attackerTeam.totalAttack}_${attackerTeam.totalDefend}_${attackerTeam.attr.morale}_${defenderTeam.totalAttack}_${defenderTeam.totalDefend}_${defenderTeam.attr.morale}`;

    if (this.calculationCache.has(cacheKey)) {
      this.manageCache(); // 缓存管理
      return this.calculationCache.get(cacheKey);
    }

    const attackValue = attackerTeam.totalAttack || 0;
    const defendValue = defenderTeam.totalDefend || 0;

    // 防止除零错误
    const totalValue = attackValue + defendValue;
    let baseRate = 0.5; // 默认50%
    if (totalValue > 0) {
      baseRate = attackValue / totalValue;
    } else {
      this.logger.warn(`队伍属性值为0: 攻击=${attackValue}, 防守=${defendValue}`);
    }

    const attackerMorale = attackerTeam.attr?.morale || 500;
    const defenderMorale = defenderTeam.attr?.morale || 500;
    let moraleBonus = (attackerMorale - defenderMorale) / 1000;

    // 检查计算结果
    if (isNaN(baseRate) || isNaN(moraleBonus)) {
      this.logger.error(`队伍对比计算产生NaN: attackValue=${attackValue}, defendValue=${defendValue}, totalValue=${totalValue}, baseRate=${baseRate}, moraleBonus=${moraleBonus}`);
      baseRate = isNaN(baseRate) ? 0.5 : baseRate;
      moraleBonus = isNaN(moraleBonus) ? 0 : moraleBonus;
    }

    const result = { attackValue, defendValue, baseRate, moraleBonus };
    this.calculationCache.set(cacheKey, result);

    return result;
  }

  /**
   * 🚀 发起阶段计算 - 性能优化版本
   * 基于old项目room.js的calcStartPeriod逻辑 - 发起阶段100%成功
   *
   * @param attackerTeam 攻击方队伍数据 - 当前实现中未使用，保留接口兼容性
   * @param defenderTeam 防守方队伍数据 - 当前实现中未使用，保留接口兼容性
   * @param attackMode 攻击模式 - 用于日志记录和调试
   * @param roundIndex 回合索引 - 用于日志记录和调试
   * @param skillAddPer 技能加成百分比 - 当前实现中未使用
   * @returns Promise<PeriodResult> 发起结果 - 总是返回成功(100%成功率)
   */
  async calcStartPeriod(
    attackerTeam: BattleTeam,
    _defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    _skillAddPer: number = 0
  ): Promise<PeriodResult> {
    // 🚀 优化：发起阶段总是成功，无需复杂计算
    this.logger.debug(`发起阶段: 攻击方式=${attackMode}, 回合=${roundIndex}`);

    // 🔧 基于old项目：传球评分更新（发起阶段100%成功）
    this.updatePassHeroScores(attackerTeam);

    // 🔧 基于old项目：计算动作ID用于评论生成（同步降级方案）
    const actionID = await this.calcRoundActionID(attackMode, 0);

    // 🚀 优化：返回预定义对象，避免重复创建
    const result = { success: true, rate: 1.0, actionID };
    if (isNaN(result.rate)) {
      this.logger.error(`发起阶段计算产生NaN: attackMode=${attackMode}, roundIndex=${roundIndex}`);
    }
    return result;
  }

  /**
   * ⚽ 推进阶段计算 - 足球比赛中攻击方突破防守的核心业务逻辑
   *
   * 📋 业务背景：
   * 在足球比赛中，攻击方获得球权后需要突破防守方的防线才能获得射门机会。
   * 推进阶段模拟了攻击球员（A1）与防守球员（B）之间的对抗过程。
   *
   * 🎯 业务流程：
   * 1. 动作记录：为本次推进生成唯一的动作ID，用于战报和回放
   * 2. 防守配置：根据攻击模式决定是否需要防守球员B参与防守
   * 3. 突破计算：基于球员属性、技能效果、攻击模式计算突破成功率
   * 4. 结果判定：使用随机数判定突破是否成功
   * 5. 数据更新：更新球员评分、队伍统计、士气变化等数据
   *
   * 🏈 攻击模式分类：
   * - 定位球（头球、任意球、点球）：无突破阶段，直接100%成功
   * - 运动战（远射、推射、抢点、吊射、单刀、角球）：需要突破计算
   *
   * 📊 成功率影响因素：
   * - 球员属性：攻击方A1的相关属性 vs 防守方B的相关属性
   * - 技能效果：激活技能对成功率的加成或减成
   * - 攻击模式：不同攻击方式使用不同的属性权重配置
   * - 随机因素：模拟比赛中的不确定性
   *
   * 💡 业务规则：
   * - 突破成功：进入射门阶段，有机会得分
   * - 突破失败：攻击结束，防守方获得球权，攻击方士气-1，防守方士气+2
   * - 特殊模式：FirstBattle使用预设数据，确保剧情一致性
   */
  async calcMiddlePeriod(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    skillAddPer: number = 0
  ): Promise<PeriodResult> {
    try {
      // 📝 步骤1：生成动作记录ID
      // 业务目的：为本次推进动作分配唯一标识，用于战报生成和比赛回放
      const actionID = await this.calcRoundActionID(attackMode, 1);

      // 🛡️ 步骤2：确定防守配置
      // 业务逻辑：不同攻击模式需要不同的防守配置
      // - 远射、推射、抢点、吊射、单刀、角球：需要防守球员B参与一对一防守
      // - 头球、任意球、点球：主要由门将防守，不需要额外的防守球员
      const needDefenderB = [AttackMode.LongShot, AttackMode.Push, AttackMode.Scramble,
                            AttackMode.Lob, AttackMode.OneOnOne, AttackMode.CornerKick].includes(attackMode);

      if (needDefenderB) {
        this.logger.debug(`攻击模式${attackMode}需要防守球员B参与突破防守`);
      }

      // ⚔️ 步骤3：执行突破对抗计算
      // 业务核心：模拟攻击球员与防守球员的对抗过程
      // 考虑因素：球员属性、技能效果、攻击模式、随机因素
      const breakResult = await this.calcBreakThrough(
        attackerTeam, defenderTeam, attackMode, roundIndex, skillAddPer
      );

      // 📊 步骤4：更新比赛数据
      // 业务影响：突破结果会影响球员评分、队伍统计、士气变化
      // - 成功：球员评分+6，突破成功次数+1
      // - 失败：攻击方士气-1，防守方士气+2
      this.updateBreakStatistics(attackerTeam, defenderTeam, breakResult.success, breakResult.per);

      // 🎯 步骤5：返回推进结果
      // 业务决策：成功则进入射门阶段，失败则攻击结束
      return {
        success: breakResult.success,        // 突破是否成功
        rate: breakResult.per / 1000,        // 成功率（0-1范围，用于AI分析）
        actionID                             // 动作ID（用于战报关联）
      };

    } catch (error) {
      this.logger.error('推进阶段计算失败', error);
      return this.calcMiddlePeriodFallback(attackerTeam, defenderTeam, skillAddPer);
    }
  }

  /**
   * ⚔️ 突破对抗核心计算 - 模拟攻防双方的实力对比
   *
   * 📋 业务逻辑：
   * 突破阶段是足球比赛中攻击方试图突破防守方防线的关键环节。
   * 通过对比攻击球员和防守球员的相关属性，结合技能效果和随机因素，
   * 计算出突破成功的概率，并进行随机判定。
   *
   * 🎯 计算流程：
   * 1. 技能触发：检查并触发符合条件的球员技能
   * 2. 模式分类：根据攻击模式采用不同的计算策略
   * 3. 属性对比：获取攻防双方球员的相关属性进行对比
   * 4. 成功率计算：基于属性差异计算基础成功率
   * 5. 随机判定：使用随机数决定最终结果
   * 6. 特殊处理：FirstBattle模式使用预设结果
   *
   * 🏈 攻击模式策略：
   * - 定位球（头球、任意球、点球）：固定100%成功，无需计算
   * - 运动战：基于球员属性和配置表进行复杂计算
   *
   * 📊 影响因素：
   * - 球员属性：速度、射门、传球、防守等核心属性
   * - 技能效果：激活技能提供的属性加成或成功率修正
   * - 攻击模式：不同模式使用不同的属性权重配置
   * - 随机因素：模拟比赛中的不确定性和运气成分
   */
  private async calcBreakThrough(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    skillAddPer: number = 0
  ): Promise<{ per: number; success: boolean }> {
    try {
      let per = 0; // 成功率（千分制：0-1000）

      // 🎭 技能系统处理
      // 业务规则：定位球（头球、任意球、点球）无突破过程，不触发推进阶段技能
      // 运动战需要触发相关技能来影响突破成功率
      if (attackMode !== AttackMode.Header && attackMode !== AttackMode.FreeKick && attackMode !== AttackMode.PenaltyKick) {
        if (this.battleSkillSystem) {
          // 🔍 识别参与球员
          // 业务逻辑：确定哪些球员参与本次推进，以便触发相应技能
          const participatingTypes = this.getParticipatingHeroTypes(attackMode, 1);
          const participatingHerosList = this.buildParticipatingHerosList(
            participatingTypes, 1, attackerTeam, defenderTeam
          );

          // ⚡ 触发技能效果
          // 业务影响：激活的技能会影响球员属性和成功率
          this.battleSkillSystem.triggerSkillsInPeriod(1, attackMode, roundIndex, participatingHerosList);

          // 📋 记录技能效果
          // 业务目的：将技能效果记录到战报中，供客户端回放使用
          this.battleSkillSystem.fillSkillEffectList(1, attackMode, roundIndex, attackerTeam);

          // 📈 计算技能加成
          // 业务逻辑：汇总所有激活技能对成功率的影响
          const skillChangePer = this.battleSkillSystem.calcSkillChangePer(1, attackMode, roundIndex);
          skillAddPer += skillChangePer;
        }
      }

      // 🏈 攻击模式分类处理
      // 业务逻辑：不同攻击方式有不同的突破机制
      switch (attackMode) {
        case AttackMode.Header: // 头球攻击
          // 业务规则：头球是定位球，球员已经获得良好的攻击位置，无需突破防线
          per = 1000; // 固定100%成功率
          break;

        case AttackMode.LongShot:    // 远射攻击
        case AttackMode.Push:        // 推射攻击
        case AttackMode.Scramble:    // 抢点攻击
        case AttackMode.Lob:         // 吊射攻击
        case AttackMode.OneOnOne:    // 单刀攻击
        case AttackMode.CornerKick:  // 角球攻击
          // 业务规则：运动战攻击需要突破防守球员，基于双方属性对比计算成功率
          per = await this.calcBreakThroughWithAttributes(
            attackerTeam, defenderTeam, attackMode, skillAddPer
          );
          break;

        case AttackMode.FreeKick:    // 任意球攻击
        case AttackMode.PenaltyKick: // 点球攻击
          // 业务规则：定位球攻击，球员已经获得无人防守的攻击机会
          per = 1000; // 固定100%成功率
          break;

        default:
          // 异常处理：未知攻击模式使用默认成功率
          this.logger.warn(`未知的攻击模式: ${attackMode}`);
          per = 500; // 默认50%成功率
      }

      // 🎲 随机判定机制
      // 业务逻辑：基于计算出的成功率进行随机判定，模拟比赛中的不确定性
      const rand = Math.floor(Math.random() * 100) + 1; // 生成1-100的随机数
      let success = false;

      if (per >= 1000) {
        // 业务规则：千分制1000表示绝对成功（定位球等特殊情况）
        success = true;
      } else {
        // 业务计算：千分制转百分制进行判定
        // 例如：per=600（60%成功率），rand=45，则45<=60，判定成功
        const perPercent = per / 10;
        success = rand <= perPercent;
      }

      // 🎬 剧情模式特殊处理
      // 业务需求：FirstBattle（新手引导战）需要使用预设结果确保剧情一致性
      if (this.battleType == BattleType.FIRST_BATTLE) {
        if (attackerTeam.firstBattleData && attackerTeam.firstBattleData.battleRoundInfo[roundIndex]) {
          const periodInfo = attackerTeam.firstBattleData.battleRoundInfo[roundIndex].periodInfo;
          if (periodInfo && periodInfo[1]) { // 推进阶段是索引1
            // 使用预设的成功率和结果，覆盖计算值
            per = periodInfo[1].actionPer || per;
            success = periodInfo[1].actionResult === 1;
            this.logger.debug('FirstBattle模式，使用预设突破结果');
          }
        }
      }

      this.logger.debug(`突破计算: 攻击模式=${attackMode}, 成功率=${per}‰, 随机数=${rand}, 结果=${success ? '成功' : '失败'}`);

      return { per, success };

    } catch (error) {
      this.logger.error('突破计算失败', error);
      return { per: 500, success: Math.random() < 0.5 };
    }
  }

  /**
   * ⚔️ 基于当前项目架构计算射门成功率
   *
   * 使用当前项目的真实接口和数据结构：
   * - AttackModeDefinition的实际字段命名
   * - BattleSkillSystem.getHeroBattleAttr的真实签名
   * - 当前项目的属性索引映射逻辑
   */
  private async calcShotResultWithCurrentArchitecture(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    skillAddPer: number = 0
  ): Promise<{ per: number; success: boolean }> {
    try {
      let per = 0; // 成功率（百分制：0-100）

      // 🎭 技能系统处理
      // 业务逻辑：射门阶段需要激活所有参与球员的相关技能
      if (this.battleSkillSystem) {
        // 🔍 识别参与球员
        const participatingTypes = this.getParticipatingHeroTypes(attackMode, 2);
        const participatingHerosList = this.buildParticipatingHerosList(
          participatingTypes, 2, attackerTeam, defenderTeam
        );

        // ⚡ 触发技能效果
        this.battleSkillSystem.triggerSkillsInPeriod(2, attackMode, roundIndex, participatingHerosList);

        // 📋 记录技能效果
        this.battleSkillSystem.fillSkillEffectList(2, attackMode, roundIndex, attackerTeam);

        // 📈 计算技能加成
        const skillChangePer = this.battleSkillSystem.calcSkillChangePer(2, attackMode, roundIndex);
        skillAddPer += skillChangePer;
      }

      // 🏈 角球特殊处理 - 基于old项目第1464-1467行
      // 业务规则：角球时清空A1和B球员，由A2执行射门
      if (attackMode === AttackMode.CornerKick) {
        if (attackerTeam.roundInfo.A1Info) {
          attackerTeam.roundInfo.A1Info.heroId = "";
        }
        if (attackerTeam.roundInfo.BInfo) {
          attackerTeam.roundInfo.BInfo.heroId = "";
        }
      }

      // 📊 射门成功率计算
      per = await this.calcShotWithRealAttributes(
        attackerTeam, defenderTeam, attackMode, skillAddPer
      );

      // 🎲 随机判定机制
      // 业务逻辑：基于计算出的成功率进行随机判定，模拟比赛中的不确定性
      const rand = Math.floor(Math.random() * 100) + 1; // 生成1-100随机数
      let success = rand <= per;

      // 🎬 剧情模式特殊处理
      // 业务需求：FirstBattle（新手引导战）需要使用预设结果确保剧情一致性
      if (this.battleType == BattleType.FIRST_BATTLE) {
        if (attackerTeam.firstBattleData && attackerTeam.firstBattleData.battleRoundInfo[roundIndex]) {
          const periodInfo = attackerTeam.firstBattleData.battleRoundInfo[roundIndex].periodInfo;
          if (periodInfo && periodInfo[2]) { // 射门阶段是索引2
            // 使用预设的成功率和结果，覆盖计算值
            per = periodInfo[2].actionPer || per;
            success = periodInfo[2].actionResult === 1;
            this.logger.debug('FirstBattle模式，使用预设射门结果');
          }
        }
      }

      this.logger.debug(`射门计算: 攻击模式=${attackMode}, 成功率=${per}%, 随机数=${rand}, 结果=${success ? '⚽进球' : '🚫失败'}`);

      return { per, success };

    } catch (error) {
      this.logger.error('射门计算失败', error);
      return { per: 50, success: Math.random() < 0.5 };
    }
  }

  /**
   * 🚀 基于当前项目真实接口计算射门属性
   *
   * 使用当前项目的实际字段：
   * - AttackModeDefinition: a1ShType1, a1ShFactor1, gKShType1, gKShFactor1等
   * - BattleSkillSystem.getHeroBattleAttr的真实签名
   * - 当前项目的属性索引映射
   */
  private async calcShotWithRealAttributes(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    skillAddPer: number
  ): Promise<number> {
    try {
      // 🚀 获取AttackMode配置 - 使用当前项目的真实接口
      const config = await this.gameConfig.attackMode.get(attackMode);
      if (!config) {
        this.logger.warn(`未找到攻击模式配置: ${attackMode}`);
        return 50; // 默认50%成功率
      }

      // ⚔️ 初始化攻防数值
      // 业务逻辑：攻击方（A1+A2）vs 防守方（GK+B）的属性对比
      let attackerData = 0;  // 攻击方综合属性值
      let defenderData = 0;  // 防守方综合属性值

      // 🎯 A1球员属性计算（主攻球员）
      // 业务角色：主要的射门执行者，贡献核心攻击力
      const A1HeroId = attackerTeam.roundInfo.A1Info?.heroId;
      if (A1HeroId && this.battleSkillSystem) {
        const A1Hero = this.getHeroFromTeam(attackerTeam, A1HeroId);
        if (A1Hero) {
          // 📊 A1主属性计算（a1ShType1）
          if (config.a1ShType1 && config.a1ShType1 !== 0) {
            const attrName = this.getAttributeNameByIndex(config.a1ShType1);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                attackerTeam.teamSide, A1Hero, attrName, this.battleType, attackerTeam, 0
              );
              attackerData += (attrObj.cur + attrObj.add) * (config.a1ShFactor1 || 0);
            }
          }

          // 📊 A1副属性计算（a1ShType2）
          if (config.a1ShType2 && config.a1ShType2 !== 0) {
            const attrName = this.getAttributeNameByIndex(Number(config.a1ShType2));
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                attackerTeam.teamSide, A1Hero, attrName, this.battleType, attackerTeam, 0
              );
              attackerData += (attrObj.cur + attrObj.add) * (config.a1ShFactor2 || 0);
            }
          }
        }
      }

      // 🤝 A2球员属性计算（协攻球员）
      // 业务角色：提供辅助攻击支持，如传球、跑位、牵制防守等
      const A2HeroId = attackerTeam.roundInfo.A2Info?.heroId;
      if (A2HeroId && this.battleSkillSystem) {
        const A2Hero = this.getHeroFromTeam(attackerTeam, A2HeroId);
        if (A2Hero) {
          // 📊 A2主属性计算（a2ShType1）
          if (config.a2ShType1 && config.a2ShType1 !== 0) {
            const attrName = this.getAttributeNameByIndex(config.a2ShType1);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                attackerTeam.teamSide, A2Hero, attrName, this.battleType, attackerTeam, 0
              );
              attackerData += (attrObj.cur + attrObj.add) * (config.a2ShFactor1 || 0);
            }
          }

          // 📊 A2副属性计算（a2ShType2）
          if (config.a2ShType2 && config.a2ShType2 !== 0) {
            const attrIndex = typeof config.a2ShType2 === 'string' ?
              parseInt(config.a2ShType2) : config.a2ShType2;
            const attrName = this.getAttributeNameByIndex(attrIndex);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                attackerTeam.teamSide, A2Hero, attrName, this.battleType, attackerTeam, 0
              );
              attackerData += (attrObj.cur + attrObj.add) * (config.a2ShFactor2 || 0);
            }
          }
        }
      }

      // 🥅 GK门将属性计算（主要防守者）
      // 业务角色：守门员是射门阶段的核心防守者，使用守门、反应等专业属性
      const GKHeroId = defenderTeam.roundInfo.GKInfo?.heroId;
      if (GKHeroId && this.battleSkillSystem) {
        const GKHero = this.getHeroFromTeam(defenderTeam, GKHeroId);
        if (GKHero) {
          // 📊 GK主属性计算（gKShType1）
          if (config.gKShType1 && config.gKShType1 !== 0) {
            const attrName = this.getAttributeNameByIndex(config.gKShType1);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                defenderTeam.teamSide, GKHero, attrName, this.battleType, defenderTeam, 0
              );
              defenderData += (attrObj.cur + attrObj.add) * (config.gKShFactor1 || 0);
            }
          }

          // 📊 GK副属性计算（gKShType2）
          if (config.gKShType2 && config.gKShType2 !== 0) {
            const attrIndex = typeof config.gKShType2 === 'string' ?
              parseInt(config.gKShType2) : config.gKShType2;
            const attrName = this.getAttributeNameByIndex(attrIndex);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                defenderTeam.teamSide, GKHero, attrName, this.battleType, defenderTeam, 0
              );
              defenderData += (attrObj.cur + attrObj.add) * (config.gKShFactor2 || 0);
            }
          }
        }
      }

      // 🛡️ B防守球员属性计算（辅助防守者）
      // 业务角色：协助门将进行防守，使用防守、拦截、位置等属性
      const BHeroId = attackerTeam.roundInfo.BInfo?.heroId;
      if (BHeroId && this.battleSkillSystem) {
        const BHero = this.getHeroFromTeam(defenderTeam, BHeroId);
        if (BHero) {
          // 📊 B主属性计算（bShType1）
          if (config.bShType1 && config.bShType1 !== 0) {
            const attrIndex = typeof config.bShType1 === 'string' ?
              parseInt(config.bShType1) : config.bShType1;
            const attrName = this.getAttributeNameByIndex(attrIndex);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                defenderTeam.teamSide, BHero, attrName, this.battleType, defenderTeam, 0
              );
              defenderData += (attrObj.cur + attrObj.add) * (config.bShFactor1 || 0);
            }
          }

          // 📊 B副属性计算（bShType2）
          if (config.bShType2 && config.bShType2 !== 0) {
            const attrName = this.getAttributeNameByIndex(config.bShType2);
            if (attrName && attrName !== 'default') {
              const attrObj = this.battleSkillSystem.getHeroBattleAttr(
                defenderTeam.teamSide, BHero, attrName, this.battleType, defenderTeam, 0
              );
              defenderData += (attrObj.cur + attrObj.add) * (config.bShFactor2 || 0);
            }
          }
        }
      }

      // 🚀 基础成功率计算 - 完全基于old项目第1565-1566行的公式
      // per = attackerData / (attackerData + defenderData) * battlePerFactor / 10000 * 100 * ForTestData.shotFactor;
      const battlePerFactor = 10000; // SystemParam.battlePerFactor，默认10000
      const shotFactor = 1; // ForTestData.shotFactor，默认1（注意：射门用shotFactor，不是breakFactor）
      let per = Math.round(attackerData / (attackerData + defenderData) * battlePerFactor / 10000 * 100 * shotFactor);

      // 🚀 技能加成 - 基于old项目第1567行
      per += skillAddPer;

      // 🚀 成功率范围限制 - 基于old项目第1568-1573行
      const minBattlePer = 5; // 对应SystemParam中MinBattlePer=500的5%
      const maxBattlePer = 95; // 对应SystemParam中MaxBattlePer=9500的95%

      if (per < minBattlePer) {
        per = minBattlePer;
      }
      if (per > maxBattlePer) {
        per = maxBattlePer;
      }

      this.logger.debug(`射门属性计算: 攻击=${attackerData}, 防守=${defenderData}, 基础成功率=${per}%, 技能加成=${skillAddPer}`);

      return per; // 返回百分制数值

    } catch (error) {
      this.logger.error('射门属性计算失败', error);
      return 50; // 默认50%成功率
    }
  }

  /**
   * 🚀 基于当前项目真实接口计算突破属性
   *
   * 使用当前项目的实际字段：
   * - AttackModeDefinition: a1BrType1, a1BrFactor1, bBrType1, bBrFactor1等
   * - BattleSkillSystem.getHeroBattleAttr的真实签名
   * - 当前项目的属性索引映射
   */
  private async calcBreakThroughWithAttributes(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    skillAddPer: number
  ): Promise<number> {
    try {
      // 🚀 获取AttackMode配置 - 使用当前项目的真实接口
      const config = await this.gameConfig.attackMode.get(attackMode);
      if (!config) {
        this.logger.warn(`未找到攻击模式配置: ${attackMode}`);
        return 500; // 默认50%成功率
      }

      // 🚀 获取参与球员 - 基于当前项目的RoundInfo结构
      const heroA1 = attackerTeam.roundInfo.A1Info?.heroId ?
        this.getHeroFromTeam(attackerTeam, attackerTeam.roundInfo.A1Info.heroId) : null;
      const heroB = attackerTeam.roundInfo.BInfo?.heroId ?
        this.getHeroFromTeam(defenderTeam, attackerTeam.roundInfo.BInfo.heroId) : null;

      let attackValue = 0;
      let defendValue = 0;

      // 🚀 计算攻击方A1属性值 - 使用当前项目的真实字段名
      if (heroA1 && this.battleSkillSystem) {
        // A1BrType1属性计算
        if (config.a1BrType1 && config.a1BrType1 !== 0) {
          const attrName = this.getAttributeNameByIndex(config.a1BrType1);
          if (attrName && attrName !== 'default') {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide, heroA1, attrName, this.battleType, attackerTeam, 0
            );
            attackValue += (attrObj.cur + attrObj.add) * (config.a1BrFactor1 || 0);

            // 记录属性信息到roundInfo
            if (attackerTeam.roundInfo.A1Info) {
              attackerTeam.roundInfo.A1Info.attrType1 = config.a1BrType1;
              attackerTeam.roundInfo.A1Info.attrValue1 = attrObj.cur;
              attackerTeam.roundInfo.A1Info.addValue1 = attrObj.add;
            }
          }
        }

        // A1BrType2属性计算
        if (config.a1BrType2 && config.a1BrType2 !== 0) {
          const attrName = this.getAttributeNameByIndex(Number(config.a1BrType2));
          if (attrName && attrName !== 'default') {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide, heroA1, attrName, this.battleType, attackerTeam, 0
            );
            attackValue += (attrObj.cur + attrObj.add) * (config.a1BrFactor2 || 0);

            // 记录属性信息到roundInfo
            if (attackerTeam.roundInfo.A1Info) {
              attackerTeam.roundInfo.A1Info.attrType2 = Number(config.a1BrType2);
              attackerTeam.roundInfo.A1Info.attrValue2 = attrObj.cur;
              attackerTeam.roundInfo.A1Info.addValue2 = attrObj.add;
            }
          }
        }
      }

      // 🚀 计算防守方B属性值 - 使用当前项目的真实字段名
      if (heroB && this.battleSkillSystem) {
        // BBrType1属性计算
        if (config.bBrType1 && config.bBrType1 !== 0) {
          const attrName = this.getAttributeNameByIndex(config.bBrType1);
          if (attrName && attrName !== 'default') {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide, heroB, attrName, this.battleType, defenderTeam, 0
            );
            defendValue += (attrObj.cur + attrObj.add) * (config.bBrFactor1 || 0);

            // 记录属性信息到roundInfo
            if (attackerTeam.roundInfo.BInfo) {
              attackerTeam.roundInfo.BInfo.attrType1 = config.bBrType1;
              attackerTeam.roundInfo.BInfo.attrValue1 = attrObj.cur;
              attackerTeam.roundInfo.BInfo.addValue1 = attrObj.add;
            }
          }
        }

        // BBrType2属性计算
        if (config.bBrType2 && config.bBrType2 !== 0) {
          const attrName = this.getAttributeNameByIndex(Number(config.bBrType2));
          if (attrName && attrName !== 'default') {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide, heroB, attrName, this.battleType, defenderTeam, 0
            );
            defendValue += (attrObj.cur + attrObj.add) * (config.bBrFactor2 || 0);

            // 记录属性信息到roundInfo
            if (attackerTeam.roundInfo.BInfo) {
              attackerTeam.roundInfo.BInfo.attrType2 = Number(config.bBrType2);
              attackerTeam.roundInfo.BInfo.attrValue2 = attrObj.cur;
              attackerTeam.roundInfo.BInfo.addValue2 = attrObj.add;
            }
          }
        }
      }

      // 🚀 基础成功率计算 - 完全基于old项目第1392-1393行的公式
      // per = Math.round(attackValue / (attackValue + defendValue) * battlePerFactor / 10000 * 100 * breakFactor);
      const battlePerFactor = 10000; // SystemParam.battlePerFactor，默认10000
      const breakFactor = 1; // ForTestData.breakFactor，默认1
      let per = Math.round(attackValue / (attackValue + defendValue) * battlePerFactor / 10000 * 100 * breakFactor);

      // 🚀 技能加成 - 基于old项目第1395行
      per += skillAddPer;

      // 🚀 成功率范围限制 - 基于old项目第1396-1401行
      // old项目：MinBattlePer.Param/100 和 MaxBattlePer.Param/100
      // 这里的Param是百分制数值，如500表示5%，9500表示95%
      const minBattlePer = 5; // 对应SystemParam中MinBattlePer=500的5%
      const maxBattlePer = 95; // 对应SystemParam中MaxBattlePer=9500的95%

      if (per < minBattlePer) {
        per = minBattlePer;
      }
      if (per > maxBattlePer) {
        per = maxBattlePer;
      }

      // 🚀 转换为千分制 - 保持与系统其他部分的一致性
      // old项目中per是百分制（0-100），我们转换为千分制（0-1000）
      const perThousand = per * 10;

      this.logger.debug(`突破属性计算: 攻击=${attackValue}, 防守=${defendValue}, 基础成功率=${per}%, 千分制=${perThousand}‰, 技能加成=${skillAddPer}`);

      return perThousand; // 返回千分制数值

    } catch (error) {
      this.logger.error('突破属性计算失败', error);
      return 500; // 默认50%成功率（千分制）
    }
  }

  /**
   * ⚽ 射门阶段计算 - 足球比赛中攻击方尝试得分的关键业务逻辑
   *
   * 📋 业务背景：
   * 射门阶段是足球比赛攻击的最终环节，攻击方球员面对防守方门将和后卫，
   * 尝试将球射入球门得分。这是决定比赛结果的关键时刻。
   *
   * 🎯 业务流程：
   * 1. 技能触发：激活射门阶段相关的球员技能
   * 2. 配置获取：从AttackMode配置表获取属性权重配置
   * 3. 攻击计算：计算A1（主攻）+ A2（协攻）的综合攻击力
   * 4. 防守计算：计算GK（门将）+ B（防守球员）的综合防守力
   * 5. 成功率计算：基于攻防对比计算射门成功率
   * 6. 随机判定：使用随机数决定射门是否成功
   * 7. 数据更新：更新射门统计、球员评分等数据
   *
   * 🏈 参与球员角色：
   * - A1（主攻球员）：主要的射门执行者，贡献主要攻击力
   * - A2（协攻球员）：辅助攻击，提供额外的攻击支持
   * - GK（门将）：主要防守者，使用守门、反应等属性
   * - B（防守球员）：辅助防守，协助门将阻止进球
   *
   * 📊 成功率影响因素：
   * - 球员属性：射门、传球、速度 vs 守门、防守、反应
   * - 技能效果：射门技能、守门技能的激活效果
   * - 攻击模式：不同射门方式使用不同的属性权重
   * - 随机因素：模拟比赛中的运气和意外情况
   *
   * 💡 业务规则：
   * - 射门成功：攻击方得分，比赛继续或结束
   * - 射门失败：防守方获得球权，攻击结束
   * - 特殊处理：FirstBattle使用预设结果确保剧情一致性
   * - 成功率限制：系统参数限制最小和最大成功率范围
   */
  async calcEndPeriod(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    _attackMode: AttackMode,
    _roundIndex: number,
    skillAddPer: number = 0
  ): Promise<PeriodResult> {
    try {
      // 📝 步骤1：生成动作记录ID
      // 业务目的：为本次射门动作分配唯一标识，用于战报生成和比赛回放
      const actionID = await this.calcRoundActionID(_attackMode, 2);

      // 🤝 步骤2：条件性选择A2协攻球员 - 基于old项目第957-959行
      // 业务逻辑：只有特定攻击模式需要A2球员参与协助进攻
      // (1.头球, 3.推射, 4.抢点, 8.角球)
      const needA2Player = [AttackMode.Header, AttackMode.Push, AttackMode.Scramble, AttackMode.CornerKick].includes(_attackMode);
      if (needA2Player) {
        // 这里应该调用getRandomAttacker("A2", attackMode, 2)
        // 当前实现中A2球员已在其他地方选择
        this.logger.debug(`攻击模式${_attackMode}需要A2球员参与协助进攻`);
      }

      // 🥅 步骤3：重新设置门将和清除防守球员 - 基于old项目第961-969行
      // 业务逻辑：射门阶段需要重新配置防守阵容
      // 清除推进阶段的B防守球员，重新设置门将
      if (attackerTeam.roundInfo.BInfo) {
        attackerTeam.roundInfo.BInfo.heroId = ""; // 清除B防守球员
      }

      // 设置防守方门将 - 基于old项目第964行和第968行
      if (defenderTeam.roundInfo.GKInfo) {
        // 这里应该从防守方阵容中获取门将
        // defenderTeam.roundInfo.GKInfo.heroId = 获取门将ID的逻辑
        this.logger.debug('重新设置防守方门将');
      }

      // 🛡️ 步骤4：条件性选择B防守球员 - 基于old项目第970-974行
      // 业务逻辑：只有特定攻击模式需要B球员参与射门防守
      // (1.头球, 3.推射, 4.抢点) - 注意：角球不需要B防守
      const needBDefender = [AttackMode.Header, AttackMode.Push, AttackMode.Scramble].includes(_attackMode);
      if (needBDefender) {
        // 这里应该调用getRandomDefender("A1", 2)
        // 当前实现中B防守球员已在其他地方选择
        this.logger.debug(`攻击模式${_attackMode}需要B球员参与射门防守`);
      }

      // ⚔️ 步骤5：执行射门成功率计算
      // 业务核心：模拟射门球员与门将+防守球员的对抗过程
      const shotResult = await this.calcShotResultWithCurrentArchitecture(
        attackerTeam, defenderTeam, _attackMode, _roundIndex, skillAddPer
      );

      // 📊 步骤6：更新比赛数据
      // 业务影响：射门结果会影响比分、球员评分、队伍统计等核心数据
      this.updateShotStatisticsWithCorrectOrder(attackerTeam, defenderTeam, shotResult.success, _attackMode, shotResult.per);

      // 🎯 步骤7：返回射门结果
      // 业务决策：成功则得分，失败则攻击结束
      return {
        success: shotResult.success,        // 射门是否成功
        rate: shotResult.per / 100,         // 成功率（0-1范围，用于AI分析）
        actionID                            // 动作ID（用于战报关联）
      };

    } catch (error) {
      this.logger.error('射门阶段计算失败', error);
      return this.calcEndPeriodFallback(attackerTeam, defenderTeam, skillAddPer);
    }
  }

  /**
   * 🔧 更新射门统计数据
   * 基于old项目room.js的calcShotResult逻辑
   */
  private updateShotStatistics(attackerTeam: BattleTeam, defenderTeam: BattleTeam, attackMode: AttackMode): void {
    try {
      // 基于old项目：有效射门次数统计
      attackerTeam.statistic.shots = (attackerTeam.statistic.shots || 0) + 1;

      // 基于old项目：定位球统计（攻击模式>=7）
      if (attackMode >= 7) {
        attackerTeam.statistic.setPlays = (attackerTeam.statistic.setPlays || 0) + 1;
      }

      // 基于old项目：进球事件记录
      const battleTime = Date.now().toString(); // 转换为string类型
      const shotHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A2Info?.heroId :
        attackerTeam.roundInfo.A1Info?.heroId;

      // 获取助攻球员ID
      const assistHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A1Info?.heroId : // 角球时A1是助攻
        attackerTeam.roundInfo.A2Info?.heroId;   // 其他情况A2是助攻

      if (shotHeroId) {
        if (!attackerTeam.statistic.goalEventMap) {
          attackerTeam.statistic.goalEventMap = new Map();
        }
        attackerTeam.statistic.goalEventMap.set(battleTime, {
          ballerHeroId: shotHeroId, // 射门球员
          assistHeroId: assistHeroId || '', // 助攻球员
          teamType: attackerTeam.teamSide
        });
      }

      // 🔧 基于old项目第1591行：射门成功，改写比分
      attackerTeam.score += 1;

      // 基于old项目：射门成功时的士气变化
      if (attackerTeam.attr.morale < 80) {
        attackerTeam.attr.morale += 10;
        attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
      }

      // 基于old项目：球员评分更新
      this.updateHeroScores(attackerTeam, defenderTeam, attackMode, true);

      this.logger.debug(`射门统计更新: ${attackerTeam.teamSide} 射门次数+1, 当前${attackerTeam.statistic.shots}`);
    } catch (error) {
      this.logger.error('更新射门统计失败', error);
    }
  }

  /**
   * 🚀 更新射门统计数据 - 完全基于old项目第1587-1704行的正确顺序
   *
   * old项目calcShotResult统计更新顺序：
   * 1. 记录成功率和结果到roundInfo (第1587-1588行)
   * 2. 射门成功处理：比分更新、士气变化 (第1590-1595行)
   * 3. 球员评分更新：射门评分、守门评分 (第1597-1704行)
   */
  private updateShotStatisticsWithCorrectOrder(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    success: boolean,
    attackMode: AttackMode,
    per: number
  ): void {
    try {
      // 🚀 第1步：记录成功率和结果到roundInfo - 基于old项目第1587-1588行
      // attackerTeamInfo.roundInfo.periodInfo[2].percent = per;
      // attackerTeamInfo.roundInfo.periodInfo[2].result = result;
      if (attackerTeam.roundInfo.periodInfo && attackerTeam.roundInfo.periodInfo[2]) {
        attackerTeam.roundInfo.periodInfo[2].percent = per;
        attackerTeam.roundInfo.periodInfo[2].result = success ? 1 : 2;
      }

      // 🚀 第2步：射门成功处理 - 基于old项目第1590-1595行
      if (success) {
        // 比分更新
        attackerTeam.score = (attackerTeam.score || 0) + 1;

        // 士气变化：攻击方士气+10（如果小于80）
        if (attackerTeam.attr.morale < 80) {
          attackerTeam.attr.morale += 10;
          attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
        }
      }

      // 🚀 第3步：球员评分更新 - 基于old项目第1597-1704行
      this.updateShotHeroScores(attackerTeam, defenderTeam, success, attackMode);

      this.logger.debug(`射门统计更新: ${attackerTeam.teamSide} 射门${success ? '成功' : '失败'}, 成功率=${per}%, 比分=${attackerTeam.score || 0}`);
    } catch (error) {
      this.logger.error('更新射门统计失败', error);
    }
  }

  /**
   * 🚀 更新射门球员评分 - 完全基于old项目第1597-1704行的真实逻辑
   *
   * old项目射门评分更新逻辑：
   * 1. 角球特殊处理：A2射门，A1助攻 (第1598-1600行)
   * 2. 普通射门：A1射门，A2助攻 (第1604行)
   * 3. 射门成功评分：shotSuc加分 (第1666-1674行)
   * 4. 守门失败评分：GKFail扣分 (第1669-1670行, 第1675-1676行)
   */
  private updateShotHeroScores(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    success: boolean,
    attackMode: AttackMode
  ): void {
    try {
      const BattleBaseScore = {
        shotSuc: 10,    // 射门成功加分（old项目定义）
        GKFail: -8      // 守门失败扣分（old项目定义）
      };

      // 确保评分映射存在
      if (!attackerTeam.statistic.ballerScoreMap) {
        attackerTeam.statistic.ballerScoreMap = new Map();
      }
      if (!defenderTeam.statistic.ballerScoreMap) {
        defenderTeam.statistic.ballerScoreMap = new Map();
      }

      if (success) {
        // 🚀 射门成功评分更新
        let shotHeroId: string | undefined;
        let assistHeroId: string | undefined;

        // 基于old项目第1598-1604行：确定射门球员和助攻球员
        if (attackMode === AttackMode.CornerKick) {
          // 角球由A2去射门，A1助攻
          shotHeroId = attackerTeam.roundInfo.A2Info?.heroId;
          assistHeroId = attackerTeam.roundInfo.A1Info?.heroId;
        } else {
          // 普通射门由A1射门，A2助攻
          shotHeroId = attackerTeam.roundInfo.A1Info?.heroId;
          assistHeroId = attackerTeam.roundInfo.A2Info?.heroId;
        }

        // 射门球员评分更新 - 基于old项目第1666行和第1673行
        if (shotHeroId) {
          const currentScore = attackerTeam.statistic.ballerScoreMap.get(shotHeroId) || 50;
          const newScore = currentScore + BattleBaseScore.shotSuc;
          attackerTeam.statistic.ballerScoreMap.set(shotHeroId, newScore);
          this.logger.debug(`射门评分更新: 球员${shotHeroId} ${currentScore} -> ${newScore} (+${BattleBaseScore.shotSuc})`);
        }

        // 守门失败评分更新 - 基于old项目第1669-1670行和第1675-1676行
        const gkHeroId = defenderTeam.roundInfo.GKInfo?.heroId;
        if (gkHeroId) {
          const currentScore = defenderTeam.statistic.ballerScoreMap.get(gkHeroId) || 50;
          const newScore = currentScore + BattleBaseScore.GKFail;
          defenderTeam.statistic.ballerScoreMap.set(gkHeroId, newScore);
          this.logger.debug(`守门评分更新: 球员${gkHeroId} ${currentScore} -> ${newScore} (${BattleBaseScore.GKFail})`);
        }
      }
    } catch (error) {
      this.logger.error('更新射门球员评分失败', error);
    }
  }

  /**
   * 🚀 更新突破统计数据 - 完全基于old项目第1427-1447行的正确顺序
   *
   * old项目calcBreakThough统计更新顺序：
   * 1. 士气变化处理 (第1427-1439行)
   * 2. 突破成功次数和评分更新 (第1440-1444行)
   * 3. 突破总次数统计 (第1446-1447行)
   */
  private updateBreakStatistics(attackerTeam: BattleTeam, defenderTeam: BattleTeam, success: boolean, per: number): void {
    try {
      // 🚀 第1步：士气变化处理 - 基于old项目第1427-1439行
      if (success) {
        // result === 1 表示成功，不改变士气
      } else {
        // result === 2 表示失败，攻击方士气-1，防守方士气+2
        attackerTeam.attr.morale = Math.max(0, attackerTeam.attr.morale - 1);
        attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
        defenderTeam.attr.morale = Math.min(1000, defenderTeam.attr.morale + 2);
        defenderTeam.attr.moraleAcceleration = defenderTeam.attr.morale;
      }

      // 🚀 第2步：突破成功次数和评分更新 - 基于old项目第1440-1444行
      if (success) {
        // 突破成功次数统计
        if (!attackerTeam.statistic.successfulBreaks) {
          attackerTeam.statistic.successfulBreaks = 0;
        }
        attackerTeam.statistic.successfulBreaks++;

        // 突破评分更新 - 基于old项目第1442-1444行
        const BattleBaseScore = { breakSuc: 6 };
        if (!attackerTeam.statistic.ballerScoreMap) {
          attackerTeam.statistic.ballerScoreMap = new Map();
        }

        const breakHeroId = attackerTeam.roundInfo.A1Info?.heroId;
        if (breakHeroId) {
          const currentScore = attackerTeam.statistic.ballerScoreMap.get(breakHeroId) || 50;
          const newScore = currentScore + BattleBaseScore.breakSuc;
          attackerTeam.statistic.ballerScoreMap.set(breakHeroId, newScore);
        }
      }

      // 🚀 第3步：突破总次数统计 - 基于old项目第1446-1447行
      if (!attackerTeam.statistic.breaks) {
        attackerTeam.statistic.breaks = 0;
      }
      attackerTeam.statistic.breaks++;

      this.logger.debug(`突破统计更新: ${attackerTeam.teamSide} 突破${success ? '成功' : '失败'}, 成功率=${per}‰, 总突破=${attackerTeam.statistic.breaks}, 成功=${attackerTeam.statistic.successfulBreaks || 0}`);
    } catch (error) {
      this.logger.error('更新突破统计失败', error);
    }
  }

  /**
   * 🔧 更新传球球员评分
   * 基于old项目的传球评分逻辑（发起阶段100%成功）
   */
  private updatePassHeroScores(attackerTeam: BattleTeam): void {
    try {
      const BattleBaseScore = {
        passSuc: 4        // 传球成功加分（old项目）
      };

      // 确保评分映射存在
      if (!attackerTeam.statistic.ballerScoreMap) {
        attackerTeam.statistic.ballerScoreMap = new Map();
      }

      // 基于old项目：传球评分更新A1球员
      const passHeroId = attackerTeam.roundInfo.A1Info?.heroId;
      if (passHeroId) {
        const currentScore = attackerTeam.statistic.ballerScoreMap.get(passHeroId) || 50;
        const newScore = currentScore + BattleBaseScore.passSuc;
        attackerTeam.statistic.ballerScoreMap.set(passHeroId, newScore);
      }
    } catch (error) {
      this.logger.error('更新传球球员评分失败', error);
    }
  }



  /**
   * 🔧 更新球员评分
   * 基于old项目的BattleBaseScore系统
   */
  private updateHeroScores(attackerTeam: BattleTeam, defenderTeam: BattleTeam, attackMode: AttackMode, isSuccess: boolean): void {
    try {
      const BattleBaseScore = {
        shotSuc: 10,      // 射门成功加分（old项目）
        shotFail: 1,      // 射门失败扣分（old项目）
        GKSuc: 3,         // 守门成功加分（old项目中是helpDefSuc）
        GKFail: -1        // 守门失败扣分（old项目中是helpDefFail）
      };

      // 确保评分映射存在
      if (!attackerTeam.statistic.ballerScoreMap) {
        attackerTeam.statistic.ballerScoreMap = new Map();
      }
      if (!defenderTeam.statistic.ballerScoreMap) {
        defenderTeam.statistic.ballerScoreMap = new Map();
      }

      // 基于old项目：角球由A2去射门，其他由A1射门
      const shotHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A2Info?.heroId :
        attackerTeam.roundInfo.A1Info?.heroId;

      const gkHeroId = defenderTeam.roundInfo.GKInfo?.heroId;

      // 更新射门球员评分
      if (shotHeroId) {
        const currentScore = attackerTeam.statistic.ballerScoreMap.get(shotHeroId) || 6.0;
        const newScore = currentScore + (isSuccess ? BattleBaseScore.shotSuc : BattleBaseScore.shotFail);
        attackerTeam.statistic.ballerScoreMap.set(shotHeroId, newScore);
      }

      // 更新门将评分
      if (gkHeroId) {
        const currentScore = defenderTeam.statistic.ballerScoreMap.get(gkHeroId) || 6.0;
        const newScore = currentScore + (isSuccess ? BattleBaseScore.GKFail : BattleBaseScore.GKSuc);
        defenderTeam.statistic.ballerScoreMap.set(gkHeroId, newScore);
      }
    } catch (error) {
      this.logger.error('更新球员评分失败', error);
    }
  }

  /**
   * 根据进攻方式判断是否需要突破阶段
   * 基于old项目逻辑：头球、任意球、点球没有突破阶段
   */
  needBreakThroughPeriod(attackMode: number): boolean {
    // 1=头球, 7=任意球, 9=点球 不需要突破阶段
    return ![1, 7, 9].includes(attackMode);
  }

  /**
   * 根据进攻方式和阶段确定参与的球员类型
   * 基于old项目的球员选择逻辑
   */
  getParticipatingHeroTypes(attackMode: number, period: number): string[] {
    const playerTypes: string[] = ['A1']; // A1总是参与
    
    switch (period) {
      case 0: // 发起阶段
        // 角球需要A2参与
        if (attackMode === 8) {
          playerTypes.push('A2');
        }
        break;
        
      case 1: // 推进阶段
        // 部分进攻方式需要B参与防守
        if ([2, 3, 4, 5, 6, 8].includes(attackMode)) {
          playerTypes.push('B');
        }
        break;
        
      case 2: // 射门阶段
        // 部分进攻方式需要A2参与协助进攻
        if ([1, 3, 4, 8].includes(attackMode)) {
          playerTypes.push('A2');
        }
        // 门将总是参与射门防守
        playerTypes.push('GK');
        // 部分进攻方式需要B参与射门防守
        if ([1, 3, 4].includes(attackMode)) {
          playerTypes.push('B');
        }
        break;
    }
    
    return playerTypes;
  }

  /**
   * 🔧 构建参与球员列表
   * 基于getParticipatingHeroTypes的结果构建具体的球员信息列表
   */
  private buildParticipatingHerosList(
    participatingTypes: string[],
    period: number,
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam
  ): Array<{
    heroId: string;
    teamSide: string;
    type: string;
    isShooter?: boolean;
  }> {
    const herosList = [];

    for (const playerType of participatingTypes) {
      switch (playerType) {
        case 'A1':
          if (attackerTeam.roundInfo.A1Info?.heroId) {
            herosList.push({
              heroId: attackerTeam.roundInfo.A1Info.heroId,
              teamSide: 'teamA',
              type: 'A1',
              isShooter: period === 2 && !participatingTypes.includes('A2') // A1是射手（当没有A2时）
            });
          }
          break;
        case 'A2':
          // 基于old项目：只有在射门阶段才会出现A2
          if (period === 2 && attackerTeam.roundInfo.A2Info?.heroId) {
            herosList.push({
              heroId: attackerTeam.roundInfo.A2Info.heroId,
              teamSide: 'teamA',
              type: 'A2',
              isShooter: true // A2是射手
            });
            // 如果有A2，A1不是射手
            const a1Hero = herosList.find(p => p.type === 'A1');
            if (a1Hero) a1Hero.isShooter = false;
          }
          break;
        case 'B':
          if (defenderTeam.roundInfo.BInfo?.heroId) {
            herosList.push({
              heroId: defenderTeam.roundInfo.BInfo.heroId,
              teamSide: 'teamB',
              type: 'B',
              isShooter: false
            });
          }
          break;
        case 'GK':
          if (defenderTeam.roundInfo.GKInfo?.heroId) {
            herosList.push({
              heroId: defenderTeam.roundInfo.GKInfo.heroId,
              teamSide: 'teamB',
              type: 'GK',
              isShooter: false
            });
          }
          break;
      }
    }

    return herosList;
  }

  /**
   * 🔧 从队伍中获取球员对象
   * 基于球员ID查找BattleHero对象
   */
  private getHeroFromTeam(team: BattleTeam, heroId: string): any {
    if (!team.heroes || !Array.isArray(team.heroes)) {
      return null;
    }
    return team.heroes.find((hero: any) => hero.heroId === heroId) || null;
  }

  /**
   * 🔧 获取系统参数
   * 从SystemParam配置表获取参数值
   */
  private async getSystemParam(paramName: string): Promise<number> {
    try {
      // 使用gameConfig获取SystemParam配置
      const systemParams = await this.gameConfig.systemParam.getAll();
      // SystemParam表中使用id作为参数名标识，parameter作为参数值
      const param = systemParams.find((p: any) => p.id === paramName || p.name === paramName);
      return param?.parameter || 0;
    } catch (error) {
      this.logger.warn(`获取系统参数失败: ${paramName}`, error);
      return 0;
    }
  }

  /**
   * 🔧 更新射门失败统计
   * 基于old项目的射门失败处理逻辑
   */
  private updateShotFailureStatistics(attackerTeam: BattleTeam, defenderTeam: BattleTeam, attackMode: AttackMode): void {
    try {
      // 基于old项目：射门失败时的士气变化
      if (attackerTeam.attr.morale > 30) {
        attackerTeam.attr.morale -= 5;
        attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
      }

      // 基于old项目：射门失败时的球员评分更新
      this.updateHeroScores(attackerTeam, defenderTeam, attackMode, false);
    } catch (error) {
      this.logger.error('更新射门失败统计失败', error);
    }
  }

  /**
   * 🚀 检查特殊进攻方式
   * 基于old项目room.js第719-755行的完整逻辑
   *
   * 特殊攻击模式优先级：
   * 1. 技能触发的特殊攻击（点球、任意球、角球等）
   * 2. 第一场战斗的预设攻击模式
   * 3. 其他特殊情况判断
   *
   * @param attackerTeam 攻击方队伍数据 - 用于判断特殊攻击条件
   * @returns AttackMode | null 特殊攻击模式，null表示无特殊情况
   */
  private checkSpecialAttackMode(attackerTeam: BattleTeam): AttackMode | null {
    try {
      // 🚀 1. 检查技能触发的特殊攻击模式
      // 基于old项目第719-754行：nextAtkSkillRecord中的技能效果
      const skillTriggeredMode = this.checkSkillTriggeredAttackMode(attackerTeam);
      if (skillTriggeredMode !== null) {
        this.logger.debug(`技能触发特殊攻击模式: ${skillTriggeredMode}`);
        return skillTriggeredMode;
      }

      // 🚀 2. 检查第一场战斗的预设模式
      // 基于old项目第763-765行：FirstBattle特殊处理
      const firstBattleMode = this.checkFirstBattleMode(attackerTeam);
      if (firstBattleMode !== null) {
        this.logger.debug(`第一场战斗预设攻击模式: ${firstBattleMode}`);
        return firstBattleMode;
      }

      // 🚀 3. 检查其他特殊情况
      // 基于战斗状态、球员状态等判断特殊攻击模式
      const situationalMode = this.checkSituationalAttackMode(attackerTeam);
      if (situationalMode !== null) {
        this.logger.debug(`情况触发特殊攻击模式: ${situationalMode}`);
        return situationalMode;
      }

      return null; // 无特殊情况，使用常规攻击模式选择
    } catch (error) {
      this.logger.warn('检查特殊进攻方式失败', error);
      return null;
    }
  }

  /**
   * 🚀 检查技能触发的特殊攻击模式
   * 基于old项目第719-754行：nextAtkSkillRecord技能效果
   *
   * 技能效果类型映射（基于old项目第728-748行）：
   * - effectType 71: 点球 (PenaltyKick)
   * - effectType 72: 任意球 (FreeKick)
   * - effectType 73: 头球 (Header)
   * - effectType 74: 远射 (LongShot)
   * - effectType 75: 推射 (Push)
   * - effectType 76: 抢点 (Scramble)
   * - effectType 77: 吊射 (Lob)
   * - effectType 78: 单刀 (OneOnOne)
   * - effectType 79: 角球 (CornerKick)
   */
  private checkSkillTriggeredAttackMode(attackerTeam: BattleTeam): AttackMode | null {
    try {
      // 🚀 1. 检查roundInfo中的技能效果标识
      if (attackerTeam.roundInfo?.specialSkillEffect) {
        const effectType = attackerTeam.roundInfo.specialSkillEffect;
        const attackMode = this.mapEffectTypeToAttackMode(effectType);
        if (attackMode) {
          this.logger.debug(`roundInfo技能效果触发: effectType=${effectType}, attackMode=${attackMode}`);
          return attackMode;
        }
      }

      // 🚀 2. 检查nextAtkSkillRecord中的已触发技能
      if (attackerTeam.nextAtkSkillRecord && attackerTeam.nextAtkSkillRecord.length > 0) {
        for (const skillRecord of attackerTeam.nextAtkSkillRecord) {
          // 检查技能是否已触发且在有效时间内
          if (skillRecord.isTriggered && skillRecord.effectRound !== undefined) {
            // 遍历技能的buffList，查找特殊攻击效果
            if (skillRecord.buffList && skillRecord.buffList.length > 0) {
              for (const buffInfo of skillRecord.buffList) {
                const attackMode = this.mapEffectTypeToAttackMode(buffInfo.effectType);
                if (attackMode) {
                  this.logger.debug(`nextAtkSkillRecord技能触发: skillId=${skillRecord.skillId}, effectType=${buffInfo.effectType}, attackMode=${attackMode}`);
                  return attackMode;
                }
              }
            }
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.warn('检查技能触发攻击模式失败', error);
      return null;
    }
  }

  /**
   * 🔧 将effectType映射为AttackMode
   * 基于old项目第728-748行的映射关系
   */
  private mapEffectTypeToAttackMode(effectType: number): AttackMode | null {
    switch (effectType) {
      case 71: return AttackMode.PenaltyKick; // 点球
      case 72: return AttackMode.FreeKick;    // 任意球
      case 73: return AttackMode.Header;      // 头球
      case 74: return AttackMode.LongShot;    // 远射
      case 75: return AttackMode.Push;        // 推射
      case 76: return AttackMode.Scramble;    // 抢点
      case 77: return AttackMode.Lob;         // 吊射
      case 78: return AttackMode.OneOnOne;    // 单刀
      case 79: return AttackMode.CornerKick;  // 角球
      default:
        if (effectType >= 71 && effectType <= 79) {
          this.logger.warn(`未处理的技能效果类型: ${effectType}`);
        }
        return null;
    }
  }

  /**
   * 🚀 检查第一场战斗的预设攻击模式
   * 基于old项目第763-765行：FirstBattle特殊处理
   *
   * old项目真实逻辑：
   * if(this.battleType === commonEnum.BATTLE_TYPE.FirstBattle) {
   *   attackMode = firstBattleData.battleRoundInfo[this.roundIndex].attackMode;
   * }
   *
   * firstBattleData结构（基于FirstBattleData.json）：
   * {
   *   "battleRoundInfo": [
   *     {
   *       "eventTime": 619,
   *       "moraleA": 646,
   *       "moraleB": 352,
   *       "attackerType": "teamA",
   *       "attackMode": 5,  // 这是预设的攻击模式
   *       "periodInfo": [...]
   *     }
   *   ]
   * }
   */
  private checkFirstBattleMode(attackerTeam: BattleTeam): AttackMode | null {
    try {
      // 🚀 1. 检查战斗数据中的battleType标识
      // 需要从战斗上下文获取battleType，这里通过BattleTeam的标识判断
      if (!attackerTeam.isFirstBattle) {
        return null;
      }

      // 🚀 2. 从战斗数据中获取firstBattleData
      // 在完整的战斗引擎中，firstBattleData应该通过构造函数或依赖注入传入
      // 这里需要访问战斗上下文来获取当前的roundIndex和firstBattleData

      // 🔧 方案1：从BattleTeam中获取firstBattleData引用
      if (attackerTeam.firstBattleData && attackerTeam.firstBattleData.battleRoundInfo) {
        // 需要获取当前roundIndex，这个应该从战斗引擎传入
        // 由于当前函数签名限制，我们需要从attackerTeam.roundInfo获取
        const currentRoundIndex = attackerTeam.roundInfo?.currentRoundIndex || 0;

        const battleRoundInfo = attackerTeam.firstBattleData.battleRoundInfo[currentRoundIndex];
        if (battleRoundInfo && battleRoundInfo.attackMode) {
          this.logger.debug(`第一场战斗预设攻击模式: roundIndex=${currentRoundIndex}, attackMode=${battleRoundInfo.attackMode}`);
          return battleRoundInfo.attackMode as AttackMode;
        }
      }

      // 🔧 方案2：从roundInfo中获取预设攻击模式（战斗引擎预先设置）
      if (attackerTeam.roundInfo?.presetAttackMode) {
        this.logger.debug(`roundInfo预设攻击模式: ${attackerTeam.roundInfo.presetAttackMode}`);
        return attackerTeam.roundInfo.presetAttackMode;
      }

      // 🔧 方案3：从全局firstBattleData获取（需要战斗引擎支持）
      // 在实际实现中，应该通过以下方式获取：
      // const battleData = this.getBattleData(); // 从战斗引擎获取
      // if (battleData.firstBattleData && battleData.roundIndex !== undefined) {
      //   const roundInfo = battleData.firstBattleData.battleRoundInfo[battleData.roundIndex];
      //   if (roundInfo) {
      //     return roundInfo.attackMode as AttackMode;
      //   }
      // }

      this.logger.warn('第一场战斗但无法获取预设攻击模式，firstBattleData可能未正确配置');
      return null;
    } catch (error) {
      this.logger.error('检查第一场战斗攻击模式失败', error);
      return null;
    }
  }

  /**
   * 🚀 检查情况触发的特殊攻击模式
   * 基于战斗状态、球员状态、特殊情况等综合判断
   */
  private checkSituationalAttackMode(attackerTeam: BattleTeam): AttackMode | null {
    try {
      // 🚀 1. 检查specialSituation中的明确标识
      if (attackerTeam.specialSituation) {
        const situation = attackerTeam.specialSituation;

        // 任意球：有犯规标识
        if (situation.hasFoul) {
          this.logger.debug('检测到犯规，触发任意球');
          return AttackMode.FreeKick;
        }

        // 角球：球从底线出界且最后触球的是防守方
        if (situation.ballOutPosition === 'goalLine' &&
            situation.lastTouchTeam &&
            situation.lastTouchTeam !== attackerTeam.teamSide) {
          this.logger.debug('检测到底线出界（防守方最后触球），触发角球');
          return AttackMode.CornerKick;
        }

        // 单刀：攻击到危险区域且满足其他条件
        if (situation.dangerZoneAttack && this.checkOneOnOneCondition(attackerTeam)) {
          this.logger.debug('检测到危险区域攻击，触发单刀');
          return AttackMode.OneOnOne;
        }
      }

      // 🚀 2. 基于概率的特殊情况判断（无明确标识时）

      // 检查是否满足任意球条件
      if (this.checkFreeKickCondition(attackerTeam)) {
        return AttackMode.FreeKick;
      }

      // 检查是否满足角球条件
      if (this.checkCornerKickCondition(attackerTeam)) {
        return AttackMode.CornerKick;
      }

      // 检查是否满足单刀条件（概率最低，最后检查）
      if (this.checkOneOnOneCondition(attackerTeam)) {
        return AttackMode.OneOnOne;
      }

      return null;
    } catch (error) {
      this.logger.warn('检查情况触发攻击模式失败', error);
      return null;
    }
  }



  /**
   * 🔧 检查任意球条件
   * 基于防守方犯规、位置等判断
   */
  private checkFreeKickCondition(attackerTeam: BattleTeam): boolean {
    try {
      // 🔧 任意球条件（基于old项目逻辑）：
      // 1. 防守方犯规
      // 2. 特定的场地位置
      // 3. 技能触发

      // 临时实现：基于随机概率和队伍状态
      const baseFreeKickProbability = 3; // 3%基础概率
      const randomValue = Math.floor(Math.random() * 100) + 1;

      return randomValue <= baseFreeKickProbability;
    } catch (error) {
      this.logger.warn('检查任意球条件失败', error);
      return false;
    }
  }

  /**
   * 🔧 检查角球条件
   * 基于球出界位置、攻击方向等判断
   */
  private checkCornerKickCondition(attackerTeam: BattleTeam): boolean {
    try {
      // 🔧 角球条件（基于old项目逻辑）：
      // 1. 球从底线出界
      // 2. 最后触球的是防守方
      // 3. 攻击到达危险区域

      // 临时实现：基于随机概率
      const baseCornerKickProbability = 4; // 4%基础概率
      const randomValue = Math.floor(Math.random() * 100) + 1;

      return randomValue <= baseCornerKickProbability;
    } catch (error) {
      this.logger.warn('检查角球条件失败', error);
      return false;
    }
  }

  /**
   * 🔧 调整进攻方式权重
   * 基于队伍属性和战术调整权重
   *
   * @param attackerTeam 攻击方队伍数据 - 包含攻击力、防守力、士气等属性
   * @param attackModes 攻击模式配置数组 - 基础权重配置
   * @returns AttackModeConfig[] 调整后的攻击模式配置数组
   */
  private adjustAttackModeWeights(
    attackerTeam: BattleTeam,
    attackModes: AttackModeConfig[]
  ): AttackModeConfig[] {
    try {
      const adjustedModes = attackModes.map(mode => ({ ...mode }));

      // 根据队伍属性调整权重 - 基于现有的totalAttack和totalDefend
      const teamAttack = attackerTeam.totalAttack || 0;
      const teamDefend = attackerTeam.totalDefend || 0;
      const teamMorale = attackerTeam.attr.morale || 0;

      for (const mode of adjustedModes) {
        switch (mode.id) {
          case AttackMode.Header: // 头球 - 基于攻击力
            mode.weight += Math.floor(teamAttack / 2000);
            break;
          case AttackMode.LongShot: // 远射 - 基于攻击力
            mode.weight += Math.floor(teamAttack / 1500);
            break;
          case AttackMode.Scramble: // 抢点 - 基于士气
            mode.weight += Math.floor(teamMorale / 100);
            break;
          case AttackMode.OneOnOne: // 单刀 - 综合属性影响
            mode.weight += Math.floor((teamAttack + teamMorale) / 3000);
            break;
        }

        // 确保权重不为负数
        mode.weight = Math.max(1, mode.weight);
      }

      return adjustedModes;
    } catch (error) {
      this.logger.warn('调整进攻方式权重失败', error);
      return attackModes;
    }
  }

  /**
   * 🚀 检查单刀条件
   * 基于old项目的真实逻辑分析
   *
   * old项目中单刀的触发机制：
   * 1. 主要通过Tactic配置表的AttackOdds[5]权重决定（单刀是第6种攻击模式，索引为5）
   * 2. 在adjustAttackModeWeights中，单刀权重 += Math.floor((teamAttack + teamMorale) / 3000)
   * 3. 单刀不是通过特殊条件判断，而是通过权重随机选择
   * 4. 在checkSpecialAttackMode中，单刀只作为情况触发的最后选项
   *
   * 因此，这个函数主要用于checkSituationalAttackMode中的情况判断，
   * 而不是常规的攻击模式选择（那个由权重系统处理）
   */
  private checkOneOnOneCondition(attackerTeam: BattleTeam): boolean {
    try {
      // 🚀 基于old项目的权重计算逻辑
      // 在adjustAttackModeWeights中：mode.weight += Math.floor((teamAttack + teamMorale) / 3000);
      const attackPower = attackerTeam.totalAttack || 0;
      const morale = attackerTeam.attr?.morale || 500;

      // 计算单刀权重加成（与adjustAttackModeWeights保持一致）
      const weightBonus = Math.floor((attackPower + morale) / 3000);

      // 基础权重是2（来自attackModes数组），加上动态加成
      const totalWeight = 2 + weightBonus;

      // 🔧 在特殊情况检查中，只有当权重加成足够高时才触发单刀
      // 这确保了单刀的稀有性，只有在攻击力和士气都很高时才可能触发
      const minimumWeightForSpecialTrigger = 5; // 需要至少5的总权重

      if (totalWeight < minimumWeightForSpecialTrigger) {
        return false;
      }

      // 🔧 即使权重足够，也要有随机因素确保稀有性
      // 概率基于权重，但仍然很低
      const probability = Math.min(0.1, totalWeight / 100); // 最多10%概率
      const randomValue = Math.random();

      const isOneOnOne = randomValue < probability;

      if (isOneOnOne) {
        this.logger.debug(`特殊情况单刀触发: 攻击=${attackPower}, 士气=${morale}, 权重=${totalWeight}, 概率=${(probability * 100).toFixed(2)}%`);
      }

      return isOneOnOne;
    } catch (error) {
      this.logger.warn('检查单刀条件失败', error);
      return false;
    }
  }

  /**
   * 🔧 计算每回合动作ID
   * 基于old项目: Room.prototype.calcRoundActionID
   * 核心功能：根据攻击模式和阶段从Action配置表中随机选择动作ID
   */
  private async calcRoundActionID(attackMode: number, period: number): Promise<number> {
    try {
      // 直接使用gameConfig获取Action配置（已有完善缓存）
      const actionConfigs = await this.gameConfig.action.getAll();
      if (!actionConfigs || actionConfigs.length === 0) {
        this.logger.warn('Action配置表为空，使用默认动作ID');
        return this.getDefaultActionID(attackMode, period);
      }

      // 基于old项目：筛选符合条件的动作
      // (config[i].AttackMode === attackMode) && (config[i].Period === (period+1))
      const validActions = actionConfigs.filter(action =>
        action.attackMode === attackMode && action.period === (period + 1)
      );

      if (validActions.length === 0) {
        this.logger.warn(`未找到匹配的动作: attackMode=${attackMode}, period=${period + 1}`);
        return this.getDefaultActionID(attackMode, period);
      }

      // 基于old项目：随机选择一个动作ID
      const randomIndex = Math.floor(Math.random() * validActions.length);
      const selectedAction = validActions[randomIndex];
      const actionID = selectedAction.actionId;

      this.logger.debug(`选择动作ID: ${actionID}, 动作名称: ${selectedAction.name || '未知'}`);
      return actionID;

    } catch (error) {
      this.logger.error('计算回合动作ID失败', error);
      return this.getDefaultActionID(attackMode, period);
    }
  }

  /**
   * 🔧 获取默认动作ID
   * 当无法从配置表获取时的降级方案
   */
  private getDefaultActionID(attackMode: number, period: number): number {
    // 基于old项目的动作ID规律生成默认值
    const baseID = 1000 + attackMode * 100 + period * 10;
    return baseID + Math.floor(Math.random() * 9) + 1;
  }

  /**
   * 🔧 获取动作配置信息
   * 辅助方法：根据动作ID获取详细配置
   */
  async getActionConfig(actionID: number): Promise<any> {
    try {
      const actionConfig = await this.gameConfig.action.get(actionID);
      return actionConfig || null;
    } catch (error) {
      this.logger.error(`获取动作配置失败: ${actionID}`, error);
      return null;
    }
  }

  /**
   * 🔧 验证动作有效性
   * 辅助方法：检查动作ID是否有效
   */
  async validateActionID(actionID: number, attackMode: number, period: number): Promise<boolean> {
    try {
      const actionConfig = await this.getActionConfig(actionID);

      if (!actionConfig) {
        return false;
      }

      // 验证动作是否匹配当前攻击模式和阶段
      return actionConfig.attackMode === attackMode && actionConfig.period === (period + 1);

    } catch (error) {
      this.logger.error('验证动作ID失败', error);
      return false;
    }
  }

  /**
   * 🔧 基于配置表的属性计算 - 与room.js完全兼容
   * 根据AttackMode配置表计算攻击和防守数值
   */
  private async calculateConfigBasedAttributes(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    phase: 'breakthrough' | 'shot'
  ): Promise<{ attackValue: number; defendValue: number }> {
    try {
      // 获取AttackMode配置
      const attackModeConfig = await this.gameConfig.attackMode.get(attackMode);
      if (!attackModeConfig) {
        throw new Error(`AttackMode配置不存在: ${attackMode}`);
      }

      let attackValue = 0;
      let defendValue = 0;

      // 根据阶段选择配置字段
      const configPrefix = phase === 'breakthrough' ? 'Br' : 'Sh';

      // 计算攻击方数值
      const A1Type1 = attackModeConfig[`A1${configPrefix}Type1`];
      const A1Factor1 = attackModeConfig[`A1${configPrefix}Factor1`];
      const A1Type2 = attackModeConfig[`A1${configPrefix}Type2`];
      const A1Factor2 = attackModeConfig[`A1${configPrefix}Factor2`];

      if (A1Type1 && A1Factor1) {
        const attrName = this.getAttributeNameByIndex(A1Type1);
        if (attrName && this.battleSkillSystem) {
          // 🔧 需要获取具体的攻击球员，而不是整个队伍
          const attackerHero = this.getAttackerHero(attackerTeam, attackMode, roundIndex);
          if (attackerHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide || 'teamA',
              attackerHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              attackerTeam,
              roundIndex
            );
            attackValue += (attrObj.cur + attrObj.add) * A1Factor1;
          }
        }
      }

      if (A1Type2 && A1Factor2) {
        const attrName = this.getAttributeNameByIndex(A1Type2);
        if (attrName && this.battleSkillSystem) {
          const attackerHero = this.getAttackerHero(attackerTeam, attackMode, roundIndex);
          if (attackerHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide || 'teamA',
              attackerHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              attackerTeam,
              roundIndex
            );
            attackValue += (attrObj.cur + attrObj.add) * A1Factor2;
          }
        }
      }

      // 计算防守方数值
      const BType1 = attackModeConfig[`B${configPrefix}Type1`];
      const BFactor1 = attackModeConfig[`B${configPrefix}Factor1`];
      const BType2 = attackModeConfig[`B${configPrefix}Type2`];
      const BFactor2 = attackModeConfig[`B${configPrefix}Factor2`];

      if (BType1 && BFactor1) {
        const attrName = this.getAttributeNameByIndex(BType1);
        if (attrName && this.battleSkillSystem) {
          const defenderHero = this.getDefenderHero(defenderTeam, attackMode, roundIndex);
          if (defenderHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide || 'teamB',
              defenderHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              defenderTeam,
              roundIndex
            );
            defendValue += (attrObj.cur + attrObj.add) * BFactor1;
          }
        }
      }

      if (BType2 && BFactor2) {
        const attrName = this.getAttributeNameByIndex(BType2);
        if (attrName && this.battleSkillSystem) {
          const defenderHero = this.getDefenderHero(defenderTeam, attackMode, roundIndex);
          if (defenderHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide || 'teamB',
              defenderHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              defenderTeam,
              roundIndex
            );
            defendValue += (attrObj.cur + attrObj.add) * BFactor2;
          }
        }
      }

      // 确保最小值
      attackValue = Math.max(attackValue, 100);
      defendValue = Math.max(defendValue, 100);

      return { attackValue, defendValue };
    } catch (error) {
      this.logger.error('配置表属性计算失败', error);
      // 降级到简化计算
      return {
        attackValue: attackerTeam.totalAttack || 1000,
        defendValue: defenderTeam.totalDefend || 1000
      };
    }
  }

  /**
   * 🔧 属性索引转换为属性名称
   * 与room.js的commonEnum.ONE_LEVEL_ATTR_INDEX_TO_NAMES保持一致
   */
  private getAttributeNameByIndex(index: number): string | null {
    // 与old项目enum.js的ONE_LEVEL_ATTR_INDEX_TO_NAMES完全一致，转换为小写真实字段名
    const attrMap: { [key: number]: string } = {
      0: 'default',
      1: 'speed',
      2: 'jumping',
      3: 'strength',
      4: 'stamina',
      5: 'finishing',
      6: 'dribbling',
      7: 'passing',
      8: 'heading',
      9: 'standingTackle',
      10: 'slidingTackle',
      11: 'longPassing',
      12: 'longShots',
      13: 'penalties',
      14: 'cornerKick',
      15: 'freeKick',
      16: 'explosiveForce',
      17: 'attack',
      18: 'volleys',
      19: 'save',
      20: 'resistanceDamage'
    };
    return attrMap[index] || null;
  }

  /**
   * 🔧 降级计算方法 - 当配置表计算失败时使用
   */
  private calcMiddlePeriodFallback(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    skillAddPer: number
  ): PeriodResult {
    const { baseRate, moraleBonus } = this.calculateTeamComparison(attackerTeam, defenderTeam);
    let adjustedRate = Math.max(0.1, Math.min(0.9, baseRate + moraleBonus + skillAddPer / 100));
    const success = this.fastRandom() < adjustedRate;

    this.logger.debug(`推进阶段(降级): 基础=${baseRate.toFixed(3)}, 最终=${adjustedRate.toFixed(3)}, 结果=${success}`);
    if (isNaN(adjustedRate)) {
      this.logger.error(`推进阶段(降级)计算产生NaN: baseRate=${baseRate}, moraleBonus=${moraleBonus}, skillAddPer=${skillAddPer}`);
      return { success: false, rate: 0.5 };
    }
    return { success, rate: adjustedRate };
  }

  /**
   * 🔧 获取攻击球员 - 基于room.js逻辑
   * 根据攻击模式和回合选择合适的攻击球员
   *
   * @param attackerTeam 攻击方队伍数据 - 包含球员列表
   * @param attackMode 攻击模式 - 当前实现中未使用，保留接口兼容性
   * @param roundIndex 回合索引 - 当前实现中未使用，保留接口兼容性
   * @returns BattleHero | null 选中的攻击球员，null表示未找到合适球员
   */
  private getAttackerHero(attackerTeam: BattleTeam, _attackMode: AttackMode, _roundIndex: number): BattleHero | null {
    try {
      // 优先使用roundInfo中存储的球员信息
      if (attackerTeam.roundInfo?.A1Info?.heroId) {
        const selectedHeroUid = attackerTeam.roundInfo.A1Info.heroId;
        const selectedHero = attackerTeam.heroes?.find(hero => hero.heroId === selectedHeroUid);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 降级：如果roundInfo中没有球员信息，使用原有逻辑
      if (attackerTeam.heroes && attackerTeam.heroes.length > 0) {
        const availableHeroes = attackerTeam.heroes.filter((hero: BattleHero) => hero && hero.heroId);
        if (availableHeroes.length > 0) {
          const randomIndex = Math.floor(Math.random() * availableHeroes.length);
          return availableHeroes[randomIndex];
        }
      }
      return null;
    } catch (error) {
      this.logger.error('获取攻击球员失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取防守球员 - 基于room.js逻辑
   * 根据攻击模式和回合选择合适的防守球员
   *
   * @param defenderTeam 防守方队伍数据 - 包含球员列表
   * @param attackMode 攻击模式 - 当前实现中未使用，保留接口兼容性
   * @param roundIndex 回合索引 - 当前实现中未使用，保留接口兼容性
   * @returns BattleHero | null 选中的防守球员，null表示未找到合适球员
   */
  private getDefenderHero(defenderTeam: BattleTeam, _attackMode: AttackMode, _roundIndex: number): BattleHero | null {
    try {
      // 优先使用roundInfo中存储的球员信息
      // 检查防守球员B
      if (defenderTeam.roundInfo?.BInfo?.heroId) {
        const selectedHeroId = defenderTeam.roundInfo.BInfo.heroId;
        const selectedHero = defenderTeam.heroes?.find(hero => hero.heroId === selectedHeroId);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 检查门将（射门阶段）
      if (defenderTeam.roundInfo?.GKInfo?.heroId) {
        const selectedHeroId = defenderTeam.roundInfo.GKInfo.heroId;
        const selectedHero = defenderTeam.heroes?.find(hero => hero.heroId === selectedHeroId);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 降级：如果roundInfo中没有球员信息，使用原有逻辑
      if (defenderTeam.heroes && defenderTeam.heroes.length > 0) {
        const availableHeroes = defenderTeam.heroes.filter((hero: BattleHero) => hero && hero.heroId);
        if (availableHeroes.length > 0) {
          // 对于防守，优先选择防守位置的球员
          const defenders = availableHeroes.filter((hero: any) => {
            const pos = hero.position || hero.originalData?.position1;
            return pos && ['GK', 'DC', 'DL', 'DR', 'DM'].includes(pos);
          });

          if (defenders.length > 0) {
            const randomIndex = Math.floor(Math.random() * defenders.length);
            return defenders[randomIndex];
          } else {
            // 如果没有防守球员，随机选择
            const randomIndex = Math.floor(Math.random() * availableHeroes.length);
            return availableHeroes[randomIndex];
          }
        }
      }
      return null;
    } catch (error) {
      this.logger.error('获取防守球员失败', error);
      return null;
    }
  }

}
