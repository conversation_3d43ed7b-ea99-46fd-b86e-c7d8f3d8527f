import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleData } from '../types/battle-data.types';
import { BattleType } from '@libs/game-constants';

/**
 * 🏆 战斗奖励计算器
 * 
 * 职责：
 * - 计算各种战斗类型的奖励数值
 * - 基于old项目的奖励计算逻辑
 * - 支持联赛副本、商业赛、通用奖励等
 * 
 * 特性：
 * - 纯计算逻辑：不涉及数据持久化或服务调用
 * - 配置驱动：基于GameConfig的奖励配置
 * - 错误容错：计算失败时返回默认奖励
 * - 日志记录：详细的奖励计算日志
 * 
 * 架构设计：
 * - 作为Calculator：专注于数值计算，不处理业务流程
 * - 无状态设计：所有方法都是纯函数式计算
 * - 可测试性：独立的奖励计算逻辑
 * - 可复用性：可被多个服务调用
 */
export class BattleRewardCalculator {
  private readonly logger = new Logger(BattleRewardCalculator.name);

  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 🔧 计算战斗奖励
   * 基于old项目: battleEndInfoJsonToClient中的奖励计算逻辑
   * 统一入口，根据战斗类型分发到具体的奖励计算方法
   *
   * @param battleData 战斗数据 - 包含战斗类型、队伍信息等
   * @param teamAScore 队伍A得分 - 用于判断胜负
   * @param teamBScore 队伍B得分 - 用于判断胜负
   * @returns Promise<奖励计算结果> 包含成功状态、奖励数据和错误信息
   */
  async calculateBattleRewards(
    battleData: BattleData,
    teamAScore: number,
    teamBScore: number
  ): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      const isWinner = teamAScore > teamBScore;
      const battleType = battleData.battleType;
      const rewards: any = {
        lootItemList: [],
        businessReward: {},
        generalRewards: {}
      };

      this.logger.debug(`开始计算战斗奖励: ${battleType}, 获胜方: ${isWinner ? 'A' : 'B'}`);

      // 🔧 联赛副本奖励（基于old项目逻辑）
      if (battleType === BattleType.PVE_LEAGUE) {
        rewards.lootItemList = await this.calculateLeagueCopyReward(battleData);
      }

      // 🔧 商业赛奖励（基于old项目逻辑）
      if (battleType === BattleType.PVP_BUSINESS) {
        rewards.businessReward = await this.calculateBusinessReward(battleData, teamAScore, teamBScore);
      }

      // 🔧 通用奖励计算（适用于所有战斗类型）
      if (this.shouldCalculateGeneralRewards(battleType)) {
        rewards.generalRewards = await this.calculateGeneralRewards(battleData, isWinner);
      }

      this.logger.debug(`战斗奖励计算完成: ${battleType}`);
      return { success: true, data: rewards };
    } catch (error) {
      this.logger.error('计算战斗奖励失败', error);
      return { success: false, message: '计算战斗奖励失败' };
    }
  }

  /**
   * 🔧 计算联赛副本奖励
   * 基于old项目: Room.prototype.getLeagueCopyReward
   *
   * @param battleData 战斗数据 - 包含联赛副本相关信息
   * @returns Promise<any[]> 联赛副本奖励列表
   */
  async calculateLeagueCopyReward(battleData: any): Promise<any[]> {
    try {
      const reward = [];
      
      if (battleData.battleType !== 'PveLeagueCopy') {
        return reward;
      }

      const teamInfo = battleData.teamB;
      let finishedStar = battleData.teamA.statistic?.shotNum - battleData.teamB.statistic?.shotNum;
      
      if (finishedStar > 3) finishedStar = 3;
      if (finishedStar < 0) finishedStar = 0;

      // 从配置表获取奖励配置
      const config = await this.getPveCommonConfig(battleData.battleType, teamInfo.teamResId);
      if (!config) {
        this.logger.warn(`获取联赛副本配置失败: ${battleData.battleType}, ${teamInfo.teamResId}`);
        return reward;
      }

      if (finishedStar > (teamInfo.takeCopyRewardProcess || 0)) {
        let totalExp = 0;
        
        for (let i = (teamInfo.takeCopyRewardProcess || 0) + 1; i <= finishedStar; i++) {
          const lootItem: any = {
            resId: config[`Reward${i}`],
            num: config[`RewardNum${i}`]
          };
          
          totalExp += config[`Exp${i}`] || 0;
          
          if (lootItem.resId > 0 && lootItem.num > 0) {
            reward.push(lootItem);
          }
        }

        // 添加经验奖励
        if (totalExp > 0) {
          reward.push({
            resId: 90001, // 经验道具ID，基于old项目commonEnum.ITEM_RESID.FAME
            num: totalExp
          });
        }
      }

      return reward;
    } catch (error) {
      this.logger.error('计算联赛副本奖励失败', error);
      return [];
    }
  }

  /**
   * 🔧 计算商业赛奖励
   * 基于old项目: battleEndInfoJsonToClient中的商业赛奖励逻辑
   */
  async calculateBusinessReward(
    battleData: any,
    teamAScore: number,
    teamBScore: number
  ): Promise<any> {
    try {
      const homeReward = { cash: 0, fanChange: 0 };
      const awayReward = { cash: 0, fanChange: 0 };

      // 基于old项目的商业赛奖励计算逻辑
      const businessRewardInfo = battleData.teamA.businessRewardInfo;
      if (!businessRewardInfo) {
        return { homeReward, awayReward };
      }

      const Q = businessRewardInfo.FansQ || 100; // 球迷系数
      const isWinner = teamAScore > teamBScore;
      const scoreDiff = Math.abs(teamAScore - teamBScore);

      // 计算主队奖励
      if (isWinner) {
        homeReward.cash = businessRewardInfo.winCash || 1000;
        homeReward.fanChange = Math.floor(Q * (1 + scoreDiff * 0.1));
      } else {
        homeReward.cash = businessRewardInfo.loseCash || 500;
        homeReward.fanChange = Math.floor(-Q * (1 + scoreDiff * 0.1));
      }

      // 计算客队奖励
      awayReward.cash = businessRewardInfo.awayCash || 300;
      awayReward.fanChange = isWinner ? -Math.floor(Q * 0.5) : Math.floor(Q * 0.3);

      return {
        totalCash: businessRewardInfo.totalCash || 0,
        homeReward,
        awayReward
      };
    } catch (error) {
      this.logger.error('计算商业赛奖励失败', error);
      return { homeReward: { cash: 0, fanChange: 0 }, awayReward: { cash: 0, fanChange: 0 } };
    }
  }

  /**
   * 🔧 计算通用奖励
   * 包括经验、金币、声望、物品等通用奖励类型
   */
  async calculateGeneralRewards(battleData: BattleData, isWinner: boolean): Promise<any> {
    try {
      const rewardConfig = await this.getRewardConfig(battleData.battleType);
      
      const rewards = {
        experience: this.calculateExperienceReward(battleData, isWinner, rewardConfig),
        coins: this.calculateCoinReward(battleData, isWinner, rewardConfig),
        reputation: this.calculateReputationReward(battleData, isWinner, rewardConfig),
        items: await this.calculateItemReward(battleData, isWinner, rewardConfig)
      };

      return rewards;
    } catch (error) {
      this.logger.error('计算通用奖励失败', error);
      return { experience: 0, coins: 0, reputation: 0, items: [] };
    }
  }

  /**
   * 🔧 判断是否需要计算通用奖励
   */
  private shouldCalculateGeneralRewards(battleType: string): boolean {
    const excludedTypes = ['PveLeagueCopy', 'PvpMatch'];
    return !excludedTypes.includes(battleType);
  }

  /**
   * 🔧 获取PVE通用配置
   * 基于old项目: Room.prototype.getPveCommonConfig
   * // TODO 需要优化战斗类型
   */
  private async getPveCommonConfig(battleType: BattleType | string, teamResId: number): Promise<any> {
    try {
      if (teamResId === 0) {
        this.logger.error('getPveCommonConfig error teamResId === 0, battleType: ', battleType);
        return null;
      }

      let configs: any[] = [];

      // 根据战斗类型选择对应的配置表
      if (battleType === 'PveLeagueCopy') {
        configs = await this.gameConfig.leagueTeam.getAll();
      } else if (battleType === 'PveTrophy') {
        configs = await this.gameConfig.trophyTeam.getAll();
      } else if (battleType === 'PveWorldCup') {
        // WorldCupPlayer表在当前项目中可能不存在，使用team表替代
        configs = await this.gameConfig.team.getAll();
      } else if (battleType === 'MiddleEastCup') {
        configs = await this.gameConfig.middleEastTeam.getAll();
      } else if (battleType === 'PveWorldBoss') {
        configs = await this.gameConfig.leagueTeam.getAll();
      } else if (battleType === 'GulfCup') {
        configs = await this.gameConfig.gulfTeam.getAll();
      } else if (battleType === 'MLS') {
        configs = await this.gameConfig.mLSTeam.getAll();
      }

      // 查找匹配的配置
      for (const config of configs) {
        // 对于LeagueTeam和TrophyTeam，使用TeamID字段匹配
        if ((battleType === 'PveLeagueCopy' || battleType === 'PveTrophy' || battleType === 'PveWorldBoss') && config.teamId === teamResId) {
          return config;
        }
        // 对于特殊杯赛，使用ID字段匹配
        else if ((battleType === 'MiddleEastCup' || battleType === 'GulfCup' || battleType === 'MLS') && config.id === teamResId) {
          return config;
        }
      }

      this.logger.warn(`未找到匹配的PVE配置: battleType=${battleType}, teamResId=${teamResId}`);
      return null;
    } catch (error) {
      this.logger.error('获取PVE通用配置失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取奖励配置
   */
  private async getRewardConfig(battleType: string): Promise<any> {
    try {
      // 从系统参数表获取奖励配置
      const rewardConfigs = await this.gameConfig.systemParam.getAll();
      const config = rewardConfigs.find((c: any) =>
        c.name && c.name.includes(battleType) && c.name.includes('Reward')
      );
      return config || null;
    } catch (error) {
      this.logger.error('获取奖励配置失败', error);
      return null;
    }
  }

  /**
   * 🔧 计算经验奖励
   */
  private calculateExperienceReward(battleData: BattleData, isWinner: boolean, config: any): number {
    const baseExp = config?.baseExperience || 100;
    const winBonus = isWinner ? (config?.winBonus || 1.5) : 1.0;
    const levelFactor = (battleData.teamB.level || 1) * 0.1;
    return Math.floor(baseExp * winBonus * (1 + levelFactor));
  }

  /**
   * 🔧 计算金币奖励
   */
  private calculateCoinReward(battleData: BattleData, isWinner: boolean, config: any): number {
    const baseCoin = config?.baseCoin || 50;
    const winBonus = isWinner ? (config?.winCoinBonus || 2.0) : 0.5;
    const scoreFactor = ((battleData.teamA.score || 0) + (battleData.teamB.score || 0)) * 0.1;
    return Math.floor(baseCoin * winBonus * (1 + scoreFactor));
  }

  /**
   * 🔧 计算声望奖励
   */
  private calculateReputationReward(battleData: BattleData, isWinner: boolean, config: any): number {
    const baseReputation = config?.baseReputation || 10;
    const winBonus = isWinner ? (config?.winReputationBonus || 1.5) : 0.5;
    const performanceFactor = (battleData.teamA.statistic?.shots || 0) * 0.05;
    return Math.floor(baseReputation * winBonus * performanceFactor);
  }

  /**
   * 🔧 计算物品奖励
   */
  private async calculateItemReward(battleData: BattleData, isWinner: boolean, config: any): Promise<any[]> {
    const items = [];
    
    if (isWinner && config?.itemDropRate) {
      const dropRate = config.itemDropRate;
      if (Math.random() < dropRate) {
        items.push({
          itemId: 1001,
          name: '战斗宝箱',
          quantity: 1,
          rarity: 'common'
        });
      }
    }
    
    return items;
  }
}
