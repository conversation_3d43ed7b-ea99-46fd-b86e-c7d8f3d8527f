/**
 * BattleManager 使用示例
 * 展示新的战斗系统API的各种使用方式
 */

import { BattleManager } from '../battle-manager';
import { GameConfigFacade } from '@libs/game-config';
import { BattleRoom } from '../../../common/schemas/battle.schema';
import { BattleType } from '../types/battle-data.types';

// ==================== 基础使用示例 ====================

/**
 * 示例1：快速战斗（推荐方式）
 * 一行代码完成战斗，自动识别战斗类型
 */
async function quickBattleExample(gameConfig: GameConfigFacade, battleRoom: BattleRoom) {
  const battleManager = new BattleManager(gameConfig);
  
  // 🚀 一键战斗 - 自动识别类型并执行
  const result = await battleManager.quickBattle(battleRoom);
  
  console.log(`战斗结果: ${result.homeScore} - ${result.awayScore}`);
  console.log(`获胜方: ${result.winner}`);
  
  return result;
}

/**
 * 示例2：指定战斗类型
 * 明确指定战斗类型，确保使用正确的战斗逻辑
 */
async function specificBattleTypeExample(gameConfig: GameConfigFacade, battleRoom: BattleRoom) {
  const battleManager = new BattleManager(gameConfig);
  
  // 根据具体需求选择对应的战斗方法
  let result;
  
  if (battleRoom.battleType === BattleType.PVE) {
    // PVE战斗
    result = await battleManager.pveBattle(battleRoom);
  } else if (battleRoom.battleType === BattleType.PVP) {
    // PVP战斗
    result = await battleManager.pvpBattle(battleRoom);
  } else if (battleRoom.battleType === BattleType.WarOfFaith) {
    // 信仰之战（需要指定信仰等级）
    const faithLevel = 5; // 信仰等级
    result = await battleManager.warOfFaithBattle(battleRoom, faithLevel);
  } else if (battleRoom.battleType === BattleType.Guest) {
    // 游客模式
    result = await battleManager.guestBattle(battleRoom);
  }
  
  return result;
}

// ==================== 高级使用示例 ====================

/**
 * 示例3：分步战斗（高级用法）
 * 适用于需要在战斗前后进行额外处理的场景
 */
async function advancedBattleExample(gameConfig: GameConfigFacade, battleRoom: BattleRoom) {
  const battleManager = new BattleManager(gameConfig);
  
  try {
    // 第一步：初始化战斗数据
    const battleData = await battleManager.initializeBattle(battleRoom);
    
    // 第二步：可以在这里进行额外的数据处理
    console.log(`战斗初始化完成，队伍A: ${battleData.teamA.heroes?.length}人`);
    console.log(`战斗初始化完成，队伍B: ${battleData.teamB.heroes?.length}人`);
    
    // 可以修改战斗数据（例如：调整属性、添加特殊效果等）
    if (battleData.teamA.attr) {
      battleData.teamA.attr.morale += 100; // 给队伍A增加士气
    }
    
    // 第三步：执行战斗
    const result = await battleManager.executeBattle(battleData);
    
    // 第四步：处理战斗结果
    console.log(`战斗完成，用时: ${result.battleTime}秒`);
    
    return result;
  } catch (error) {
    console.error('高级战斗失败:', error);
    throw error;
  }
}

/**
 * 示例4：直接访问底层组件（专家级用法）
 * 适用于需要完全自定义战斗流程的场景
 */
async function expertBattleExample(gameConfig: GameConfigFacade, battleRoom: BattleRoom) {
  const battleManager = new BattleManager(gameConfig);
  
  // 获取底层组件
  const battleEngine = battleManager.getBattleEngine();
  const battleInitializer = battleManager.getBattleInitializer();
  
  try {
    // 完全自定义的初始化流程
    const battleData = await battleInitializer.initializeBattleData(battleRoom);
    
    // 根据特殊需求进行额外初始化
    if (battleRoom.battleType === BattleType.WarOfFaith) {
      await battleInitializer.initWarOfFaithBattle(battleData, 10); // 最高信仰等级
    }
    
    // 自定义战斗执行
    const processedBattleData = await battleEngine.executeBattleRounds(battleData);
    const result = battleEngine.generateBattleResult(processedBattleData);
    
    return result;
  } catch (error) {
    console.error('专家级战斗失败:', error);
    throw error;
  }
}

// ==================== 实际业务场景示例 ====================

/**
 * 示例5：在Service中使用BattleManager
 * 展示如何在实际的业务服务中集成BattleManager
 */
class ExampleBattleService {
  constructor(private readonly battleManager: BattleManager) {}
  
  /**
   * PVE副本战斗
   */
  async dungeonBattle(characterId: string, dungeonId: string): Promise<any> {
    try {
      // 1. 创建战斗房间（这里简化处理）
      const battleRoom: BattleRoom = {
        roomId: `dungeon_${Date.now()}`,
        battleType: BattleType.PVE,
        teamA: { characterId, heroes: [] }, // 实际应该从数据库获取
        teamB: { dungeonId, heroes: [] },   // 实际应该从配置获取
        createdAt: new Date(),
        status: 'active'
      } as BattleRoom;
      
      // 2. 执行战斗
      const result = await this.battleManager.pveBattle(battleRoom);
      
      // 3. 处理战斗结果
      if (result.winner === 'teamA') {
        console.log('玩家获胜！');
        // 发放奖励逻辑
      } else {
        console.log('挑战失败！');
        // 失败处理逻辑
      }
      
      return {
        success: result.winner === 'teamA',
        score: `${result.homeScore}-${result.awayScore}`,
        battleRecord: result.battleRecord
      };
    } catch (error) {
      console.error('副本战斗失败:', error);
      throw error;
    }
  }
  
  /**
   * 玩家对战
   */
  async playerVsPlayer(homeCharacterId: string, awayCharacterId: string): Promise<any> {
    try {
      const battleRoom: BattleRoom = {
        roomId: `pvp_${Date.now()}`,
        battleType: BattleType.PVP,
        teamA: { characterId: homeCharacterId, heroes: [] },
        teamB: { characterId: awayCharacterId, heroes: [] },
        createdAt: new Date(),
        status: 'active'
      } as BattleRoom;
      
      // 使用快速战斗API
      const result = await this.battleManager.quickBattle(battleRoom);
      
      return {
        winner: result.winner,
        homeScore: result.homeScore,
        awayScore: result.awayScore,
        battleTime: result.battleTime,
        statistics: result.statistics
      };
    } catch (error) {
      console.error('玩家对战失败:', error);
      throw error;
    }
  }
  
  /**
   * 信仰之战
   */
  async faithWarBattle(attackerId: string, defenderId: string, faithLevel: number): Promise<any> {
    try {
      const battleRoom: BattleRoom = {
        roomId: `faith_${Date.now()}`,
        battleType: BattleType.WarOfFaith,
        teamA: { characterId: attackerId, heroes: [] },
        teamB: { characterId: defenderId, heroes: [] },
        createdAt: new Date(),
        status: 'active'
      } as BattleRoom;
      
      // 使用专用的信仰之战API
      const result = await this.battleManager.warOfFaithBattle(battleRoom, faithLevel);
      
      return {
        winner: result.winner,
        faithLevel: faithLevel,
        battleRecord: result.battleRecord,
        specialEffects: result.statistics?.specialEffects || []
      };
    } catch (error) {
      console.error('信仰之战失败:', error);
      throw error;
    }
  }
}

// ==================== 错误处理示例 ====================

/**
 * 示例6：完整的错误处理
 */
async function battleWithErrorHandling(gameConfig: GameConfigFacade, battleRoom: BattleRoom) {
  const battleManager = new BattleManager(gameConfig);
  
  try {
    const result = await battleManager.quickBattle(battleRoom);
    return { success: true, data: result };
  } catch (error) {
    console.error('战斗执行失败:', error);
    
    // 根据错误类型进行不同处理
    if (error.message.includes('数据不完整')) {
      return { success: false, error: 'INVALID_DATA', message: '战斗数据不完整' };
    } else if (error.message.includes('配置')) {
      return { success: false, error: 'CONFIG_ERROR', message: '战斗配置错误' };
    } else {
      return { success: false, error: 'UNKNOWN_ERROR', message: '未知错误' };
    }
  }
}

export {
  quickBattleExample,
  specificBattleTypeExample,
  advancedBattleExample,
  expertBattleExample,
  ExampleBattleService,
  battleWithErrorHandling
};
