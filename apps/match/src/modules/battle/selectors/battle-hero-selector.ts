import { Logger } from '@nestjs/common';
import { BattleTeam, BattleHero } from '../types/battle-data.types';
import { GameConfigFacade } from '@libs/game-config';

/**
 * 战斗球员选择器
 * 基于old项目room.js的球员选择逻辑
 * 
 * 职责：
 * - 根据进攻方式和阶段选择参与球员
 * - 随机攻击球员选择 (getRandomAttacker)
 * - 随机防守球员选择 (getRandomDefender)
 * - 特殊情况球员选择 (定位球、角球等)
 */
export class BattleHeroSelector {
  private readonly logger = new Logger(BattleHeroSelector.name);

  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 🔧 获取随机攻击球员
   * 基于old项目: Room.prototype.getRandomAttacker
   *
   * @param attackerTeam 攻击方队伍数据 - 包含球员列表和阵型信息
   * @param attackerType 攻击球员类型 - A1或A2
   * @param attackMode 攻击模式 - 影响球员选择策略
   * @param period 战斗阶段 - 0=发起, 1=推进, 2=射门
   * @returns 选中的球员UID
   */
  getRandomAttacker(
    attackerTeam: BattleTeam,
    attackerType: string, // A1, A2
    attackMode: number,
    period: number
  ): string {
    let selectedHeroUid = '';

    try {
      // 特殊进攻方式的球员选择
      if (period === 0) { // 发起阶段
        selectedHeroUid = this.selectStartPeriodAttacker(attackerTeam, attackMode);
      } else if (period === 2) { // 射门阶段
        selectedHeroUid = this.selectEndPeriodAttacker(attackerTeam, attackMode, attackerType);
      } else {
        // 推进阶段一般不需要额外选择A2
        selectedHeroUid = this.selectRandomFromFormation(attackerTeam, ['ST', 'WL', 'WR', 'MC']);
      }

      this.logger.debug(`选择攻击球员: ${attackerType}, 进攻方式=${attackMode}, 阶段=${period}, 球员=${selectedHeroUid}`);
    } catch (error) {
      this.logger.error('选择攻击球员失败', error);
      selectedHeroUid = this.selectRandomFromFormation(attackerTeam, ['ST']);
    }

    return selectedHeroUid;
  }

  /**
   * 🔧 获取随机防守球员
   * 基于old项目: Room.prototype.getRandomDefender
   *
   * @param defenderTeam 防守方队伍数据 - 包含球员列表和阵型信息
   * @param attackerType 攻击球员类型 - A1或A2，影响防守策略
   * @param period 战斗阶段 - 0=发起, 1=推进, 2=射门
   * @returns 选中的防守球员UID
   */
  getRandomDefender(
    defenderTeam: BattleTeam,
    attackerType: string, // A1, A2
    period: number
  ): string {
    let selectedHeroUid = '';

    try {
      if (period === 1) { // 推进阶段防守
        selectedHeroUid = this.selectBreakThroughDefender(defenderTeam);
      } else if (period === 2) { // 射门阶段防守
        selectedHeroUid = this.selectShotDefender(defenderTeam);
      }

      this.logger.debug(`选择防守球员: 攻击者=${attackerType}, 阶段=${period}, 球员=${selectedHeroUid}`);
    } catch (error) {
      this.logger.error('选择防守球员失败', error);
      selectedHeroUid = this.selectRandomFromFormation(defenderTeam, ['DC']);
    }

    return selectedHeroUid;
  }

  /**
   * 🔧 选择发起阶段攻击球员
   * 处理定位球、角球、点球等特殊情况
   *
   * @param attackerTeam 攻击方队伍数据
   * @param attackMode 攻击模式 - 决定球员选择策略
   * @returns 选中的球员UID
   */
  private selectStartPeriodAttacker(attackerTeam: BattleTeam, attackMode: number): string {
    switch (attackMode) {
      case 7: // 任意球
        return attackerTeam.freeKickHero || this.selectBestAttributeHero(attackerTeam, 'freeKick');
      case 8: // 角球
        return attackerTeam.cornerKickHero || this.selectBestAttributeHero(attackerTeam, 'cornerKick');
      case 9: // 点球
        return attackerTeam.penaltiesHero || this.selectBestAttributeHero(attackerTeam, 'penalties');
      default:
        return this.selectRandomFromFormation(attackerTeam, ['ST', 'WL', 'WR']);
    }
  }

  /**
   * 🔧 选择射门阶段攻击球员
   * 根据进攻方式选择合适的射门球员
   *
   * @param attackerTeam 攻击方队伍数据
   * @param attackMode 攻击模式 - 影响射门球员选择
   * @param attackerType 攻击球员类型 - A1或A2
   * @returns 选中的射门球员UID
   */
  private selectEndPeriodAttacker(attackerTeam: BattleTeam, attackMode: number, attackerType: string): string {
    if (attackerType === 'A2') {
      // A2球员选择逻辑
      switch (attackMode) {
        case 1: // 头球 - 选择头球好的球员
          return this.selectBestAttributeHero(attackerTeam, 'heading');
        case 3: // 推射 - 选择射门好的球员
          return this.selectBestAttributeHero(attackerTeam, 'finishing');
        case 4: // 抢点 - 选择速度快的球员
          return this.selectBestAttributeHero(attackerTeam, 'speed');
        case 8: // 角球 - 选择头球好的球员
          return this.selectBestAttributeHero(attackerTeam, 'heading');
        default:
          return this.selectRandomFromFormation(attackerTeam, ['ST', 'WL', 'WR']);
      }
    }

    // A1球员选择逻辑
    return this.selectRandomFromFormation(attackerTeam, ['ST', 'WL', 'WR']);
  }

  /**
   * 🔧 选择突破阶段防守球员
   *
   * @param defenderTeam 防守方队伍数据
   * @returns 选中的防守球员UID
   */
  private selectBreakThroughDefender(defenderTeam: BattleTeam): string {
    // 优先选择中后卫和边后卫
    return this.selectRandomFromFormation(defenderTeam, ['DC', 'DL', 'DR', 'MC']);
  }

  /**
   * 🔧 选择射门阶段防守球员
   *
   * @param defenderTeam 防守方队伍数据
   * @returns 选中的防守球员UID
   */
  private selectShotDefender(defenderTeam: BattleTeam): string {
    // 射门阶段主要是后卫参与防守
    return this.selectRandomFromFormation(defenderTeam, ['DC', 'DL', 'DR']);
  }





  /**
   * 🔧 选择指定属性最高的球员
   *
   * @param team 队伍数据 - 包含球员列表
   * @param attributeType 属性类型 - 如'attack', 'defend'等
   * @returns 属性最高的球员UID
   */
  private selectBestAttributeHero(team: BattleTeam, attributeType: string): string {
    try {
      if (!team.heroes || !Array.isArray(team.heroes)) {
        return '';
      }

      let bestHero = '';
      let bestValue = 0;

      for (const hero of team.heroes) {
        const attributeValue = this.getHeroAttribute(hero, attributeType);
        if (attributeValue > bestValue) {
          bestValue = attributeValue;
          bestHero = hero.heroId;
        }
      }

      return bestHero;
    } catch (error) {
      this.logger.error(`选择最佳属性球员失败: ${attributeType}`, error);
      return '';
    }
  }

  /**
   * 🔧 从指定位置随机选择球员
   *
   * @param team 队伍数据 - 包含球员列表
   * @param positions 位置列表 - 如['DC', 'DL', 'DR']
   * @returns 随机选中的球员UID
   */
  private selectRandomFromFormation(team: BattleTeam, positions: string[]): string {
    try {
      if (!team.heroes || !Array.isArray(team.heroes)) {
        return '';
      }

      // 筛选指定位置的球员 - 使用BattleHero类型
      const eligibleHeros = team.heroes.filter((hero: BattleHero) =>
        positions.includes(hero.position)
      );

      if (eligibleHeros.length === 0) {
        // 如果没有指定位置的球员，从所有球员中选择
        const randomIndex = Math.floor(Math.random() * team.heroes.length);
        return team.heroes[randomIndex].heroId || team.heroes[randomIndex].heroId;
      }

      // 随机选择一个符合条件的球员
      const randomIndex = Math.floor(Math.random() * eligibleHeros.length);
      return eligibleHeros[randomIndex].heroId || eligibleHeros[randomIndex].heroId;
    } catch (error) {
      this.logger.error(`从阵型选择球员失败: ${positions.join(',')}`, error);
      return '';
    }
  }

  /**
   * 🔧 获取球员属性值
   */
  private getHeroAttribute(hero: BattleHero, attributeType: string): number {
    try {
      // 直接使用BattleHero.attributes中真实存在的字段（基于hero.schema.ts HeroAttributes）
      switch (attributeType) {
        case 'freeKick':
          return hero.attributes.freeKick.cur;
        case 'cornerKick':
          return hero.attributes.cornerKick.cur;
        case 'penalties':
          return hero.attributes.penalties.cur;
        case 'speed':
          return hero.attributes.speed.cur;
        case 'strength':
          return hero.attributes.strength.cur;
        case 'passing':
          return hero.attributes.passing.cur;
        case 'dribbling':
          return hero.attributes.dribbling.cur;
        case 'heading':
          return hero.attributes.heading.cur;
        case 'longShots':
          return hero.attributes.longShots.cur;
        case 'longPassing':
          return hero.attributes.longPassing.cur;
        case 'volleys':
          return hero.attributes.volleys.cur;
        case 'standingTackle':
          return hero.attributes.standingTackle.cur;
        case 'slidingTackle':
          return hero.attributes.slidingTackle.cur;
        case 'save':
          return hero.attributes.save.cur;
        case 'attack':
          return hero.attributes.attack.cur;
        case 'jumping':
          return hero.attributes.jumping.cur;
        case 'stamina':
          return hero.attributes.stamina.cur;
        case 'explosiveForce':
          return hero.attributes.explosiveForce.cur;
        case 'finishing':
          return hero.attributes.finishing.cur;
        case 'resistanceDamage':
          return hero.attributes.resistanceDamage.cur;
        default:
          return 0;
      }
    } catch (error) {
      this.logger.warn(`获取球员属性失败: ${attributeType}`, error);
      return 0;
    }
  }

  /**
   * 🔧 获取门将球员
   *
   * @param team 队伍数据 - 包含球员列表
   * @returns 门将球员UID
   */
  getGoalkeeper(team: BattleTeam): string {
    try {
      if (!team.heroes || !Array.isArray(team.heroes)) {
        return '';
      }

      const goalkeeper = team.heroes.find((hero: BattleHero) => hero.position === 'GK');
      return goalkeeper?.heroId || goalkeeper?.heroId || '';
    } catch (error) {
      this.logger.error('获取门将球员失败', error);
      return '';
    }
  }

  /**
   * 🔧 验证球员是否存在于阵型中
   *
   * @param team 队伍数据 - 包含球员列表
   * @param heroId 球员UID - 要验证的球员标识
   * @returns 是否存在于阵型中
   */
  validateHeroInFormation(team: BattleTeam, heroId: string): boolean {
    try {
      if (!team.heroes || !Array.isArray(team.heroes) || !heroId) {
        return false;
      }

      return team.heroes.some((hero: BattleHero) =>
        hero.heroId === heroId || hero.heroId === heroId
      );
    } catch (error) {
      this.logger.warn(`验证球员存在性失败: ${heroId}`, error);
      return false;
    }
  }
}
