import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { GroundMatchService } from './ground-match.service';
import {
  GetMyGroundFieldInfoResponseDto,
  OccupyFieldResponseDto,
  RobFieldResponseDto,
  DriveAwayResponseDto,
  ReportResponseDto,
  SearchOpponentResponseDto,
  GetGroundMatchRankingResponseDto,
  GetGroundMatchRewardsResponseDto
} from '../../common/dto/ground-match.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  GetMyGroundFieldInfoPayloadDto,
  OccupyFieldPayloadDto,
  RobFieldPayloadDto,
  DriveAwayPayloadDto,
  ReportPayloadDto,
  SearchOpponentPayloadDto,
  GetGroundMatchRankingPayloadDto,
  GetGroundMatchRewardsPayloadDto,
  RefreshReportNumPayloadDto,
  GetGroundMatchStatisticsPayloadDto
} from '../../common/dto/ground-match-payload.dto';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 个人球场争夺战控制器
 * 基于old项目footballGround.js和groundMatchService.js的接口设计
 * 
 * 核心接口：
 * - groundMatch.getMyGroundFieldInfo: 获取我的训练场信息
 * - groundMatch.occupyField: 占领训练场
 * - groundMatch.robField: 抢夺训练场
 * - groundMatch.driveAway: 驱赶占领者
 * - groundMatch.report: 举报占领者
 * - groundMatch.searchOpponent: 搜索对手
 * - groundMatch.getRanking: 获取排行榜
 * - groundMatch.getRewards: 获取奖励
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class GroundMatchController extends BaseController {
  constructor(private readonly groundMatchService: GroundMatchService) {
    super('GroundMatchController');
  }

  /**
   * 获取我的训练场信息
   * 基于old项目getMyGroundFieldInfo功能
   */
  @MessagePattern('groundMatch.getMyGroundFieldInfo')
  @Cacheable({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getMyGroundFieldInfo(@Payload() payload: GetMyGroundFieldInfoPayloadDto): Promise<XResponse<GetMyGroundFieldInfoResponseDto>> {
    this.logger.log(`获取训练场信息: ${payload.characterId}`);
    
    const result = await this.groundMatchService.getMyGroundFieldInfo(payload);
    return this.fromResult(result);
  }

  /**
   * 占领训练场
   * 基于old项目占领功能
   */
  @MessagePattern('groundMatch.occupyField')
  @CacheEvict({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async occupyField(@Payload() payload: OccupyFieldPayloadDto): Promise<XResponse<OccupyFieldResponseDto>> {
    this.logger.log(`占领训练场: ${payload.characterId} -> ${payload.targetUid}, 索引: ${payload.fieldIndex}`);
    
    const result = await this.groundMatchService.occupyField(payload);
    return this.fromResult(result);
  }

  /**
   * 抢夺训练场
   * 基于old项目抢夺功能
   */
  @MessagePattern('groundMatch.robField')
  @CacheEvict({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async robField(@Payload() payload: RobFieldPayloadDto): Promise<XResponse<RobFieldResponseDto>> {
    this.logger.log(`抢夺训练场: ${payload.characterId} -> ${payload.targetUid}, 索引: ${payload.fieldIndex}`);
    
    const result = await this.groundMatchService.robField(payload);
    return this.fromResult(result);
  }

  /**
   * 驱赶占领者
   * 基于old项目驱赶功能
   */
  @MessagePattern('groundMatch.driveAway')
  @CacheEvict({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async driveAway(@Payload() payload: DriveAwayPayloadDto): Promise<XResponse<DriveAwayResponseDto>> {
    this.logger.log(`驱赶占领者: ${payload.characterId}, 索引: ${payload.fieldIndex}`);
    
    const result = await this.groundMatchService.driveAway(payload);
    return this.fromResult(result);
  }

  /**
   * 举报占领者
   * 基于old项目举报功能
   */
  @MessagePattern('groundMatch.report')
  @CacheEvict({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async report(@Payload() payload: ReportPayloadDto): Promise<XResponse<ReportResponseDto>> {
    this.logger.log(`举报占领者: ${payload.characterId} -> ${payload.reportedUid}, 索引: ${payload.occupyIndex}`);
    
    const result = await this.groundMatchService.report(payload);
    return this.fromResult(result);
  }

  /**
   * 搜索对手
   * 基于old项目搜索功能
   */
  @MessagePattern('groundMatch.searchOpponent')
  @Cacheable({
    key: 'groundMatch:search:#{payload.characterId}:#{payload.minBallFan}:#{payload.maxBallFan}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600 // 10分钟缓存
  })
  async searchOpponent(@Payload() payload: SearchOpponentPayloadDto): Promise<XResponse<SearchOpponentResponseDto>> {
    this.logger.log(`搜索对手: ${payload.characterId}`);
    
    const result = await this.groundMatchService.searchOpponent(payload);
    return this.fromResult(result);
  }

  /**
   * 获取排行榜
   * 基于old项目排行榜功能
   */
  @MessagePattern('groundMatch.getRanking')
  @Cacheable({
    key: 'groundMatch:ranking:#{payload.rankType}:#{payload.limit}',
    dataType: 'global',
    ttl: 900 // 15分钟缓存
  })
  async getRanking(@Payload() payload: GetGroundMatchRankingPayloadDto): Promise<XResponse<GetGroundMatchRankingResponseDto>> {
    this.logger.log(`获取排行榜: ${payload.characterId}, 类型: ${payload.rankType || 'ballFan'}`);
    
    const result = await this.groundMatchService.getRanking(payload);
    return this.fromResult(result);
  }

  /**
   * 获取奖励
   * 基于old项目奖励功能
   */
  @MessagePattern('groundMatch.getRewards')
  @CacheEvict({
    key: 'groundMatch:fieldInfo:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getRewards(@Payload() payload: GetGroundMatchRewardsPayloadDto): Promise<XResponse<GetGroundMatchRewardsResponseDto>> {
    this.logger.log(`获取奖励: ${payload.characterId}`);
    
    const result = await this.groundMatchService.getRewards(payload);
    return this.fromResult(result);
  }

  /**
   * 刷新举报次数（管理接口）
   */
  @MessagePattern('groundMatch.refreshReportNum')
  async refreshReportNum(@Payload() payload: RefreshReportNumPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`刷新举报次数: ${payload.characterId}, 次数: ${payload.reportNum || 10}`);

    const reportNum = payload.reportNum || 10;
    const result = await this.groundMatchService.refreshReportNum(payload.characterId, reportNum);
    return this.fromResult(result);
  }

  /**
   * 获取统计信息（管理接口）
   */
  @MessagePattern('groundMatch.getStatistics')
  async getStatistics(@Payload() _payload: GetGroundMatchStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取球场争夺战统计信息');

    const result = await this.groundMatchService.getStatistics();
    return this.fromResult(result);
  }
}
