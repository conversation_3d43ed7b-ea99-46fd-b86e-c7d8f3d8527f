import { Injectable, Logger } from '@nestjs/common';
import { LeagueRepository } from '../../common/repositories/league.repository';
import {GameConfigFacade, LeagueTeamDefinition} from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { HeroPosition, BattleType } from '@libs/game-constants';
import { BattleService } from '../battle/battle.service';
import { BattleDataService } from '../../common/services/battle-data.service';
import {
  GetLeagueCopyDataDto,
  PVELeagueBattleDto,
  TakeLeagueRewardDto,
  GetLeagueCopyDataResponseDto,
  PVEBattleResultResponseDto,
  OneLeagueResponseDto
} from '../../common/dto/league.dto';
import { League, OneLeague, LeagueCopyData } from '../../common/schemas/league.schema';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 联赛系统服务 - 已适配Result模式
 * 严格基于old项目leagueCopy.js的业务逻辑实现
 *
 * 核心功能：
 * - 初始化联赛副本数据
 * - 获取联赛副本数据
 * - PVE联赛战斗处理
 * - 联赛奖励发放
 * - 联赛进度管理
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的微服务调用和错误处理
 */
@Injectable()
export class LeagueService extends BaseService {

  constructor(
    private readonly leagueRepository: LeagueRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly battleService: BattleService,
    private readonly battleDataService: BattleDataService,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('LeagueService', microserviceClient);
  }

  /**
   * 获取联赛副本数据
   * 基于old项目: LeagueCopy.prototype.getLeagueCopyData
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getLeagueCopyData(dto: GetLeagueCopyDataDto): Promise<XResult<GetLeagueCopyDataResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取联赛副本数据: ${dto.characterId}`);

      // 查找现有联赛数据
      const leagueDataResult = await this.leagueRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(leagueDataResult)) {
        return XResultUtils.error(`查询联赛数据失败: ${leagueDataResult.message}`, leagueDataResult.code);
      }

      let leagueData = leagueDataResult.data;

      // 如果没有数据，初始化联赛数据
      if (!leagueData || this.hasAllCopyNotPassed(leagueData)) {
        const nextLeagueIdResult = await this.initLeagueCopyData(dto.characterId);
        if (XResultUtils.isFailure(nextLeagueIdResult)) {
          return XResultUtils.error(`初始化联赛数据失败: ${nextLeagueIdResult.message}`, nextLeagueIdResult.code);
        }

        const nextLeagueId = nextLeagueIdResult.data;
        if (nextLeagueId === 0) {
          return XResultUtils.error('初始化联赛数据失败', 'LEAGUE_INIT_FAILED');
        }

        // 重新查询数据
        const newLeagueDataResult = await this.leagueRepository.findById(dto.characterId);
        if (XResultUtils.isFailure(newLeagueDataResult)) {
          return XResultUtils.error(`重新查询联赛数据失败: ${newLeagueDataResult.message}`, newLeagueDataResult.code);
        }
        leagueData = newLeagueDataResult.data;
      }

      if (!leagueData) {
        return XResultUtils.error('联赛数据不存在', 'LEAGUE_DATA_NOT_FOUND');
      }

      const nextLeagueId = this.getNextLeagueId(leagueData);
      const nextTeamCopyId = this.getnextTeamCopyId(leagueData);

      // 根据类型过滤联赛数据
      let leagueCopyData: OneLeagueResponseDto[];
      if (dto.type) {
        const leagueCopyDataResult = await this.makeClientLeagueListInfoByType(leagueData, dto.type);
        if (XResultUtils.isFailure(leagueCopyDataResult)) {
          return XResultUtils.error(`生成联赛数据失败: ${leagueCopyDataResult.message}`, leagueCopyDataResult.code);
        }
        leagueCopyData = leagueCopyDataResult.data;
      } else {
        leagueCopyData = this.makeClientLeagueListInfo(leagueData);
      }

      const responseData =  {
        nextLeagueId,
        nextTeamCopyId,
        leagueCopyData,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_league_copy_data',
      metadata: {
        characterId: dto.characterId,
        type: dto.type
      }
    });
  }

  /**
   * PVE联赛战斗
   * 基于old项目: LeagueCopy.prototype.PVELeagueCopyBattle
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async pveBattle(dto: PVELeagueBattleDto): Promise<XResult<PVEBattleResultResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`PVE联赛战斗: ${dto.characterId}, 联赛${dto.leagueId}, 副本${dto.teamCopyId}`);

      // 获取角色阵容数据 - 使用BaseService的标准化微服务调用
      const lineupsResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'lineup.getLineups',
        { characterId: dto.characterId, serverId: dto.serverId }
      );

      if (XResultUtils.isFailure(lineupsResult)) {
        return XResultUtils.error(`获取阵容数据失败: ${lineupsResult.message}`, lineupsResult.code);
      }

      // 获取PVE配置数据
      const definition = await this.getOneLeagueTeamDefinition(dto.leagueId, dto.teamCopyId);
      if (!definition) {
        return XResultUtils.error('联赛配置不存在', 'LEAGUE_CONFIG_NOT_FOUND');
      }

      // 转换阵容数据为战斗数据格式
      const characterBattleDataResult = await this.battleDataService.convertLineupToBattleData(lineupsResult.data, dto.characterId);
      if (XResultUtils.isFailure(characterBattleDataResult)) {
        return XResultUtils.error(`战斗数据转换失败: ${characterBattleDataResult.message}`, characterBattleDataResult.code);
      }

      const characterBattleData = characterBattleDataResult.data;
      if (!characterBattleData) {
        return XResultUtils.error('战斗数据为空', 'BATTLE_DATA_EMPTY');
      }

      this.logger.log(`玩家战斗数据转换完成: 球员数量=${characterBattleData.heroes.length}`);

      // 生成敌方战斗数据
      const enemyBattleDataResult = await this.generateEnemyBattleData(definition);
      if (XResultUtils.isFailure(enemyBattleDataResult)) {
        return XResultUtils.error(`敌方数据生成失败: ${enemyBattleDataResult.message}`, enemyBattleDataResult.code);
      }

      const enemyBattleData = enemyBattleDataResult.data;
      if (!enemyBattleData) {
        return XResultUtils.error('敌方数据为空', 'ENEMY_DATA_EMPTY');
      }

      this.logger.log(`敌方战斗数据生成完成: 球员数量=${enemyBattleData.heroes.length}`);

      // 调用本地战斗服务 - 注意：battleService现在返回XResult类型
      const battleResult = await this.battleService.pveBattle({
        characterId: dto.characterId,
        characterBattleData: characterBattleData,
        enemyBattleData: enemyBattleData,
        battleType: BattleType.PVE_LEAGUE,
        leagueId: dto.leagueId,
        teamCopyId: dto.teamCopyId,
      });

      if (XResultUtils.isFailure(battleResult)) {
        return XResultUtils.error(`战斗计算失败: ${battleResult.message}`, battleResult.code);
      }

      const battleData = battleResult.data;

      // 处理战斗结果
      const processResult = await this.processBattleResult(
        dto.characterId,
        dto.leagueId,
        dto.teamCopyId,
        battleData.homeScore,
        battleData.awayScore
      );

      if (XResultUtils.isFailure(processResult)) {
        return XResultUtils.error(`处理战斗结果失败: ${processResult.message}`, processResult.code);
      }

      const responseData: PVEBattleResultResponseDto = {
        battleResult: battleData,
        rewards: processResult.data.rewards,
        nextLeagueId: processResult.data.nextLeagueId,
        nextTeamCopyId: processResult.data.nextTeamCopyId,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'pve_league_battle',
      metadata: {
        characterId: dto.characterId,
        leagueId: dto.leagueId,
        teamCopyId: dto.teamCopyId
      }
    });
  }



  // ==================== 私有方法 ====================

  /**
   * 检查是否所有副本都未通过
   * 基于old项目: LeagueCopy.prototype.hasAllCopyNotPassed
   */
  private hasAllCopyNotPassed(leagueData: League): boolean {
    return !leagueData.allLeagueCopys || leagueData.allLeagueCopys.length === 0;
  }

  /**
   * 初始化联赛副本数据
   * 基于old项目: LeagueCopy.prototype.initLeagueCopyData
   */
  private async initLeagueCopyData(characterId: string): Promise<XResult<number>> {
    const leagueConfigs = await this.gameConfig.leagueCopy.getAll();
    if (!leagueConfigs || leagueConfigs.length === 0) {
      return XResultUtils.error('联赛配置不存在', 'LEAGUE_CONFIG_NOT_FOUND');
    }

    const allLeagueCopys: OneLeague[] = [];
    let firstLeagueId = 0;

    // 遍历联赛配置，创建联赛数据
    for (const config of leagueConfigs) {
      if (!config || config.id <= 0) continue;

      if (firstLeagueId === 0) {
        firstLeagueId = config.id;
      }

      const oneLeague = this.newLeague(config.id);
      const copyDataResult = await this.newLeagueCopyData(config.id);

      if (XResultUtils.isFailure(copyDataResult)) {
        this.logger.warn(`创建联赛副本数据失败: ${copyDataResult.message}`);
        continue;
      }

      const copyData = copyDataResult.data;
      if (copyData.length > 0) {
        oneLeague.copyData = copyData;
        oneLeague.count = copyData.length * 3; // 每个副本最多3星
        allLeagueCopys.push(oneLeague);
      }
    }

    // 解锁第一个联赛的第一个副本
    if (allLeagueCopys.length > 0 && allLeagueCopys[0].copyData.length > 0) {
      allLeagueCopys[0].copyData[0].unlockState = 1;
    }

    // 保存到数据库 - 直接调用BaseRepository的upsert方法
    const upsertResult = await this.leagueRepository.upsert(
      { characterId: characterId },
      {
        characterId: characterId,
        allLeagueCopys,
        fixData: 0,
        lastUpdateTime: new Date(),
      }
    );

    if (XResultUtils.isFailure(upsertResult)) {
      return XResultUtils.error(`保存联赛数据失败: ${upsertResult.message}`, upsertResult.code);
    }

    return XResultUtils.ok(firstLeagueId);
  }

  /**
   * 创建新的联赛数据
   * 基于old项目: LeagueCopy.prototype.newLeague
   */
  private newLeague(resID: number): OneLeague {
    return {
      uid: resID,                    // 联赛ID，直接使用配置的ID
      isTakeLeagueReward: 0,         // 是否领取了奖励
      isAllCopyPassed: 0,            // 当前联赛是否全部通关
      curPassCount: 0,               // 当前通关副本总星数
      count: 0,                      // 联赛总星数
      copyData: [],                  // 副本数据
    };
  }

  /**
   * 创建联赛副本数据
   * 基于old项目: LeagueCopy.prototype.newLeagueCopyData
   * 🔥 优化：使用findBy方法，只获取指定联赛的队伍配置
   */
  private async newLeagueCopyData(leagueId: number): Promise<XResult<LeagueCopyData[]>> {
    // 使用新的findBy方法，直接根据leagueId筛选
    const leagueTeamConfigs = await this.gameConfig.leagueTeam.findBy('leagueId', leagueId);
    if (!leagueTeamConfigs || leagueTeamConfigs.length === 0) {
      this.logger.error(`联赛队伍配置不存在: leagueId=${leagueId}`);
      return XResultUtils.ok([]);
    }

    const copyData: LeagueCopyData[] = [];

    // 已经通过findBy筛选了指定联赛的队伍配置，直接使用
    for (const config of leagueTeamConfigs) {
      if (!config || config.id <= 0) continue;

      if (config.teamId > 0) {
        const copyObj = this.newCopy(config.teamId);
        copyData.push(copyObj);
      }
    }

    return XResultUtils.ok(copyData);
  }

  /**
   * 创建副本数据
   * 基于old项目: LeagueCopy.prototype.newCopy
   */
  private newCopy(copyID: number): LeagueCopyData {
    return {
      teamCopyId: copyID,            // 球队副本ID
      process: 0,                    // 进度 0为没有挑战过
      takeCopyRewardProcess: 0,      // 领取副本奖励进度
      isFinished: 0,                 // 是否完成三星挑战
      unlockState: 0,                // 解锁状态 0为未解锁
    };
  }

  /**
   * 获取下一个联赛ID
   * 基于old项目: LeagueCopy.prototype.getNextLeagueId
   */
  private getNextLeagueId(leagueData: League): number {
    if (!leagueData.allLeagueCopys || leagueData.allLeagueCopys.length === 0) {
      return 0;
    }

    // 找到第一个未全部通关的联赛
    for (const league of leagueData.allLeagueCopys) {
      if (league.isAllCopyPassed === 0) {
        return league.uid;
      }
    }

    // 如果所有联赛都通关了，返回最后一个联赛ID
    return leagueData.allLeagueCopys[leagueData.allLeagueCopys.length - 1].uid;
  }

  /**
   * 获取下一个副本ID
   * 基于old项目: LeagueCopy.prototype.getnextTeamCopyId
   */
  private getnextTeamCopyId(leagueData: League): number {
    const nextLeagueId = this.getNextLeagueId(leagueData);
    if (nextLeagueId === 0) return 0;

    const league = leagueData.allLeagueCopys.find(l => l.uid === nextLeagueId);
    if (!league) return 0;

    // 找到第一个未完成三星的副本
    for (const copy of league.copyData) {
      if (copy.process < 3) {
        return copy.teamCopyId;
      }
    }

    // 如果所有副本都完成了，返回最后一个副本ID
    return league.copyData.length > 0 ? league.copyData[league.copyData.length - 1].teamCopyId : 0;
  }

  /**
   * 获取联赛配置
   * 基于old项目: LeagueCopy.prototype.getOneLeagueConfig
   * 已适配Result模式：返回具体配置对象而不是XResult（因为这是纯数据查询）
   */
  private async getOneLeagueTeamDefinition(leagueId: number, teamCopyId: number): Promise<LeagueTeamDefinition | null> {
    const definitions = await this.gameConfig.leagueTeam.filter(data =>
      data.leagueId === leagueId && data.teamId === teamCopyId
    );

    // 返回第一个匹配的配置，如果没有则返回null
    return definitions.length > 0 ? definitions[0] : null;
  }

  /**
   * 处理战斗结果
   * 基于old项目: LeagueCopy.prototype.PVELeagueCopyBattleResultProcess
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async processBattleResult(
    characterId: string,
    leagueId: number,
    teamCopyId: number,
    selfScore: number,
    enemyScore: number
  ): Promise<XResult<any>> {
    const leagueDataResult = await this.leagueRepository.findById(characterId);
    if (XResultUtils.isFailure(leagueDataResult)) {
      return XResultUtils.error(`查询联赛数据失败: ${leagueDataResult.message}`, leagueDataResult.code);
    }

    const leagueData = leagueDataResult.data;
    if (!leagueData) {
      return XResultUtils.error('联赛数据不存在', 'LEAGUE_DATA_NOT_FOUND');
    }

    const league = leagueData.allLeagueCopys.find(l => l.uid === leagueId);
    if (!league) {
      return XResultUtils.error('联赛不存在', 'LEAGUE_NOT_FOUND');
    }

    const copy = league.copyData.find(c => c.teamCopyId === teamCopyId);
    if (!copy) {
      return XResultUtils.error('副本不存在', 'COPY_NOT_FOUND');
    }

    // 判断战斗结果
    const result = selfScore > enemyScore ? 1 : 0;
    const rewards = [];

    if (result === 1) {
      // 胜利，更新进度
      const oldProcess = copy.process;

      // 🔧 修复星级计算逻辑 - 基于old项目PVELeagueCopyBattleResultProcess
      // 星级 = 净胜球数，最大3星，最小1星（胜利至少1星）
      let finishedStar = selfScore - enemyScore;
      if (finishedStar > 3) {
        finishedStar = 3;
      }
      if (finishedStar < 1) {
        finishedStar = 1; // 胜利至少1星
      }

      // 只有新进度更高才更新
      if (finishedStar > oldProcess) {
        copy.process = finishedStar;

        // 🔧 如果达到三星，标记为完成
        if (finishedStar === 3) {
          copy.isFinished = 1;
        }

        // 更新联赛总星数
        league.curPassCount = league.curPassCount - oldProcess + finishedStar;

        // 检查是否全部通关
        this.checkLeagueAllPassed(league);

        // 🔧 基于old项目逻辑：解锁下一个副本和联赛
        await this.unlockNextCopyAndLeague(characterId, leagueId, teamCopyId, league);

        // 🔧 发放副本奖励 - 基于old项目sendCopyRewardEmail逻辑
        const rewardResult = await this.sendCopyRewardEmail(characterId, leagueId, teamCopyId, finishedStar, copy.takeCopyRewardProcess);
        if (XResultUtils.isSuccess(rewardResult)) {
          rewards.push(...rewardResult.data);
          // 更新奖励领取进度
          copy.takeCopyRewardProcess = finishedStar;
        }

        // 🔧 触发任务系统 - 基于old项目任务触发逻辑
        await this.triggerLeagueTasks(characterId, leagueId, teamCopyId, finishedStar, league.curPassCount);
      }

      // TODO: 触发任务系统 - Activity服务调用（使用BaseService的标准化微服务调用）
      // 注意：Activity服务已开发但尚未测试，暂时注释以避免超时错误
      // 开启方法：
      // 1. 确保Activity服务已启动：npm run start:activity
      // 2. 验证Activity服务健康状态：GET http://localhost:3006/api/health
      // 3. 测试Activity服务的task.triggerTask接口正常工作
      // 4. 取消下面代码的注释
      // 5. 重新测试联赛PVE战斗功能
      /*
      const taskResult = await this.callMicroservice(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.triggerTask',
        {
          characterId,
          taskType: 39, // THIRTY_NINE 金牌任务
        }
      );
      if (XResultUtils.isFailure(taskResult)) {
        this.logger.warn(`触发任务失败: ${taskResult.message}`);
      }
      */

      // 保存数据
      const updateResult = await this.leagueRepository.updateById(characterId, leagueData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`保存联赛数据失败: ${updateResult.message}`, updateResult.code);
      }
    }

    // 获取下一个联赛和副本ID
    const nextLeagueId = this.getNextLeagueId(leagueData);
    const nextTeamCopyId = this.getnextTeamCopyId(leagueData);

    const responseData = {
      result,
      rewards,
      nextLeagueId,
      nextTeamCopyId,
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 检查联赛是否全部通关
   * 基于old项目的逻辑
   */
  private checkLeagueAllPassed(league: OneLeague): void {
    let allPassed = true;
    for (const copy of league.copyData) {
      if (copy.process < 3) {
        allPassed = false;
        break;
      }
    }
    league.isAllCopyPassed = allPassed ? 1 : 0;
  }

  /**
   * 解锁下一个副本和联赛
   * 基于old项目: LeagueCopy.prototype.teamCopyUnlock + triggerUpdateNextLeague
   */
  private async unlockNextCopyAndLeague(
    characterId: string,
    currentLeagueId: number,
    currentTeamCopyId: number,
    currentLeague: OneLeague
  ): Promise<XResult<void>> {
    // 🔧 计算下一个联赛和副本ID - 基于old项目calcNextLeagueAndTeamCopyId
    const calcResult = await this.calcNextLeagueAndTeamCopyId(currentLeagueId, currentTeamCopyId);
    if (XResultUtils.isFailure(calcResult)) {
      return XResultUtils.error(`计算下一关失败: ${calcResult.message}`, calcResult.code);
    }

    const { nextLeagueId, nextTeamCopyId } = calcResult.data;

    if (nextLeagueId > 0 && nextTeamCopyId > 0) {
      if (nextLeagueId === currentLeagueId) {
        // 🔧 同一联赛内，解锁下一个副本
        const unlockResult = await this.teamCopyUnlock(currentLeague, nextTeamCopyId);
        if (XResultUtils.isFailure(unlockResult)) {
          this.logger.warn(`解锁下一副本失败: ${unlockResult.message}`);
        }
      } else {
        // 🔧 跨联赛，需要生成下一个联赛数据
        const triggerResult = await this.triggerUpdateNextLeague(characterId, nextLeagueId, nextTeamCopyId);
        if (XResultUtils.isFailure(triggerResult)) {
          this.logger.warn(`生成下一联赛失败: ${triggerResult.message}`);
        }
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 计算下一个联赛和副本ID
   * 基于old项目: LeagueCopy.prototype.calcNextLeagueAndTeamCopyId
   */
  private async calcNextLeagueAndTeamCopyId(currentLeagueId: number, currentTeamCopyId: number): Promise<XResult<{ nextLeagueId: number; nextTeamCopyId: number }>> {
    // 获取当前联赛的所有队伍配置
    const leagueTeamConfigs = await this.gameConfig.leagueTeam.findBy('leagueId', currentLeagueId);
    if (!leagueTeamConfigs || leagueTeamConfigs.length === 0) {
      return XResultUtils.error('联赛队伍配置不存在', 'LEAGUE_TEAM_CONFIG_NOT_FOUND');
    }

    // 找到当前副本在配置中的位置
    const currentIndex = leagueTeamConfigs.findIndex(config => config.teamId === currentTeamCopyId);
    if (currentIndex === -1) {
      return XResultUtils.error('当前副本配置不存在', 'CURRENT_COPY_CONFIG_NOT_FOUND');
    }

    // 检查是否还有下一个副本
    if (currentIndex < leagueTeamConfigs.length - 1) {
      // 同一联赛内的下一个副本
      const nextConfig = leagueTeamConfigs[currentIndex + 1];
      return XResultUtils.ok({
        nextLeagueId: currentLeagueId,
        nextTeamCopyId: nextConfig.teamId
      });
    } else {
      // 当前联赛已完成，查找下一个联赛
      const allLeagueConfigs = await this.gameConfig.leagueCopy.getAll();
      const currentLeagueIndex = allLeagueConfigs.findIndex(config => config.id === currentLeagueId);

      if (currentLeagueIndex >= 0 && currentLeagueIndex < allLeagueConfigs.length - 1) {
        const nextLeagueConfig = allLeagueConfigs[currentLeagueIndex + 1];
        const nextLeagueTeamConfigs = await this.gameConfig.leagueTeam.findBy('leagueId', nextLeagueConfig.id);

        if (nextLeagueTeamConfigs && nextLeagueTeamConfigs.length > 0) {
          return XResultUtils.ok({
            nextLeagueId: nextLeagueConfig.id,
            nextTeamCopyId: nextLeagueTeamConfigs[0].teamId
          });
        }
      }

      // 没有更多联赛了
      return XResultUtils.ok({
        nextLeagueId: 0,
        nextTeamCopyId: 0
      });
    }
  }

  /**
   * 解锁指定副本
   * 基于old项目: LeagueCopy.prototype.teamCopyUnlock
   */
  private async teamCopyUnlock(league: OneLeague, copyId: number): Promise<XResult<void>> {
    const copy = league.copyData.find(c => c.teamCopyId === copyId);
    if (!copy) {
      return XResultUtils.error('副本不存在', 'COPY_NOT_FOUND');
    }

    if (copy.unlockState === 0) {
      copy.unlockState = 1;
      this.logger.debug(`副本解锁成功: 联赛${league.uid}, 副本${copyId}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 触发生成下一个联赛数据
   * 基于old项目: LeagueCopy.prototype.triggerUpdateNextLeague
   */
  private async triggerUpdateNextLeague(characterId: string, nextLeagueId: number, nextTeamCopyId: number): Promise<XResult<void>> {
    // 获取当前玩家的联赛数据
    const leagueDataResult = await this.leagueRepository.findById(characterId);
    if (XResultUtils.isFailure(leagueDataResult)) {
      return XResultUtils.error(`查询联赛数据失败: ${leagueDataResult.message}`, leagueDataResult.code);
    }

    const leagueData = leagueDataResult.data;
    if (!leagueData) {
      return XResultUtils.error('联赛数据不存在', 'LEAGUE_DATA_NOT_FOUND');
    }

    // 检查下一个联赛是否已存在
    const existingLeague = leagueData.allLeagueCopys.find(l => l.uid === nextLeagueId);
    if (existingLeague) {
      // 联赛已存在，只需解锁第一个副本
      return this.teamCopyUnlock(existingLeague, nextTeamCopyId);
    }

    // 创建新的联赛数据
    const newLeague = this.newLeague(nextLeagueId);
    const copyDataResult = await this.newLeagueCopyData(nextLeagueId);

    if (XResultUtils.isFailure(copyDataResult)) {
      return XResultUtils.error(`创建联赛副本数据失败: ${copyDataResult.message}`, copyDataResult.code);
    }

    const copyData = copyDataResult.data;
    if (copyData.length > 0) {
      newLeague.copyData = copyData;
      newLeague.count = copyData.length * 3;

      // 解锁第一个副本
      copyData[0].unlockState = 1;

      // 添加到联赛列表
      leagueData.allLeagueCopys.push(newLeague);

      // 保存数据
      const updateResult = await this.leagueRepository.updateById(characterId, leagueData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`保存联赛数据失败: ${updateResult.message}`, updateResult.code);
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 生成客户端联赛列表信息
   * 基于old项目: LeagueCopy.prototype.makeClientLeagueListInfo
   */
  private makeClientLeagueListInfo(leagueData: League): OneLeagueResponseDto[] {
    return leagueData.allLeagueCopys.map(league => ({
      uid: league.uid,
      isTakeLeagueReward: league.isTakeLeagueReward,
      isAllCopyPassed: league.isAllCopyPassed,
      curPassCount: league.curPassCount,
      count: league.count,
      copyData: league.copyData.map(copy => ({
        teamCopyId: copy.teamCopyId,
        process: copy.process,
        takeCopyRewardProcess: copy.takeCopyRewardProcess,
        isFinished: copy.isFinished,
        unlockState: copy.unlockState,
      })),
    }));
  }

  /**
   * 根据类型生成客户端联赛列表信息
   * 基于old项目: LeagueCopy.prototype.getLeagueCopyDataByType
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async makeClientLeagueListInfoByType(leagueData: League, type: number): Promise<XResult<OneLeagueResponseDto[]>> {
    const tourMatchConfig = await this.gameConfig.tourMatch.get(type);
    if (!tourMatchConfig) {
      return XResultUtils.ok([]);
    }

    const min = tourMatchConfig.leagueMin;
    const max = tourMatchConfig.leagueMax;

    if (min < 1 || max < 1) {
      return XResultUtils.ok([]);
    }

    const filteredLeagues = leagueData.allLeagueCopys
      .filter(league => league.uid >= min && league.uid <= max)
      .map(league => ({
        uid: league.uid,
        isTakeLeagueReward: league.isTakeLeagueReward,
        isAllCopyPassed: league.isAllCopyPassed,
        curPassCount: league.curPassCount,
        count: league.count,
        copyData: league.copyData.map(copy => ({
          teamCopyId: copy.teamCopyId,
          process: copy.process,
          takeCopyRewardProcess: copy.takeCopyRewardProcess,
          isFinished: copy.isFinished,
          unlockState: copy.unlockState,
        })),
      }));

    return XResultUtils.ok(filteredLeagues);
  }



  /**
   * 领取联赛奖励
   * 基于old项目: LeagueCopy.prototype.takeLeagueReward
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async takeLeagueReward(dto: TakeLeagueRewardDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`领取联赛奖励: ${dto.characterId}, 联赛${dto.leagueId}`);

      // 1. 检查联赛进度
      const progressCheckResult = await this.checkLeagueProgress(dto.characterId, dto.leagueId);
      if (XResultUtils.isFailure(progressCheckResult)) {
        return XResultUtils.error(`检查联赛进度失败: ${progressCheckResult.message}`, progressCheckResult.code);
      }

      const progressCheck = progressCheckResult.data;
      if (!progressCheck.canClaim) {
        return XResultUtils.error(progressCheck.reason, 'CANNOT_CLAIM_REWARD');
      }

      // 2. 验证奖励条件
      const rewardConfigResult = await this.getLeagueRewardConfig(dto.leagueId, progressCheck.currentStars);
      if (XResultUtils.isFailure(rewardConfigResult)) {
        return XResultUtils.error(`获取奖励配置失败: ${rewardConfigResult.message}`, rewardConfigResult.code);
      }

      const rewardConfig = rewardConfigResult.data;
      if (!rewardConfig) {
        return XResultUtils.error('未找到奖励配置', 'REWARD_CONFIG_NOT_FOUND');
      }

      // 3. 发放奖励
      const rewardResult = await this.distributeLeagueRewards(dto.characterId, rewardConfig);
      if (XResultUtils.isFailure(rewardResult)) {
        return XResultUtils.error(`奖励发放失败: ${rewardResult.message}`, rewardResult.code);
      }

      const rewardData = rewardResult.data;
      if (!rewardData.success) {
        return XResultUtils.error('奖励发放失败', 'REWARD_DISTRIBUTION_FAILED');
      }

      // 4. 更新领取状态
      const updateResult = await this.updateRewardClaimStatus(dto.characterId, dto.leagueId, progressCheck.currentStars);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新奖励领取状态失败: ${updateResult.message}`);
      }

      const responseData = {
        rewards: rewardData.rewards,
        totalStars: progressCheck.currentStars,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'take_league_reward',
      metadata: {
        characterId: dto.characterId,
        leagueId: dto.leagueId
      }
    });
  }

  /**
   * 购买联赛次数
   * 基于old项目: LeagueCopy.prototype.buyLeagueTimes
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async buyLeagueTimes(dto: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`购买联赛次数: ${dto.characterId}, 次数: ${dto.times}`);

      // 1. 参数验证
      if (!dto.characterId || !dto.times || dto.times <= 0) {
        return XResultUtils.error('参数错误', 'INVALID_PARAMETERS');
      }

      // 2. 获取联赛数据
      const leagueDataResult = await this.leagueRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(leagueDataResult)) {
        return XResultUtils.error(`查询联赛数据失败: ${leagueDataResult.message}`, leagueDataResult.code);
      }

      const leagueData = leagueDataResult.data;
      if (!leagueData) {
        return XResultUtils.error('联赛数据不存在', 'LEAGUE_DATA_NOT_FOUND');
      }

      // 3. 获取购买次数配置（使用EnergyTimes配置表）
      const buyConfig = await this.gameConfig.energyTimes.getAll();
      if (!buyConfig || buyConfig.length === 0) {
        return XResultUtils.error('购买配置不存在', 'BUY_CONFIG_NOT_FOUND');
      }

      // 4. 计算购买费用（基于old项目的费用计算逻辑）
      let totalCost = 0;
      const currentBuyTimes = leagueData.buyTimes || 0;

      for (let i = 0; i < dto.times; i++) {
        const buyIndex = currentBuyTimes + i + 1;
        const config = buyConfig.find(c => c.id === buyIndex);
        if (config) {
          totalCost += config.gold; // 使用gold字段作为费用
        } else {
          // 超出配置范围，使用最后一个配置的费用
          const lastConfig = buyConfig[buyConfig.length - 1];
          totalCost += lastConfig ? lastConfig.gold : 100;
        }
      }

      // TODO: Economy服务调用 - 已开发但尚未测试，暂时注释以避免超时错误（使用BaseService的标准化微服务调用）
      // 注意：Economy服务已开发但尚未测试，暂时注释以避免超时错误
      // 开启方法：
      // 1. 确保Economy服务已启动：npm run start:economy
      // 2. 验证Economy服务健康状态：GET http://localhost:3004/api/health
      // 3. 测试Economy服务的resource.consumeResource接口正常工作
      // 4. 取消下面代码的注释
      // 5. 重新测试购买联赛次数功能
      /*
      // 5. 调用Economy服务扣除费用
      const consumeResult = await this.callMicroservice(
        MICROSERVICE_NAMES.ECONOMY_SERVICE,
        'resource.consumeResource',
        {
          characterId: dto.characterId,
          resourceType: 'gold', // 使用金币购买
          amount: totalCost,
          source: 'league_buy_times',
          sourceId: `league_buy_${Date.now()}`,
        }
      );

      if (XResultUtils.isFailure(consumeResult)) {
        return XResultUtils.error(`资源扣除失败: ${consumeResult.message}`, consumeResult.code);
      }
      */

      // 6. 更新联赛购买次数（暂时跳过Economy调用，直接更新数据）
      leagueData.buyTimes = currentBuyTimes + dto.times;
      leagueData.lastUpdateTime = new Date();

      const updateResult = await this.leagueRepository.updateById(dto.characterId, leagueData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新联赛数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`购买联赛次数成功: ${dto.characterId}, 购买${dto.times}次, 总费用${totalCost}`);

      const responseData = {
        times: dto.times,
        totalCost: totalCost,
        newBuyTimes: leagueData.buyTimes,
        // TODO: 等Economy服务测试完成后，返回实际的资源扣除结果
        // consumeResult: consumeResult.data,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'buy_league_times',
      metadata: {
        characterId: dto.characterId,
        times: dto.times
      }
    });
  }


  /**
   * 生成敌方战斗数据
   * 根据LeagueTeam配置生成完整的敌方队伍数据
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async generateEnemyBattleData(leagueTeamDefinition: LeagueTeamDefinition): Promise<XResult<any>> {
    this.logger.log(`🔧 开始生成敌方数据: teamId=${leagueTeamDefinition.teamId}, name=${leagueTeamDefinition.name}`);

    // 🔧 正确的逻辑流程：
    // 1. 从LeagueTeamDefinition获取teamId
    // 2. 通过teamId从Team配置表获取球员基本信息（heroId, heroName等）
    // 3. 在convertStaticHeroToBattleFormat中通过heroId从HeroPve获取详细属性

    const teamDefinitions = await this.gameConfig.team.findBy('teamId', leagueTeamDefinition.teamId);
    if (!teamDefinitions || teamDefinitions.length === 0) {
      return XResultUtils.error(`未找到队伍球员配置: teamId=${leagueTeamDefinition.teamId}`, 'TEAM_HEROES_NOT_FOUND');
    }

    this.logger.log(`获取到${teamDefinitions.length}个球员基本信息`);

    // 🔧 使用battleDataService的统一转换方法
    // teamDefinitions包含球员基本信息，convertStaticHeroToBattleFormat会通过heroId获取HeroPve详细属性
    const staticConfig = {
      teamId: leagueTeamDefinition.teamId,
      teamName: leagueTeamDefinition.name || `敌方队伍${leagueTeamDefinition.teamId}`,
      level: leagueTeamDefinition.lv || 1,
      heroes: teamDefinitions, // 使用正确的变量名
      formationId: leagueTeamDefinition.formation || 442101,
      tactic: leagueTeamDefinition.offensiveId || 101,
      morale: 500
    };

    // 调用battleDataService的统一转换方法
    const battleTeamResult = await this.battleDataService.convertStaticConfigToBattleTeam(staticConfig);
    if (XResultUtils.isFailure(battleTeamResult)) {
      return XResultUtils.error(`敌方数据转换失败: ${battleTeamResult.message}`, 'ENEMY_DATA_CONVERSION_FAILED');
    }

    // 设置正确的队伍标识
    const enemyBattleTeam = battleTeamResult.data;
    enemyBattleTeam.teamSide = 'teamB' as const;
    enemyBattleTeam.isEnemy = true;

    this.logger.log(`敌方数据生成成功: 球员数量=${enemyBattleTeam.heroes.length}, 阵型=${enemyBattleTeam.formationId}`);
    return XResultUtils.ok(enemyBattleTeam);
  }

  /**
   * 计算球员属性值
   * 基于球员基础属性和等级计算最终属性
   */
  private calculateHeroAttribute(hero: any, attributeType: string, level: number): number {
    const baseValue = 50; // 基础属性值
    const levelBonus = (level || 1) * 10; // 等级加成
    const randomFactor = Math.floor(Math.random() * 20); // 随机因子

    // 根据位置调整属性
    let positionBonus = 0;
    switch (attributeType) {
      case 'attack':
        positionBonus = hero.positionName?.includes('前锋') || hero.positionName?.includes('ST') ? 30 : 10;
        break;
      case 'defend':
        positionBonus = hero.positionName?.includes('后卫') || hero.positionName?.includes('DC') || hero.positionName?.includes('DL') || hero.positionName?.includes('DR') ? 30 : 10;
        break;
      case 'speed':
        positionBonus = hero.positionName?.includes('边锋') || hero.positionName?.includes('ML') || hero.positionName?.includes('MR') ? 25 : 15;
        break;
      case 'power':
        positionBonus = hero.positionName?.includes('中锋') || hero.positionName?.includes('MC') ? 25 : 15;
        break;
      case 'technique':
        positionBonus = hero.positionName?.includes('前腰') || hero.positionName?.includes('AMC') ? 25 : 15;
        break;
      default:
        positionBonus = 15;
    }

    return baseValue + levelBonus + positionBonus + randomFactor;
  }

  /**
   * 转换位置名称为枚举值
   * 使用正确的HeroPosition枚举值
   */
  private convertPositionToEnum(positionName: string): string {
    const positionMap: { [key: string]: string } = {
      '门将': 'GK',
      '左后卫': 'DL',
      '右后卫': 'DR',
      '中后卫': 'DC',
      '后腰': 'DM',
      '左中场': 'ML',
      '右中场': 'MR',
      '中场': 'MC',
      '前腰': 'AM',      // 修复：使用AM而不是AMC
      '左边锋': 'WL',     // 修复：使用WL而不是AML
      '右边锋': 'WR',     // 修复：使用WR而不是AMR
      '中锋': 'ST',
      '前锋': 'ST'
    };

    return positionMap[positionName] || 'MC'; // 默认为中场
  }

  /**
   * 检查联赛进度
   * 基于old项目: 联赛进度检查逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async checkLeagueProgress(characterId: string, leagueId: number): Promise<XResult<any>> {
    const leagueResult = await this.leagueRepository.findById(characterId);
    if (XResultUtils.isFailure(leagueResult)) {
      return XResultUtils.error(`查询联赛数据失败: ${leagueResult.message}`, leagueResult.code);
    }

    const league = leagueResult.data;
    const leagueData = league.allLeagueCopys.find(l => l.uid === leagueId);

    if (!leagueData) {
      const progressData = { canClaim: false, reason: '联赛数据不存在' };
      return XResultUtils.ok(progressData);
    }

    // 检查是否有可领取的奖励
    const currentStars = leagueData.curPassCount || 0;
    if (currentStars <= 0) {
      const progressData = { canClaim: false, reason: '尚未获得星级' };
      return XResultUtils.ok(progressData);
    }

    // 检查是否已经领取过（基于OneLeague schema的isTakeLeagueReward字段）
    const hasClaimedReward = leagueData.isTakeLeagueReward === 1;
    if (hasClaimedReward) {
      const progressData = { canClaim: false, reason: '奖励已领取' };
      return XResultUtils.ok(progressData);
    }

    const progressData = {
      canClaim: true,
      currentStars,
      hasClaimedReward,
      newStars: currentStars
    };

    return XResultUtils.ok(progressData);
  }

  /**
   * 获取联赛奖励配置
   * 基于old项目: 联赛奖励配置获取
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getLeagueRewardConfig(leagueId: number, stars: number): Promise<XResult<any>> {
    // 🔧 从配置表获取联赛奖励配置
    const leagueConfig = await this.gameConfig.leagueCopy.get(leagueId);
    if (!leagueConfig) {
      return XResultUtils.error('联赛配置不存在', 'LEAGUE_CONFIG_NOT_FOUND');
    }

    // 🔧 基于星数计算奖励
    const rewardConfig = {
      leagueId,
      requiredStars: stars,
      rewards: []
    };

    // 根据星数等级发放不同奖励
    if (stars >= 10) {
      rewardConfig.rewards.push(
        { type: 'currency', currencyType: 'gold', amount: 5000 },
        { type: 'item', itemId: 2001, quantity: 3 }
      );
    } else if (stars >= 5) {
      rewardConfig.rewards.push(
        { type: 'currency', currencyType: 'gold', amount: 2000 },
        { type: 'item', itemId: 2001, quantity: 1 }
      );
    } else {
      rewardConfig.rewards.push(
        { type: 'currency', currencyType: 'gold', amount: 1000 }
      );
    }

    return XResultUtils.ok(rewardConfig);
  }

  /**
   * 发放联赛奖励
   * 基于old项目: 联赛奖励发放逻辑
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async distributeLeagueRewards(characterId: string, rewardConfig: any): Promise<XResult<any>> {
    const distributedRewards = [];

    for (const reward of rewardConfig.rewards) {
      if (reward.type === 'currency') {
        // 🔧 调用Character服务发放货币
        const currencyResult = await this.callMicroservice(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'character.addCurrency',
          {
            characterId,
            currencyType: reward.currencyType,
            amount: reward.amount,
            source: 'league_reward'
          }
        );

        if (XResultUtils.isSuccess(currencyResult)) {
          distributedRewards.push({
            type: 'currency',
            currencyType: reward.currencyType,
            amount: reward.amount,
          });
        }
      } else if (reward.type === 'item') {
        // 🔧 调用Character服务发放物品
        const itemResult = await this.callMicroservice(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'item.add',
          {
            characterId,
            itemId: reward.itemId,
            quantity: reward.quantity,
            source: 'league_reward'
          }
        );

        if (XResultUtils.isSuccess(itemResult)) {
          distributedRewards.push({
            type: 'item',
            itemId: reward.itemId,
            quantity: reward.quantity,
          });
        }
      }
    }

    const rewardData = { success: true, rewards: distributedRewards };
    return XResultUtils.ok(rewardData);
  }

  /**
   * 更新奖励领取状态
   * 基于old项目: 联赛奖励领取状态更新
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async updateRewardClaimStatus(characterId: string, leagueId: number, claimedStars: number): Promise<XResult<void>> {
    const leagueResult = await this.leagueRepository.findById(characterId);
    if (XResultUtils.isFailure(leagueResult)) {
      return XResultUtils.error(`查询联赛数据失败: ${leagueResult.message}`, leagueResult.code);
    }

    const league = leagueResult.data;
    const leagueData = league.allLeagueCopys.find(l => l.uid === leagueId);

    if (leagueData) {
      leagueData.isTakeLeagueReward = 1; // 标记为已领取
      const updateResult = await this.leagueRepository.updateById(characterId, league);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新奖励领取状态失败: ${updateResult.message}`, updateResult.code);
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 发放副本奖励邮件
   * 基于old项目: LeagueCopy.prototype.sendCopyRewardEmail
   */
  private async sendCopyRewardEmail(
    characterId: string,
    leagueId: number,
    teamCopyId: number,
    currentProcess: number,
    oldProcess: number
  ): Promise<XResult<any[]>> {
    const rewards = [];

    // 只有超过旧的奖励进度才有奖励
    if (currentProcess <= oldProcess) {
      return XResultUtils.ok(rewards);
    }

    // 获取副本配置
    const copyConfig = await this.getOneLeagueTeamDefinition(leagueId, teamCopyId);
    if (!copyConfig) {
      return XResultUtils.error('副本配置不存在', 'COPY_CONFIG_NOT_FOUND');
    }

    // 计算需要发放的奖励（从旧进度+1到当前进度）
    let totalExp = 0;
    for (let star = oldProcess + 1; star <= currentProcess; star++) {
      const rewardId = copyConfig[`reward${star}`];
      const rewardNum = copyConfig[`rewardNum${star}`];
      const itemType = copyConfig[`rewardType${star}`];
      const exp = copyConfig[`exp${star}`] || 0;

      totalExp += exp;

      if (rewardId > 0 && rewardNum > 0) {
        rewards.push({
          type: itemType || 'item',
          itemId: rewardId,
          quantity: rewardNum,
          star: star
        });
      }
    }

    // 添加经验奖励
    if (totalExp > 0) {
      rewards.push({
        type: 'exp',
        amount: totalExp
      });

      // 调用Character服务发放经验
      await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.addExp',
        { characterId, exp: totalExp }
      );
    }

    // 发放物品奖励
    for (const reward of rewards) {
      if (reward.type === 'item' && reward.itemId && reward.quantity) {
        await this.callMicroservice(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'item.add',
          {
            characterId,
            itemId: reward.itemId,
            quantity: reward.quantity,
            source: 'league_copy'
          }
        );
      }
    }

    return XResultUtils.ok(rewards);
  }

  /**
   * 触发联赛任务
   * 基于old项目: 任务系统触发逻辑
   */
  private async triggerLeagueTasks(
    characterId: string,
    leagueId: number,
    teamCopyId: number,
    finishedStar: number,
    totalStars: number
  ): Promise<XResult<void>> {
    // 调用Activity服务触发任务
    const taskCalls = [
      // 大力推图任务
      this.callMicroservice(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.trigger',
        {
          characterId,
          taskType: 'LEAGUE_PUSH', // TARGET_TYPE.FOUR
          params: {}
        }
      ),
      // 通过某个关卡任务
      this.callMicroservice(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.trigger',
        {
          characterId,
          taskType: 'LEAGUE_COPY_PASS', // TARGET_TYPE.ONE
          params: { teamCopyId, stars: finishedStar }
        }
      ),
      // 通关整个联赛任务
      this.callMicroservice(
        MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        'task.trigger',
        {
          characterId,
          taskType: 'LEAGUE_TOTAL_STARS', // TARGET_TYPE.TWO
          params: { leagueId, totalStars }
        }
      )
    ];

    // 并行执行所有任务触发
    await Promise.allSettled(taskCalls);

    return XResultUtils.ok(undefined);
  }
}
