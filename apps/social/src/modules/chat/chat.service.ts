import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ChatMessageType, ChatMessageStatus } from '../../common/schemas/chat.schema';
import {
  SendChatMessageDto,
  GetChatHistoryDto,
  GetPrivateChatDto,
  ChatMessageResponseDto,
  ChatHistoryResponseDto
} from '../../common/dto/chat.dto';
import {ChatChannelRepository} from "@social/common/repositories/chat-channel.repository";
import {ChatMessageRepository} from "@social/common/repositories/chat-message.repository";

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { MicroserviceClientService } from '@libs/service-mesh';
import { PushClientService } from '@libs/common/push/push-client.service';

/**
 * 聊天系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 发送聊天消息
 * - 获取聊天历史
 * - 私聊消息管理
 * - 频道消息管理
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理
 */
@Injectable()
export class ChatService extends BaseService {

  constructor(
    private readonly chatChannelRepository: ChatChannelRepository,
    private readonly chatMessageRepository: ChatMessageRepository,
    private readonly pushClientService: PushClientService,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('ChatService', microserviceClient);
  }

  /**
   * 发送聊天消息
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async sendMessage(messageData: SendChatMessageDto): Promise<XResult<ChatMessageResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`发送聊天消息: ${messageData.senderId} -> ${messageData.channelId}`);

      // 验证私聊消息必须有接收者
      if (messageData.messageType === ChatMessageType.PRIVATE) {
        if (!messageData.receiverId || !messageData.receiverName) {
          return XResultUtils.error('私聊消息必须指定接收者', 'PRIVATE_MESSAGE_MISSING_RECEIVER');
        }
      }

      // 生成消息ID
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sendTime = Date.now();

      // 创建消息数据
      const chatMessage = {
        messageId,
        senderId: messageData.senderId,
        senderName: messageData.senderName,
        channelId: messageData.channelId,
        messageType: messageData.messageType,
        content: messageData.content,
        receiverId: messageData.receiverId,
        receiverName: messageData.receiverName,
        extraData: messageData.extraData,
        status: ChatMessageStatus.SENT,
        sendTime,
        readTime: 0,
        serverId: messageData.serverId,
      };

      // 保存消息到数据库
      const savedMessageResult = await this.chatMessageRepository.createMessage(chatMessage);
      if (XResultUtils.isFailure(savedMessageResult)) {
        return XResultUtils.error(`保存消息失败: ${savedMessageResult.message}`, savedMessageResult.code);
      }

      const savedMessage = savedMessageResult.data;

      // 更新频道最后消息信息
      const updateChannelResult = await this.chatChannelRepository.updateChannelLastMessage(
        messageData.channelId,
        messageData.content,
        sendTime
      );

      if (XResultUtils.isFailure(updateChannelResult)) {
        this.logger.warn(`更新频道最后消息失败: ${updateChannelResult.message}`);
      }

      this.logger.log(`聊天消息发送成功: ${messageId}`);

      const responseData: ChatMessageResponseDto = {
        messageId: savedMessage.messageId,
        senderId: savedMessage.senderId,
        senderName: savedMessage.senderName,
        channelId: savedMessage.channelId,
        messageType: savedMessage.messageType,
        content: savedMessage.content,
        receiverId: savedMessage.receiverId,
        receiverName: savedMessage.receiverName,
        extraData: savedMessage.extraData,
        status: savedMessage.status,
        sendTime: savedMessage.sendTime,
        readTime: savedMessage.readTime,
      };

      // 发布聊天消息事件
      await this.publishChatMessageEvent(messageData, responseData);

      return XResultUtils.ok(responseData);
    }, {
      reason: 'send_chat_message',
      metadata: {
        senderId: messageData.senderId,
        channelId: messageData.channelId,
        messageType: messageData.messageType
      }
    });
  }

  /**
   * 获取聊天历史
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getChatHistory(params: GetChatHistoryDto): Promise<XResult<ChatHistoryResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取聊天历史: ${params.channelId}`);

      const limit = params.limit || 50;
      const messagesResult = await this.chatMessageRepository.getMessagesByChannel(
        params.channelId,
        limit + 1, // 多获取一条来判断是否还有更多
        params.before
      );

      if (XResultUtils.isFailure(messagesResult)) {
        return XResultUtils.error(`获取消息失败: ${messagesResult.message}`, messagesResult.code);
      }

      const messages = messagesResult.data;
      const hasMore = messages.length > limit;
      const actualMessages = hasMore ? messages.slice(0, limit) : messages;

      // 转换为响应格式
      const messageResponses: ChatMessageResponseDto[] = actualMessages.map(msg => ({
        messageId: msg.messageId,
        senderId: msg.senderId,
        senderName: msg.senderName,
        channelId: msg.channelId,
        messageType: msg.messageType,
        content: msg.content,
        receiverId: msg.receiverId,
        receiverName: msg.receiverName,
        extraData: msg.extraData,
        status: msg.status,
        sendTime: msg.sendTime,
        readTime: msg.readTime,
      }));

      const responseData: ChatHistoryResponseDto = {
        messages: messageResponses.reverse(), // 按时间正序返回
        total: messageResponses.length,
        channelId: params.channelId,
        hasMore,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_chat_history',
      metadata: {
        channelId: params.channelId,
        limit: params.limit
      }
    });
  }

  /**
   * 获取私聊历史
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getPrivateChatHistory(params: GetPrivateChatDto): Promise<XResult<ChatHistoryResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取私聊历史: ${params.senderId} <-> ${params.receiverId}`);

      const limit = params.limit || 50;
      const messagesResult = await this.chatMessageRepository.getPrivateMessages(
        params.senderId,
        params.receiverId,
        limit + 1,
        params.before
      );

      if (XResultUtils.isFailure(messagesResult)) {
        return XResultUtils.error(`获取私聊消息失败: ${messagesResult.message}`, messagesResult.code);
      }

      const messages = messagesResult.data;
      const hasMore = messages.length > limit;
      const actualMessages = hasMore ? messages.slice(0, limit) : messages;

      // 转换为响应格式
      const messageResponses: ChatMessageResponseDto[] = actualMessages.map(msg => ({
        messageId: msg.messageId,
        senderId: msg.senderId,
        senderName: msg.senderName,
        channelId: msg.channelId,
        messageType: msg.messageType,
        content: msg.content,
        receiverId: msg.receiverId,
        receiverName: msg.receiverName,
        extraData: msg.extraData,
        status: msg.status,
        sendTime: msg.sendTime,
        readTime: msg.readTime,
      }));

      const responseData: ChatHistoryResponseDto = {
        messages: messageResponses.reverse(), // 按时间正序返回
        total: messageResponses.length,
        channelId: `private_${params.senderId}_${params.receiverId}`,
        hasMore,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_private_chat_history',
      metadata: {
        senderId: params.senderId,
        receiverId: params.receiverId,
        limit: params.limit
      }
    });
  }

  /**
   * 加入聊天频道
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async joinChannel(characterId: string, channelId: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`加入聊天频道: ${characterId} -> ${channelId}`);

      // 确保频道存在
      const channelResult = await this.chatChannelRepository.findChannelById(channelId);
      if (XResultUtils.isFailure(channelResult)) {
        return XResultUtils.error(`查询频道失败: ${channelResult.message}`, channelResult.code);
      }

      let channel = channelResult.data;

      if (!channel) {
        // 创建频道（根据频道ID判断类型）
        let channelType = ChatMessageType.WORLD;
        let channelName = '世界频道';

        if (channelId.startsWith('guild_')) {
          channelType = ChatMessageType.GUILD;
          channelName = '公会频道';
        } else if (channelId.startsWith('private_')) {
          channelType = ChatMessageType.PRIVATE;
          channelName = '私聊';
        }

        const createChannelResult = await this.chatChannelRepository.getOrCreateChannel({
          channelId,
          channelName,
          channelType,
          members: [],
          memberCount: 0,
          maxMembers: channelType === ChatMessageType.GUILD ? 100 :
                     channelType === ChatMessageType.PRIVATE ? 2 : 1000,
          isActive: true,
          isPrivate: channelType === ChatMessageType.PRIVATE,
          messageCount: 0,
          lastMessageTime: 0,
          serverId,
        });

        if (XResultUtils.isFailure(createChannelResult)) {
          return XResultUtils.error(`创建频道失败: ${createChannelResult.message}`, createChannelResult.code);
        }

        channel = createChannelResult.data;
      }

      // 加入频道
      const updatedChannelResult = await this.chatChannelRepository.joinChannel(channelId, characterId);
      if (XResultUtils.isFailure(updatedChannelResult)) {
        return XResultUtils.error(`加入频道失败: ${updatedChannelResult.message}`, updatedChannelResult.code);
      }

      const updatedChannel = updatedChannelResult.data;

      const responseData = {
        characterId,
        channelId,
        channelName: channel.channelName,
        channelType: channel.channelType,
        memberCount: updatedChannel?.memberCount || channel.memberCount,
        status: 'joined',
        joinedAt: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'join_chat_channel',
      metadata: {
        characterId,
        channelId,
        serverId
      }
    });
  }

  /**
   * 离开聊天频道
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async leaveChannel(characterId: string, channelId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`离开聊天频道: ${characterId} -> ${channelId}`);

      // 离开频道
      const updatedChannelResult = await this.chatChannelRepository.leaveChannel(channelId, characterId);

      if (XResultUtils.isFailure(updatedChannelResult)) {
        return XResultUtils.error(`离开频道失败: ${updatedChannelResult.message}`, updatedChannelResult.code);
      }

      const updatedChannel = updatedChannelResult.data;
      if (!updatedChannel) {
        return XResultUtils.error('频道不存在或用户不在频道中', 'CHANNEL_NOT_FOUND_OR_NOT_MEMBER');
      }

      const responseData = {
        characterId,
        channelId,
        channelName: updatedChannel.channelName,
        memberCount: updatedChannel.memberCount,
        status: 'left',
        leftAt: Date.now(),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'leave_chat_channel',
      metadata: {
        characterId,
        channelId
      }
    });
  }

  // ==================== 事件发布方法 ====================

  /**
   * 发送聊天消息推送通知
   */
  private async publishChatMessageEvent(messageData: SendChatMessageDto, responseData: ChatMessageResponseDto): Promise<void> {
    try {
      if (messageData.messageType === ChatMessageType.PRIVATE && messageData.receiverId) {
        // 🎯 私聊消息：使用新的角色推送API
        await this.pushClientService.sendToCharacter(messageData.receiverId, {
          eventName: 'private_message',
          payload: {
            messageId: responseData.messageId,
            senderId: responseData.senderId,
            senderName: responseData.senderName,
            content: responseData.content,
            sendTime: responseData.sendTime,
            channelId: responseData.channelId,
            messageType: responseData.messageType,
          },
          businessType: 'social'
        });

        this.logger.debug(`💬 Private message sent: ${messageData.senderId} -> ${messageData.receiverId}`);

      } else if (messageData.channelId) {
        // 🎯 频道消息：使用房间推送API
        let roomId = '';

        // 根据频道类型构建房间ID
        if (messageData.channelId.startsWith('world')) {
          roomId = 'channel:world';
        } else if (messageData.channelId.startsWith('guild_')) {
          roomId = `channel:guild:${messageData.channelId.replace('guild_', '')}`;
        } else if (messageData.channelId.startsWith('server_')) {
          roomId = `channel:server:${messageData.serverId}`;
        } else {
          // 自定义频道
          roomId = `channel:custom:${messageData.channelId}`;
        }

        if (roomId) {
          await this.pushClientService.pushToRoom(roomId, {
            eventName: 'channel_message',
            payload: {
              messageId: responseData.messageId,
              channel: messageData.channelId,
              message: responseData.content,
              sender: {
                characterId: responseData.senderId,
                characterName: responseData.senderName,
                avatar: '', // TODO: 从角色服务获取头像
              },
              type: 'chat',
              timestamp: new Date(responseData.sendTime),
            }
          });

          this.logger.debug(`💬 Channel message sent to room: ${roomId}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to send chat message notification: ${error.message}`);
    }
  }
}
