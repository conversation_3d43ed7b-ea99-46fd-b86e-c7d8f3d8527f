Stack trace:
Frame         Function      Args
0007FFFFBFA0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBFA0, 0007FFFFAEA0) msys-2.0.dll+0x1FE8E
0007FFFFBFA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC278) msys-2.0.dll+0x67F9
0007FFFFBFA0  000210046832 (000210286019, 0007FFFFBE58, 0007FFFFBFA0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBFA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBFA0  000210068E24 (0007FFFFBFB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC280  00021006A225 (0007FFFFBFB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA98060000 ntdll.dll
7FFA96C50000 KERNEL32.DLL
7FFA95980000 KERNELBASE.dll
7FFA97E50000 USER32.dll
7FFA95950000 win32u.dll
7FFA97020000 GDI32.dll
7FFA954C0000 gdi32full.dll
7FFA95740000 msvcp_win.dll
7FFA955F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA95EC0000 advapi32.dll
7FFA95FA0000 msvcrt.dll
7FFA96E80000 sechost.dll
7FFA97BA0000 RPCRT4.dll
7FFA94B00000 CRYPTBASE.DLL
7FFA958B0000 bcryptPrimitives.dll
7FFA97E10000 IMM32.DLL
