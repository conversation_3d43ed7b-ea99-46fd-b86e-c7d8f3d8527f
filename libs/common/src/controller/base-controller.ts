import { Controller, Logger } from '@nestjs/common';
import { Payload } from '@nestjs/microservices';
import { XResult, XResultUtils, XResponseUtils, XResponse, PaginationResult } from '../types/result.type';
import { InjectedContext } from '../types';
import { validate, ValidationError } from 'class-validator';
import { plainToInstance } from 'class-transformer';

/**
 * 标准Payload接口
 */
export interface BasePayload {
  /** 角色ID */
  characterId?: string;
  /** 服务器ID */
  serverId?: string;
  /** 注入的上下文 */
  injectedContext?: InjectedContext;
}

/**
 * 分页Payload接口
 */
export interface PaginationPayload extends BasePayload {
  /** 页码 */
  page?: number;
  /** 每页记录数 */
  limit?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 控制器选项
 */
export interface ControllerOptions {
  /** 是否启用请求日志 */
  enableRequestLogging?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 默认超时时间（毫秒） */
  defaultTimeout?: number;
  /** 是否自动处理异常 */
  autoHandleExceptions?: boolean;
}

/**
 * 通用BaseController基类
 * 
 * 🎯 设计目标：
 * - 统一的微服务接口处理
 * - 标准化的响应格式
 * - 完整的Result模式集成
 * - 通用的错误处理和日志记录
 * - 缓存装饰器支持
 * - 参数验证框架
 * 
 * 🚀 核心特性：
 * - 统一的MessagePattern处理
 * - 标准化的响应格式转换
 * - 完整的请求日志记录
 * - 性能监控支持
 * - 异常处理框架
 * - 参数提取和验证
 * 
 * 📖 使用示例：
 * ```typescript
 * @Controller()
 * export class CharacterController extends BaseController {
 *   constructor(private readonly characterService: CharacterService) {
 *     super('CharacterController');
 *   }
 * 
 *   @MessagePattern('character.create')
 *   async createCharacter(@Payload() payload: CreateCharacterPayload) {
 *     return this.handleRequest(async () => {
 *       const result = await this.characterService.createCharacter(payload.createDto);
 *       return this.toSuccessResponse(result, '角色创建成功');
 *     }, payload);
 *   }
 * }
 * ```
 */
@Controller()
export abstract class BaseController {
  protected readonly logger: Logger;
  protected readonly options: ControllerOptions;

  constructor(
    protected readonly controllerName: string,
    options: ControllerOptions = {}
  ) {
    this.logger = new Logger(controllerName);
    this.options = {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      defaultTimeout: 30000,
      autoHandleExceptions: true,
      ...options
    };
  }

  // ========== 核心请求处理方法 ==========

  /**
   * 处理微服务请求（带日志和异常处理）
   *
   * 🎯 **使用场景**：
   * - 需要特殊的请求日志记录
   * - 需要性能监控和慢请求检测
   * - 需要统一的异常处理
   * - 复杂的请求处理流程
   *
   * ⚠️ **重要提醒**：
   * - 简单的CRUD接口请直接使用fromResult，不要使用此方法
   * - 此方法会增加调用栈和日志开销，仅在必要时使用
   * - handler函数应该返回XResponse<T>，而不是XResult<T>
   * - 🔥 requestId由Gateway层统一管理，此处不再处理
   *
   * 📋 **使用决策**：
   * ```
   * 是否使用handleRequest？
   * ├─ 简单CRUD接口？ → ❌ 直接使用fromResult(service.method())
   * ├─ 需要特殊日志记录？ → ✅ 使用handleRequest
   * ├─ 需要性能监控？ → ✅ 使用handleRequest
   * ├─ 复杂的请求处理？ → ✅ 使用handleRequest
   * └─ 需要统一异常处理？ → ✅ 使用handleRequest
   * ```
   *
   * 📖 **使用示例**：
   * ```typescript
   * // ✅ 适合场景：需要特殊监控的复杂操作
   * @MessagePattern('character.complexOperation')
   * async performComplexOperation(@Payload() payload: ComplexPayload) {
   *   return this.handleRequest(async () => {
   *     this.logger.log('开始执行复杂操作', { userId: payload.userId });
   *
   *     const result = await this.characterService.performComplexOperation(payload);
   *
   *     if (XResultUtils.isSuccess(result)) {
   *       this.logger.log('复杂操作执行成功', { userId: payload.userId });
   *     }
   *
   *     return this.fromResult(result);
   *   }, payload);
   * }
   *
   * // ✅ 正确做法：简单接口直接使用fromResult
   * @MessagePattern('character.get')
   * async getCharacter(@Payload() payload: GetCharacterPayload) {
   *   const result = await this.characterService.getCharacter(payload.id);
   *   return this.fromResult(result);
   * }
   * ```
   *
   * @param handler 请求处理函数，返回XResponse<T>
   * @param payload 请求数据（用于日志记录）
   * @returns Promise<XResponse<T>> 微服务响应
   */
  protected async handleRequest<T>(
    handler: () => Promise<XResponse<T>>,
    payload?: any
  ): Promise<XResponse<T>> {
    const startTime = Date.now();

    try {
      if (this.options.enableRequestLogging) {
        this.logRequestStart('handleRequest', payload);
      }

      const response = await handler();

      if (this.options.enablePerformanceMonitoring) {
        this.logRequestEnd('handleRequest', startTime, true);
      }

      return {
        ...response,
        timestamp: Date.now()
      };

    } catch (error) {
      this.logRequestError('handleRequest', startTime, error);

      if (this.options.autoHandleExceptions) {
        return this.toErrorResponse(
          `请求处理失败: ${error.message}`,
          'UNKNOWN_ERROR',
          { stack: error.stack }
        );
      }
      throw error;
    }
  }

  /**
   * 处理分页请求
   * @param handler 分页处理函数
   * @param payload 分页请求数据
   */
  protected async handlePaginationRequest<T>(
    handler: (paginationOptions: any) => Promise<XResult<any>>,
    payload: PaginationPayload
  ): Promise<XResponse<T>> {
    return this.handleRequest(async () => {
      const paginationOptions = this.extractPaginationOptions(payload);
      const result = await handler(paginationOptions);
      return this.fromResult(result);
    }, payload);
  }

  // ========== 响应格式转换方法 ==========

  /**
   * 从Result转换为微服务响应（推荐的标准方法）
   *
   * 🎯 **使用场景**：
   * - 所有简单的CRUD接口（推荐）
   * - 标准的业务逻辑调用
   * - 不需要特殊日志记录的接口
   * - 大部分微服务接口的标准做法
   *
   * ✅ **优势**：
   * - 轻量级，无额外开销
   * - 代码简洁，易于维护
   * - 统一的响应格式
   * - 完美的类型安全
   * - 🔥 简化设计，requestId由Gateway层统一管理
   *
   * 📖 **使用示例**：
   * ```typescript
   * // ✅ 标准用法：简单接口
   * @MessagePattern('character.get')
   * async getCharacter(@Payload() payload: GetCharacterPayload) {
   *   const result = await this.characterService.getCharacter(payload.id);
   *   return this.fromResult(result);
   * }
   *
   * // ✅ 带参数验证的接口
   * @MessagePattern('character.create')
   * async createCharacter(@Payload() payload: CreateCharacterPayload) {
   *   const validation = await this.validatePayloadWithDto(payload, CreateCharacterDto);
   *   if (XResultUtils.isFailure(validation)) {
   *     return this.fromResult(validation);
   *   }
   *
   *   const result = await this.characterService.createCharacter(validation.data);
   *   return this.fromResult(result);
   * }
   * ```
   *
   * @param result Service层返回的XResult对象
   * @param message 自定义成功消息（可选）
   * @returns XResponse<T> 标准的微服务响应格式
   */
  protected fromResult<T>(result: XResult<T>, message?: string): XResponse<T> {
    return XResponseUtils.fromResult(result,message);
  }

  /**
   * 创建成功响应
   * 🔥 优化：简化参数，移除requestId管理
   * @param data 响应数据
   * @param message 响应消息
   */
  protected toSuccessResponse<T>(
    data: T,
    message: string = '操作成功'
  ): XResponse<T> {
    return XResponseUtils.success(data, message);
  }

  /**
   * 创建错误响应
   * 🔥 优化：支持details参数，移除requestId管理
   *
   * @param message 错误消息（必需）
   * @param code 错误码（可选，默认'UNKNOWN_ERROR'）
   * @param details 错误详情（可选）- 用于提供额外的错误上下文信息
   *
   * 使用示例：
   * ```typescript
   * // 基础用法
   * return this.toErrorResponse('用户不存在');
   *
   * // 指定错误码
   * return this.toErrorResponse('用户不存在', 'USER_NOT_FOUND');
   *
   * // 带详细错误信息
   * return this.toErrorResponse('验证失败', 'VALIDATION_ERROR', { field: 'email' });
   * ```
   */
  protected toErrorResponse<T = null>(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    details?: any
  ): XResponse<T> {
    return XResponseUtils.error(code, message, details);
  }

  /**
   * 创建分页响应
   * 🔥 优化：简化参数，移除requestId管理
   * @param data 分页数据
   * @param message 响应消息
   */
  protected toPaginationResponse<T>(
    data: PaginationResult<T>,
    message: string = '获取成功'
  ): XResponse<PaginationResult<T>> {
    return XResponseUtils.success(data, message);
  }

  // ========== 参数提取和验证方法 ==========

  /**
   * 提取基础参数
   * @param payload 请求数据
   */
  protected extractBaseParams(payload: BasePayload): {
    characterId?: string;
    serverId?: string;
    injectedContext?: InjectedContext;
  } {
    return {
      characterId: payload.characterId,
      serverId: payload.serverId,
      injectedContext: payload.injectedContext
    };
  }

  /**
   * 提取分页参数
   * @param payload 分页请求数据
   */
  protected extractPaginationOptions(payload: PaginationPayload): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    return {
      page: payload.page || 1,
      limit: Math.min(payload.limit || 20, 100), // 限制最大每页数量
      sortBy: payload.sortBy,
      sortOrder: payload.sortOrder || 'desc'
    };
  }

  /**
   * 验证必需参数（基础版本）
   * @param payload 请求数据
   * @param requiredFields 必需字段列表
   */
  protected validateRequiredFields(
    payload: any,
    requiredFields: string[]
  ): XResult<void> {
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      if (!payload || payload[field] === undefined || payload[field] === null) {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return XResultUtils.error(
        `缺少必需参数: ${missingFields.join(', ')}`,
        'MISSING_REQUIRED_FIELDS'
      );
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 增强的参数验证方法 - 支持DTO验证和深度结构检查
   * @param payload 请求数据
   * @param dtoClass DTO类构造函数
   * @param requiredFields 必需字段列表（可选，用于额外的字段存在性检查）
   * @param validationOptions 验证选项
   */
  protected async validatePayloadWithDto<T>(
    payload: any,
    dtoClass: new () => T,
    requiredFields?: string[],
    validationOptions?: {
      skipMissingProperties?: boolean;
      whitelist?: boolean;
      forbidNonWhitelisted?: boolean;
      stopAtFirstError?: boolean;
      transform?: boolean;
    }
  ): Promise<XResult<T>> {
    try {
      // 🔍 调试日志：开始BaseController验证
      this.logger.debug('🔍 [BaseController] 开始validatePayloadWithDto', {
        payload: JSON.stringify(payload),
        dtoClassName: dtoClass.name,
        requiredFields,
        validationOptions,
      });

      // 1. 基础字段存在性检查（如果提供了requiredFields）
      if (requiredFields && requiredFields.length > 0) {
        this.logger.debug('📝 [BaseController] 执行基础字段检查', { requiredFields });

        const fieldValidation = this.validateRequiredFields(payload, requiredFields);
        if (XResultUtils.isFailure(fieldValidation)) {
          this.logger.warn('❌ [BaseController] 基础字段检查失败', {
            fieldValidation,
            missingFields: requiredFields.filter(field =>
              !payload || payload[field] === undefined || payload[field] === null
            )
          });
          return fieldValidation as XResult<T>;
        }

        this.logger.debug('✅ [BaseController] 基础字段检查通过');
      }

      // 2. DTO结构验证和转换
      const options = {
        enableImplicitConversion: validationOptions?.transform ?? true,
        excludeExtraneousValues: validationOptions?.whitelist ?? true
      };

      this.logger.debug('🔄 [BaseController] 开始DTO转换', {
        transformOptions: options,
        originalPayload: JSON.stringify(payload),
      });

      const dto = plainToInstance(dtoClass, payload, options);

      this.logger.debug('🔄 [BaseController] DTO转换完成', {
        transformedDto: JSON.stringify(dto),
        dtoConstructor: dto?.constructor?.name,
        isInstanceOfTarget: dto instanceof dtoClass,
      });

      // 3. class-validator验证
      const validationConfig = {
        skipMissingProperties: validationOptions?.skipMissingProperties ?? false,
        whitelist: validationOptions?.whitelist ?? true,
        forbidNonWhitelisted: validationOptions?.forbidNonWhitelisted ?? true,
        stopAtFirstError: validationOptions?.stopAtFirstError ?? false
      };

      this.logger.debug('✅ [BaseController] 开始class-validator验证', {
        validationConfig,
        dtoToValidate: JSON.stringify(dto),
      });

      const errors = await validate(dto, validationConfig);

      this.logger.debug('📊 [BaseController] 验证结果', {
        errorCount: errors.length,
        hasErrors: errors.length > 0,
        errorDetails: errors.map(err => ({
          property: err.property,
          value: err.value,
          constraints: Object.keys(err.constraints || {}),
        }))
      });

      if (errors.length > 0) {
        const errorMessages = this.formatValidationErrors(errors);

        this.logger.error('❌ [BaseController] DTO验证失败', {
          errorCount: errors.length,
          errorMessages,
          detailedErrors: errors.map(err => ({
            property: err.property,
            value: err.value,
            valueType: typeof err.value,
            constraints: err.constraints,
          }))
        });

        return XResultUtils.error(
          `DTO验证失败: ${errorMessages.join('; ')}`,
          'DTO_VALIDATION_FAILED'
        );
      }

      this.logger.debug('✅ [BaseController] 验证成功', {
        finalDto: JSON.stringify(dto),
        resultType: typeof dto,
      });

      return XResultUtils.ok(dto as T);
    } catch (error) {
      this.logger.error('💥 [BaseController] 参数验证异常', {
        error: error.message,
        stack: error.stack,
        payload: JSON.stringify(payload),
        dtoClass: dtoClass.name,
        validationOptions,
      });

      return XResultUtils.error(
        `参数验证异常: ${error.message}`,
        'VALIDATION_EXCEPTION'
      );
    }
  }

  /**
   * 格式化验证错误信息
   * @param errors 验证错误数组
   */
  private formatValidationErrors(errors: ValidationError[]): string[] {
    const messages: string[] = [];

    const formatError = (error: ValidationError, prefix = ''): void => {
      const fieldPath = prefix ? `${prefix}.${error.property}` : error.property;

      if (error.constraints) {
        Object.values(error.constraints).forEach(message => {
          messages.push(`${fieldPath}: ${message}`);
        });
      }

      // 处理嵌套验证错误
      if (error.children && error.children.length > 0) {
        error.children.forEach(child => {
          formatError(child, fieldPath);
        });
      }
    };

    errors.forEach(error => formatError(error));
    return messages;
  }

  /**
   * 验证嵌套对象字段
   * @param payload 请求数据
   * @param fieldPath 字段路径（支持点号分隔的嵌套路径，如 'user.profile.name'）
   * @param validator 自定义验证函数
   */
  protected validateNestedField<T>(
    payload: any,
    fieldPath: string,
    validator?: (value: any) => boolean | string
  ): XResult<T> {
    const pathParts = fieldPath.split('.');
    let current = payload;

    // 遍历嵌套路径
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (!current || typeof current !== 'object') {
        return XResultUtils.error(
          `字段路径 '${fieldPath}' 在 '${pathParts.slice(0, i).join('.')}' 处中断`,
          'INVALID_NESTED_FIELD_PATH'
        );
      }

      if (current[part] === undefined || current[part] === null) {
        return XResultUtils.error(
          `嵌套字段 '${fieldPath}' 不存在或为空`,
          'MISSING_NESTED_FIELD'
        );
      }

      current = current[part];
    }

    // 执行自定义验证
    if (validator) {
      const validationResult = validator(current);
      if (validationResult !== true) {
        const errorMessage = typeof validationResult === 'string'
          ? validationResult
          : `字段 '${fieldPath}' 验证失败`;

        return XResultUtils.error(errorMessage, 'CUSTOM_VALIDATION_FAILED');
      }
    }

    return XResultUtils.ok(current);
  }

  /**
   * 验证角色ID
   * @param characterId 角色ID
   */
  protected validateCharacterId(characterId?: string): XResult<string> {
    if (!characterId || characterId.trim().length === 0) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }
    return XResultUtils.ok(characterId);
  }

  /**
   * 验证服务器ID
   * @param serverId 服务器ID
   */
  protected validateServerId(serverId?: string): XResult<string> {
    if (!serverId || serverId.trim().length === 0) {
      return XResultUtils.error('服务器ID不能为空', 'INVALID_SERVER_ID');
    }
    return XResultUtils.ok(serverId);
  }

  // ========== 工具方法 ==========




  /**
   * 清理敏感数据（用于日志记录）
   * @param payload 请求数据
   */
  protected sanitizeForLogging(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'injectedContext'];
    const sanitized = { ...payload };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  // ========== 日志记录方法 ==========

  /**
   * 记录请求开始
   */
  protected logRequestStart(requestId: string, payload?: any): void {
    this.logger.log(`请求开始: ${requestId}`, {
      payload: this.sanitizeForLogging(payload)
    });
  }

  /**
   * 记录请求结束
   */
  protected logRequestEnd(requestId: string, startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    const level = success ? 'log' : 'warn';
    this.logger[level](`请求${success ? '成功' : '失败'}: ${requestId} - 耗时 ${duration}ms`);

    if (duration > 3000) {
      this.logger.warn(`慢请求检测: ${requestId} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录请求错误
   */
  protected logRequestError(requestId: string, startTime: number, error: any): void {
    const duration = Date.now() - startTime;
    this.logger.error(`请求异常: ${requestId} - 耗时 ${duration}ms`, {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 获取控制器名称
   */
  protected getControllerName(): string {
    return this.controllerName;
  }
}
