/**
 * 微服务专用验证管道
 * 
 * 🎯 设计目标：
 * - 专为@MessagePattern接口设计的参数验证
 * - 使用RpcException而不是HttpException
 * - 支持详细的验证错误信息
 * - 集成class-validator和class-transformer
 * - 提供灵活的验证配置选项
 * 
 * 📖 使用示例：
 * ```typescript
 * @MessagePattern('character.update')
 * @UsePipes(MicroserviceValidationPipe)
 * async updateCharacter(@Payload() payload: UpdateCharacterPayload) {
 *   // payload已经通过验证和转换
 *   return this.characterService.updateCharacter(payload);
 * }
 * ```
 * - 或在@Controller层统一处理验证和转换
 * ```typescript
 * @Controller()
 * @UsePipes(MicroserviceValidationPipe)
 * export class HeroController {
 *   @MessagePattern('hero.update')
 *   async updateHero(@Payload() payload: UpdatePayloadDto) {
 *     // payload已经通过验证和转换
 *     return this.heroService.updateHero(payload.heroId, payload);
 *   }
 * }
 * 
 * @MessagePattern('hero.special')
 * @UsePipes(StrictMicroserviceValidationPipe) // 覆盖为更严格的验证
 * async specialMethod(@Payload() payload: SpecialPayloadDto) {
 *   // 使用不同的验证规则
 * }
 * 
 * ```
 */

import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  Logger,
} from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { validate, ValidationError } from 'class-validator';
import { plainToInstance } from 'class-transformer';

/**
 * 微服务验证管道选项
 */
export interface MicroserviceValidationOptions {
  /** 是否跳过缺失属性的验证 */
  skipMissingProperties?: boolean;
  /** 是否启用白名单模式（只保留装饰器标记的属性） */
  whitelist?: boolean;
  /** 是否禁止非白名单属性 */
  forbidNonWhitelisted?: boolean;
  /** 是否启用自动类型转换 */
  transform?: boolean;
  /** 是否在第一个错误时停止验证 */
  stopAtFirstError?: boolean;
  /** 验证组 */
  groups?: string[];
  /** 是否总是验证 */
  always?: boolean;
  /** 是否启用严格组模式 */
  strictGroups?: boolean;
  /** 是否禁用默认错误消息 */
  dismissDefaultMessages?: boolean;
  /** 自定义异常工厂 */
  exceptionFactory?: (errors: ValidationError[]) => RpcException;
  /** 是否验证injectedContext字段 */
  validateInjectedContext?: boolean;
  /** 需要跳过验证的字段列表 */
  skipValidationFields?: string[];
}

/**
 * 微服务验证管道实现
 */
@Injectable()
export class MicroserviceValidationPipe implements PipeTransform<any> {
  private readonly logger = new Logger(MicroserviceValidationPipe.name);
  private readonly options: Required<MicroserviceValidationOptions>;

  constructor(options?: MicroserviceValidationOptions) {
    this.options = {
      skipMissingProperties: false,
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      stopAtFirstError: false,
      groups: [],
      always: false,
      strictGroups: false,
      dismissDefaultMessages: false,
      exceptionFactory: this.defaultExceptionFactory.bind(this),
      validateInjectedContext: false, // 默认不验证injectedContext
      skipValidationFields: ['characterId', 'injectedContext'], // 默认跳过characterId和injectedContext字段
      ...options,
    };
  }

  async transform(value: any, metadata: ArgumentMetadata): Promise<any> {
    // 🔍 调试日志：开始验证流程
    // this.logger.debug('🔍 [验证管道] 开始验证流程', {
    //   inputValue: JSON.stringify(value),
    //   inputType: typeof value,
    //   metatype: metadata.metatype?.name,
    //   metadataType: metadata.type,
    // });

    // 如果没有类型信息或不需要验证，直接返回
    if (!metadata.metatype || !this.shouldValidate(metadata.metatype)) {
      // this.logger.debug('⏭️  [验证管道] 跳过验证 - 无需验证的类型', {
      //   metatype: metadata.metatype?.name,
      //   shouldValidate: this.shouldValidate(metadata.metatype || Object),
      // });
      return value;
    }

    try {
      // 🔍 调试日志：转换前的数据
      // this.logger.debug('📝 [验证管道] 转换前数据分析', {
      //   originalValue: JSON.stringify(value),
      //   valueKeys: Object.keys(value || {}),
      //   skipValidationFields: this.options.skipValidationFields,
      //   validateInjectedContext: this.options.validateInjectedContext,
      //   transformOptions: {
      //     enableImplicitConversion: true,
      //     excludeExtraneousValues: this.options.whitelist,
      //     exposeDefaultValues: true,
      //     exposeUnsetFields: false,
      //   }
      // });

      // 0. 处理跳过验证的字段
      const { processedValue, skippedFields } = this.processSkippedFields(value);

      // 1. 转换为类实例
      const object = plainToInstance(metadata.metatype, processedValue, {
        enableImplicitConversion: true, // 强制启用类型转换
        excludeExtraneousValues: this.options.whitelist,
        exposeDefaultValues: true,
        exposeUnsetFields: false,
      });

      // 🔍 调试日志：转换后的数据
      // this.logger.debug('🔄 [验证管道] 转换后数据分析', {
      //   transformedObject: JSON.stringify(object),
      //   objectKeys: Object.keys(object || {}),
      //   objectConstructor: object?.constructor?.name,
      //   isInstanceOf: object instanceof metadata.metatype,
      // });

      // 🔍 调试日志：详细字段对比
      if (value && object) {
        const fieldComparison = {};
        const allKeys = new Set([...Object.keys(value), ...Object.keys(object)]);

        for (const key of allKeys) {
          fieldComparison[key] = {
            original: { value: value[key], type: typeof value[key] },
            transformed: { value: object[key], type: typeof object[key] },
            changed: value[key] !== object[key]
          };
        }

        // this.logger.debug('🔍 [验证管道] 字段转换对比', { fieldComparison });
      }

      // 2. 执行验证
      const validationOptions = {
        skipMissingProperties: this.options.skipMissingProperties,
        whitelist: this.options.whitelist,
        forbidNonWhitelisted: this.options.forbidNonWhitelisted,
        groups: this.options.groups.length > 0 ? this.options.groups : undefined,
        always: this.options.always,
        strictGroups: this.options.strictGroups,
        dismissDefaultMessages: this.options.dismissDefaultMessages,
        stopAtFirstError: this.options.stopAtFirstError,
      };

      // this.logger.debug('✅ [验证管道] 开始class-validator验证', {
      //   validationOptions,
      //   objectToValidate: JSON.stringify(object),
      // });

      const errors = await validate(object, validationOptions);

      // 🔍 调试日志：验证结果
      // this.logger.debug('📊 [验证管道] 验证结果', {
      //   errorCount: errors.length,
      //   hasErrors: errors.length > 0,
      //   errorSummary: errors.map(err => ({
      //     property: err.property,
      //     value: err.value,
      //     constraints: Object.keys(err.constraints || {}),
      //   }))
      // });

      // 3. 处理验证错误
      if (errors.length > 0) {
        this.logger.error('❌ [验证管道] 验证失败', {
          errorCount: errors.length,
          detailedErrors: errors.map(err => ({
            property: err.property,
            value: err.value,
            valueType: typeof err.value,
            constraints: err.constraints,
            children: err.children?.length || 0,
          }))
        });

        this.logValidationErrors(errors, metadata);
        throw this.options.exceptionFactory(errors);
      }

      // 4. 恢复跳过验证的字段
      const resultWithSkippedFields = this.restoreSkippedFields(
        this.options.transform ? object : processedValue,
        skippedFields
      );

      // this.logger.debug('✅ [验证管道] 验证成功', {
      //   returnTransformed: this.options.transform,
      //   skippedFieldsCount: Object.keys(skippedFields).length,
      //   skippedFieldNames: Object.keys(skippedFields),
      //   finalResult: JSON.stringify(resultWithSkippedFields),
      //   resultType: typeof resultWithSkippedFields,
      // });

      return resultWithSkippedFields;

    } catch (error) {
      // 如果是RpcException，直接重新抛出
      if (error instanceof RpcException) {
        this.logger.error('🚨 [验证管道] RpcException重新抛出', {
          error: error.getError(),
        });
        throw error;
      }

      // 其他异常转换为RpcException
      this.logger.error('💥 [验证管道] 未预期异常', {
        error: error.message,
        stack: error.stack,
        metadata: metadata.type,
        metatype: metadata.metatype?.name,
        inputValue: JSON.stringify(value),
      });

      throw new RpcException({
        message: '参数验证异常',
        error: 'VALIDATION_PIPE_ERROR',
        details: error.message,
      });
    }
  }

  /**
   * 检查是否需要验证
   */
  private shouldValidate(metatype: Function): boolean {
    const primitiveTypes: Function[] = [String, Boolean, Number, Array, Object];
    return !primitiveTypes.includes(metatype);
  }

  /**
   * 默认异常工厂
   */
  private defaultExceptionFactory(errors: ValidationError[]): RpcException {
    const formattedErrors = this.formatValidationErrors(errors);
    
    return new RpcException({
      message: '参数验证失败',
      error: 'VALIDATION_FAILED',
      details: {
        errors: formattedErrors,
        count: formattedErrors.length,
      },
    });
  }

  /**
   * 格式化验证错误
   */
  private formatValidationErrors(errors: ValidationError[]): Array<{
    field: string;
    value: any;
    constraints: string[];
  }> {
    const formattedErrors: Array<{
      field: string;
      value: any;
      constraints: string[];
    }> = [];

    const formatError = (error: ValidationError, prefix = ''): void => {
      const fieldPath = prefix ? `${prefix}.${error.property}` : error.property;
      
      if (error.constraints) {
        formattedErrors.push({
          field: fieldPath,
          value: error.value,
          constraints: Object.values(error.constraints),
        });
      }

      // 处理嵌套验证错误
      if (error.children && error.children.length > 0) {
        error.children.forEach(child => {
          formatError(child, fieldPath);
        });
      }
    };

    errors.forEach(error => formatError(error));
    return formattedErrors;
  }

  /**
   * 记录验证错误
   */
  private logValidationErrors(errors: ValidationError[], metadata: ArgumentMetadata): void {
    const formattedErrors = this.formatValidationErrors(errors);
    
    this.logger.warn('微服务参数验证失败', {
      type: metadata.type,
      metatype: metadata.metatype?.name,
      errorCount: formattedErrors.length,
      errors: formattedErrors.map(e => ({
        field: e.field,
        constraints: e.constraints,
      })),
    });
  }

  /**
   * 处理跳过验证的字段
   * 从原始值中移除需要跳过验证的字段，并保存它们以便后续恢复
   */
  private processSkippedFields(value: any): { processedValue: any; skippedFields: Record<string, any> } {
    if (!value || typeof value !== 'object' || !this.options.skipValidationFields?.length) {
      return { processedValue: value, skippedFields: {} };
    }

    const processedValue = { ...value };
    const skippedFields: Record<string, any> = {};

    // 移除需要跳过验证的字段
    for (const fieldName of this.options.skipValidationFields) {
      if (fieldName in processedValue) {
        skippedFields[fieldName] = processedValue[fieldName];

        // 根据validateInjectedContext配置决定是否完全移除字段
        if (fieldName === 'injectedContext' && !this.options.validateInjectedContext) {
          // 保留字段但清空内容，避免验证错误
          processedValue[fieldName] = undefined;
        } else {
          // 完全移除字段
          delete processedValue[fieldName];
        }
      }
    }

    // this.logger.debug('🔧 [验证管道] 处理跳过字段', {
    //   skippedFieldNames: Object.keys(skippedFields),
    //   skippedFieldsCount: Object.keys(skippedFields).length,
    //   validateInjectedContext: this.options.validateInjectedContext,
    // });

    return { processedValue, skippedFields };
  }

  /**
   * 恢复跳过验证的字段
   * 将之前保存的跳过字段重新添加到结果对象中
   */
  private restoreSkippedFields(result: any, skippedFields: Record<string, any>): any {
    if (!result || typeof result !== 'object' || !Object.keys(skippedFields).length) {
      return result;
    }

    const finalResult = { ...result };

    // 恢复跳过的字段
    for (const [fieldName, fieldValue] of Object.entries(skippedFields)) {
      finalResult[fieldName] = fieldValue;
    }

    // this.logger.debug('🔧 [验证管道] 恢复跳过字段', {
    //   restoredFieldNames: Object.keys(skippedFields),
    //   restoredFieldsCount: Object.keys(skippedFields).length,
    // });

    return finalResult;
  }
}

/**
 * 预配置的微服务验证管道实例
 */

/** 标准验证管道 - 适用于大多数场景 */
export const StandardMicroserviceValidationPipe = new MicroserviceValidationPipe({
  transform: true,
  whitelist: true,
  forbidNonWhitelisted: true,
  stopAtFirstError: false,
});

/** 严格验证管道 - 适用于安全敏感场景 */
export const StrictMicroserviceValidationPipe = new MicroserviceValidationPipe({
  transform: true,
  whitelist: true,
  forbidNonWhitelisted: true,
  stopAtFirstError: true,
  skipMissingProperties: false,
});

/** 宽松验证管道 - 适用于向后兼容场景 */
export const LenientMicroserviceValidationPipe = new MicroserviceValidationPipe({
  transform: true,
  whitelist: false,
  forbidNonWhitelisted: false,
  stopAtFirstError: false,
  skipMissingProperties: true,
});

/** 验证injectedContext的管道 - 用于测试injectedContext处理 */
export const ValidateInjectedContextPipe = new MicroserviceValidationPipe({
  transform: true,
  whitelist: true,
  forbidNonWhitelisted: true,
  stopAtFirstError: false,
  validateInjectedContext: true, // 启用injectedContext验证
  skipValidationFields: [], // 不跳过任何字段
});

/** 跳过injectedContext验证的管道 - 默认行为 */
export const SkipInjectedContextPipe = new MicroserviceValidationPipe({
  transform: true,
  whitelist: true,
  forbidNonWhitelisted: true,
  stopAtFirstError: false,
  validateInjectedContext: false, // 不验证injectedContext
  skipValidationFields: ['injectedContext'], // 跳过injectedContext字段
});
